package team.aikero.murmuration.sdk.client

import org.springframework.cloud.openfeign.FeignClient
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestParam
import team.aikero.blade.core.annotation.feign.InnerFeign
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.murmuration.common.enums.SearchDimension
import team.aikero.murmuration.common.req.BatchSearchSimilarityRequest
import team.aikero.murmuration.common.vo.SearchInfo
import team.aikero.murmuration.common.vo.SearchSimilarityVo
import team.aikero.murmuration.sdk.Constants

/**
 * 向量服务
 *
 * <AUTHOR>
 * @since 2025-08-01
 */
@InnerFeign
@FeignClient(
    contextId = "VectorClient",
    name = Constants.SERVICE_NAME,
    path = "${Constants.CONTEXT_PATH}/vector",
    url = "\${domain.nest-api}"
)
interface VectorClient {

    /**
     * 检索图片相似度
     *
     * @param url 图片URL
     * @param dimensions 检索维度(支持传多个)
     * @return 各维度对应的检索信息
     */
    @GetMapping("/search-similarity")
    fun searchSimilarity(
        @RequestParam url: String,
        @RequestParam dimensions: Set<SearchDimension>,
    ): DataResponse<Map<SearchDimension, SearchInfo>>

    /**
     * 批量检索图片相似度
     *
     * @param request 批量检索请求
     * @return 检索结果列表
     */
    @PostMapping("/batch-search-similarity")
    fun batchSearchSimilarity(@RequestBody request: BatchSearchSimilarityRequest): DataResponse<List<SearchSimilarityVo>>
}
