package team.aikero.murmuration.sdk.client

import org.springframework.cloud.openfeign.FeignClient
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestParam
import team.aikero.blade.core.annotation.feign.InnerFeign
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.murmuration.common.req.AddMuseSameStyleImageRequest
import team.aikero.murmuration.sdk.Constants

/**
 * MUSE同款图库服务
 *
 * <AUTHOR>
 * @since 2025-08-01
 */
@InnerFeign
@FeignClient(
    contextId = "MuseSameStyleImageClient",
    name = Constants.SERVICE_NAME,
    path = "${Constants.CONTEXT_PATH}/muse-same-style-image",
    url = "\${domain.nest-api}"
)
interface MuseSameStyleImageClient {

    /**
     * 添加MUSE同款
     *
     * @param request 请求对象
     * @param checkSimilarity 是否进行相似度判定
     */
    @PostMapping("/add")
    fun add(
        @RequestBody request: AddMuseSameStyleImageRequest,
        @RequestParam checkSimilarity: Boolean,
    ): DataResponse<Unit>

    /**
     * 删除MUSE同款
     *
     * @param spuCode SPU款号
     */
    @PostMapping("/delete")
    fun delete(@RequestParam spuCode: String): DataResponse<Unit>
}
