package team.aikero.murmuration.service.node.task.lingvisions

import org.apache.commons.codec.digest.DigestUtils
import org.springframework.stereotype.Component
import team.aikero.murmuration.util.requiredProperty
import team.aikero.murmuration.core.workflow.task.TaskResult
import team.aikero.murmuration.core.workflow.task.TaskResult.InternalErrorType
import java.util.*

/**
 * 灵图客户端
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@Component
@Suppress("SpringJavaInjectionPointsAutowiringInspection")
class LingVisionsClient(val httpClient: LingVisionsHttpClient) {

    /**
     * 相似图裂变进阶版
     *
     * @param request 请求
     * @return 任务ID
     */
    fun reimage(request: ReimageProRequest): String {
        val headers = makeHeaders("/aigc/reimage/pro")
        return httpClient.reimagePro(request, headers)
    }

    /**
     * 查询任务结果
     *
     * @param jobId 任务ID
     * @return 任务结果
     */
    fun getJobResult(jobId: String): TaskResult<List<String>> {
        val headers = makeHeaders("/aigc/job/result")
        val (status, message, urls) = httpClient.getJobResult(GetJobResultRequest(jobId), headers)
        return when (status) {
            Status.WAITING, Status.EXECUTING -> TaskResult.Running
            Status.SUCCEED -> TaskResult.Completed(urls)
            Status.FAILED -> TaskResult.Failed(message)
            Status.CANCELED -> TaskResult.Failed(InternalErrorType.TASK_CANCELED)
        }
    }

    private fun makeHeaders(uri: String): Map<String, String> {
        val (appId, secret) = getCertificate()
        val salt = UUID.randomUUID().toString()
        val timestamp = System.currentTimeMillis() / 1000

        // 签名
        val body = "${appId}${uri}${salt}${timestamp}${secret}"
        val sign = DigestUtils.sha256Hex(body)

        // 组装Header
        return mapOf(
            "timestamp" to "$timestamp",
            "appId" to appId,
            "salt" to salt,
            "sign" to sign,
        )
    }

    private fun getCertificate(): Certificate {
        val appId = requiredProperty<String>("lingvisions.appId")
        val secret = requiredProperty<String>("lingvisions.secret")
        return Certificate(appId, secret)
    }
}

data class Certificate(
    val appId: String,
    val secret: String,
)
