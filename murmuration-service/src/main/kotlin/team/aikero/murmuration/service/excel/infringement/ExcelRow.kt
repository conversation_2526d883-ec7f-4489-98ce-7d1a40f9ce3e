package team.aikero.murmuration.service.excel.infringement

import cn.idev.excel.annotation.ExcelProperty

class ExcelRow {
    @ExcelProperty("商品id")
    var productId: String? = null

    @ExcelProperty("款式代表图")
    var url: String? = null

    @ExcelProperty("品牌名称")
    var brand: String? = null

    @ExcelProperty("商品链接")
    var productLink: String? = null

    @ExcelProperty("类目")
    var category: String? = null

    @ExcelProperty("备注信息")
    var remark: String? = null
}
