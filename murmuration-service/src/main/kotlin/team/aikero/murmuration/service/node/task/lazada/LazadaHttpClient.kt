package team.aikero.murmuration.service.node.task.lazada

import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.service.annotation.GetExchange
import org.springframework.web.service.annotation.PostExchange
import team.aikero.murmuration.infra.client.RestClient

/**
 * Lazada HTTP 客户端接口
 *
 * <AUTHOR>  
 * @since 2025-07-24
 */
@RestClient(
    url = "https://api.lazada.sg/rest",
    connectionTimeout = 15,
    readTimeout = 15
)
interface LazadaHttpClient {

    /**
     * AI换脸
     *
     * @param commonParams 公共参数（包含签名）
     * @param request 请求参数
     * @return API响应
     */
    @PostExchange("/content/ai/changeFace")
    fun changeFace(
        @RequestParam commonParams: Map<String, String>,
        request: ChangeFaceRequest
    ): Response<CreateTaskResult>

    /**
     * AI换背景
     *
     * @param commonParams 公共参数（包含签名）
     * @param request 请求参数  
     * @return API响应
     */
    @PostExchange("/content/ai/changeProductBackground")
    fun changeBackground(
        @RequestParam commonParams: Map<String, String>,
        request: ChangeBackgroundRequest
    ): Response<CreateTaskResult>

    /**
     * 虚拟换衣
     *
     * @param commonParams 公共参数（包含签名）
     * @param request 请求参数
     * @return API响应
     */
    @PostExchange("/content/ai/tryOnCloth")
    fun tryOnCloth(
        @RequestParam commonParams: Map<String, String>,
        request: TryOnClothRequest
    ): Response<CreateTaskResult>

    /**
     * 查询任务状态
     *
     * @param commonParams 公共参数（包含签名）
     * @param taskId 任务ID
     * @return API响应
     */
    @GetExchange("/content/ai/getTaskStatus")
    fun getTaskStatus(
        @RequestParam commonParams: Map<String, String>,
        @RequestParam("task_id") taskId: String
    ): Response<GetTaskResult>
}