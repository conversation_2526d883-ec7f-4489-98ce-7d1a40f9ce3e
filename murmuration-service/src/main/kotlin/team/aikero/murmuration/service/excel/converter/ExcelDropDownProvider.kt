package team.aikero.murmuration.service.excel.converter

import org.springframework.stereotype.Component
import team.aikero.murmuration.service.excel.annotation.DictConvert
import java.io.ByteArrayOutputStream

/**
 * Excel 下拉选项提供者
 * 
 * 注意：由于 FastExcel 的 API 限制，此类主要提供下拉选项信息，
 * 实际的 Excel 下拉功能需要使用 Apache POI 或其他库实现。
 */
@Component
class ExcelDropDownProvider {
    
    /**
     * 获取字段的下拉选项列表
     */
    fun getDropDownOptionsForField(field: java.lang.reflect.Field): List<String> {
        val dictConvert = field.getAnnotation(DictConvert::class.java)
        if (dictConvert != null) {
            val provider = DictValueProviderRegistry.getProvider(dictConvert.dictType)
            if (provider != null) {
                return provider.getDropDownOptions(dictConvert.dictType)
            }
        }
        return emptyList()
    }
    
    /**
     * 为数据类生成下拉选项信息
     */
    fun <T> generateDropDownOptionsInfo(dataClass: Class<T>): Map<String, List<String>> {
        val optionsMap = mutableMapOf<String, List<String>>()
        
        dataClass.declaredFields.forEach { field ->
            val options = getDropDownOptionsForField(field)
            if (options.isNotEmpty()) {
                optionsMap[field.name] = options
            }
        }
        
        return optionsMap
    }
    
    /**
     * 生成下拉选项信息字符串
     */
    fun generateDropDownOptionsString(dataClass: Class<*>): String {
        val optionsInfo = generateDropDownOptionsInfo(dataClass)
        
        if (optionsInfo.isEmpty()) {
            return "该类没有配置下拉选项"
        }
        
        val sb = StringBuilder()
        sb.append("Excel 下拉选项配置：\n")
        sb.append("==================\n")
        
        optionsInfo.forEach { (fieldName, options) ->
            sb.append("字段: $fieldName\n")
            sb.append("选项: ${options.joinToString(", ")}\n")
            sb.append("------------------\n")
        }
        
        return sb.toString()
    }
    
    /**
     * 生成带下拉选项的 Excel 字节数组
     * 注意：此方法目前仅返回空字节数组，实际实现需要使用 Apache POI
     */
    fun generateExcelWithDropDownOptions(
        dataClass: Class<*>,
        data: List<Any>
    ): ByteArray {
        // 由于 FastExcel 限制，无法直接添加数据验证
        // 这里返回下拉选项信息的文本
        val optionsString = generateDropDownOptionsString(dataClass)
        return optionsString.toByteArray()
    }
}