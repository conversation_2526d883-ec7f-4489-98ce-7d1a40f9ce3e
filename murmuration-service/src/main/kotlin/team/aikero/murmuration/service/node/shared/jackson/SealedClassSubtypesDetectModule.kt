package team.aikero.murmuration.service.node.shared.jackson

import com.fasterxml.jackson.databind.jsontype.NamedType
import com.fasterxml.jackson.databind.module.SimpleModule
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.murmuration.service.node.shared.Image
import kotlin.reflect.KClass

/**
 * 密封类子类自动注册模块
 *
 * <AUTHOR>
 */
class SealedClassSubtypesDetectModule : SimpleModule() {

    override fun setupModule(context: SetupContext) {
        super.setupModule(context)

        registerSealedInterfaceSubtypes(context, Image::class)
    }

    /**
     * 注册密封接口的子类型
     */
    private fun registerSealedInterfaceSubtypes(context: SetupContext, sealedInterface: KClass<*>) {
        if (!sealedInterface.isSealed) {
            return
        }

        val subTypes = sealedInterface.sealedSubclasses

        log.info { "registerSealedInterfaceSubtypes: ${sealedInterface.simpleName}: ${subTypes.map { it.simpleName }}" }

        subTypes.forEach { subClass ->
            context.registerSubtypes(NamedType(subClass.java, subClass.simpleName))
        }
    }
}
