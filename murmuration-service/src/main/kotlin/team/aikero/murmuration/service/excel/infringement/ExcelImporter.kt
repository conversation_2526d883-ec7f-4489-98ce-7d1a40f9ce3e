package team.aikero.murmuration.service.excel.infringement

import cn.idev.excel.FastExcel
import com.zaxxer.hikari.HikariDataSource
import org.babyfish.jimmer.sql.kt.KSqlClient
import org.redisson.api.RedissonClient
import org.springframework.stereotype.Component
import org.springframework.transaction.PlatformTransactionManager
import team.aikero.blade.oss.OssTemplate
import team.aikero.blade.util.io.Uris.download
import team.aikero.murmuration.service.component.ImageVectorFetcher
import team.aikero.murmuration.service.excel.ExcelImportLog
import team.aikero.murmuration.service.vector.VectorStoreFactory
import java.net.URI

/**
 * 侵权图库Excel导入器
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Component
class ExcelImporter(
    val sql: KSqlClient,
    val transactionManager: PlatformTransactionManager,
    val imageVectorFetcher: ImageVectorFetcher,
    val vectorStoreFactory: VectorStoreFactory,
    val ossTemplate: OssTemplate,
    val redisson: RedissonClient,
    val dataSource: HikariDataSource,
) {

    fun import(log: ExcelImportLog, infringementType: String) {
        // 创建 Listener
        val listener = ExcelReadListener(
            logId = log.id,
            sql = sql,
            transactionManager = transactionManager,
            imageVectorFetcher = imageVectorFetcher,
            vectorStoreFactory = vectorStoreFactory,
            infringementType = infringementType,
            ossTemplate = ossTemplate,
            redisson = redisson,
            dataSource = dataSource,
        )

        URI(log.url).download().inputStream().use { inputStream ->
            val reader = FastExcel
                .read(inputStream, ExcelRow::class.java, listener)
                .build()
            val sheet = FastExcel.readSheet(0).build()
            reader.use { it.read(sheet) }
        }
    }
}
