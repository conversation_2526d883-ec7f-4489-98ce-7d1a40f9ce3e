package team.aikero.murmuration.service.vector

import org.springframework.stereotype.Component
import team.aikero.murmuration.common.enums.SearchDimension
import team.aikero.murmuration.service.node.task.aip.AipAbility

/**
 * 嵌入模型注册中心
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@Component
class EmbeddingModelRegistry(models: List<EmbeddingModel>) {

    /**
     * AipAbility -> EmbeddingModel
     */
    val modelMap = models.associateBy { it.ability }

    /**
     * SearchDimension -> AipAbility
     */
    val dimensionAbilities = mapOf(
        SearchDimension.MUSE_SAME_STYLE to AipAbility.CLOTH_FEAT_EXTRACT,
        SearchDimension.INFRINGEMENT to AipAbility.CLOTH_FEAT_EXTRACT,
    )

    /**
     * 根据检索维度获取嵌入模型
     */
    fun findByDimension(dimension: SearchDimension): EmbeddingModel {
        val ability = findAbilityByDimension(dimension)
        return modelMap[ability] ?: throw IllegalStateException("根据检索维度[${dimension}]找不到对应的嵌入模型")
    }

    /**
     * 根据检索维度获取算法调度平台能力
     */
    fun findAbilityByDimension(dimension: SearchDimension): AipAbility {
        return dimensionAbilities[dimension] ?: throw IllegalStateException("根据检索维度[${dimension}]找不到对应的算法调度平台能力")
    }
}
