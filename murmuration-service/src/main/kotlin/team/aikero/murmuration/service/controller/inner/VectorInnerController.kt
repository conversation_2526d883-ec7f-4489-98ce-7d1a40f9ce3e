package team.aikero.murmuration.service.controller.inner

import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import team.aikero.blade.auth.annotation.PreCheckIgnore
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.ok
import team.aikero.murmuration.common.enums.SearchDimension
import team.aikero.murmuration.common.req.BatchSearchSimilarityRequest
import team.aikero.murmuration.common.vo.SearchInfo
import team.aikero.murmuration.common.vo.SearchSimilarityVo
import team.aikero.murmuration.service.component.ImageVectorFetcher
import team.aikero.murmuration.service.component.SimilarityLevelMapper
import team.aikero.murmuration.service.vector.VectorStoreFactory
import team.aikero.murmuration.util.async
import team.aikero.murmuration.util.await

/**
 * 向量内部服务
 *
 * <AUTHOR>
 * @since 2025-08-01
 */
@RestController
@RequestMapping("/inner/vector")
class VectorInnerController(
    val vectorStoreFactory: VectorStoreFactory,
    val similarityLevelMapper: SimilarityLevelMapper,
    val imageVectorFetcher: ImageVectorFetcher,
) {

    /**
     * 检索图片相似度
     *
     * @param url 图片URL
     * @param dimensions 检索维度(支持传多个)
     * @return 各维度对应的检索信息
     */
    @PreCheckIgnore
    @GetMapping("/search-similarity")
    fun searchSimilarity(
        @RequestParam url: String,
        @RequestParam dimensions: Set<SearchDimension>,
    ): DataResponse<Map<SearchDimension, SearchInfo>> {
        val searchResults = doSearchSimilarity(url, dimensions)
        return ok(searchResults)
    }

    /**
     * 批量检索图片相似度
     *
     * @param request 批量检索请求
     * @return 文档列表
     */
    @PreCheckIgnore
    @PostMapping("/batch-search-similarity")
    fun batchSearchSimilarity(@RequestBody request: BatchSearchSimilarityRequest): DataResponse<List<SearchSimilarityVo>> {
        val futures = request.urls.map { url ->
            async {
                val dimensions = doSearchSimilarity(url, request.dimensions)
                SearchSimilarityVo(url, dimensions)
            }
        }
        return ok(futures.await())
    }

    /**
     * 检索图片相似度
     */
    private fun doSearchSimilarity(url: String, dimensions: Set<SearchDimension>): Map<SearchDimension, SearchInfo> {
        val results = mutableMapOf<SearchDimension, SearchInfo>()

        for (dimension in dimensions) {
            // 获取向量
            val vector = imageVectorFetcher.fetch(url, dimension)

            // 检索向量库
            val searchStore = vectorStoreFactory.getSearchStore(dimension)
            val similarity = searchStore.top1Similarity(vector)
            val level = similarityLevelMapper.mapSimilarityToLevel(similarity, dimension)
            results[dimension] = SearchInfo(similarity, level)
        }

        return results
    }
}
