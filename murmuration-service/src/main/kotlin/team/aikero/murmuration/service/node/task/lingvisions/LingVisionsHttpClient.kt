package team.aikero.murmuration.service.node.task.lingvisions

import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestHeader
import org.springframework.web.service.annotation.PostExchange
import team.aikero.murmuration.infra.client.RestClient

/**
 * 灵图开放API
 *
 * [docs](https://dwkp4sqddlu.feishu.cn/wiki/McjiwKrLUizBljk5sFTczXHVnIg)
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@RestClient("\${domain.api.lingvisions:}")
interface LingVisionsHttpClient {

    /**
     * 相似图裂变进阶版
     *
     * https://dwkp4sqddlu.feishu.cn/wiki/McjiwKrLUizBljk5sFTczXHVnIg?fromScene=spaceOverview
     */
    @PostExchange("/aigc/reimage/pro")
    fun reimagePro(
        @RequestBody request: ReimageProRequest,
        @RequestHeader headers: Map<String, String>,
    ): String

    /**
     * 查询任务结果
     *
     * https://dwkp4sqddlu.feishu.cn/wiki/McjiwKrLUizBljk5sFTczXHVnIg?fromScene=spaceOverview
     */
    @PostExchange("/aigc/job/result")
    fun getJobResult(
        @RequestBody request: GetJobResultRequest,
        @RequestHeader headers: Map<String, String>,
    ): GetJobResultResponse
}
