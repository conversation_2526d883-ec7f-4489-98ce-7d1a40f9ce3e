package team.aikero.murmuration.service.material.entity

import org.babyfish.jimmer.sql.Entity
import org.babyfish.jimmer.sql.Serialized
import team.aikero.blade.data.jimmer.entity.BaseEntity
import team.aikero.blade.data.jimmer.entity.CreatedTime
import team.aikero.blade.data.jimmer.entity.LongId
import team.aikero.blade.data.jimmer.entity.OrgWithFilter
import team.aikero.blade.data.jimmer.entity.RevisedTime
import team.aikero.blade.data.jimmer.entity.Reviser
import team.aikero.blade.data.jimmer.entity.TenantId

/**
 * 风格管理
 */
@Entity
interface StyleManage: LongId, CreatedTime, RevisedTime, Reviser, OrgWithFilter, TenantId {

    /**
     * 风格编号
     */
    val styleCode: String

    /**
     * 风格名称
     */
    val styleName: String

    /**
     * 应用业务
     */
    val applicationBusiness: ApplicationBusiness

    /**
     * 图片素材
     */
    @Serialized
    val imageMaterial: List<String>
}

/**
 * 风格管理-应用业务
 */
enum class ApplicationBusiness {

    /**
     * POD-印花
     */
    POD_PRINT,

    /**
     * POD-模特图
     */
    POD_MODEL_PICTURE,
}
