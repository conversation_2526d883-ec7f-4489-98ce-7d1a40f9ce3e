package team.aikero.murmuration.service.excel.infringement

import cn.idev.excel.context.AnalysisContext
import cn.idev.excel.read.listener.ReadListener
import com.zaxxer.hikari.HikariDataSource
import org.babyfish.jimmer.sql.ast.mutation.SaveMode
import org.babyfish.jimmer.sql.kt.KSqlClient
import org.redisson.api.RedissonClient
import org.springframework.transaction.PlatformTransactionManager
import org.springframework.transaction.TransactionDefinition
import org.springframework.transaction.support.TransactionTemplate
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.oss.OssTemplate
import team.aikero.blade.oss.OssTemplateExt.transferFrom
import team.aikero.murmuration.common.enums.SearchDimension
import team.aikero.murmuration.service.component.ImageVectorFetcher
import team.aikero.murmuration.service.excel.ExcelRowError
import team.aikero.murmuration.service.vector.Document
import team.aikero.murmuration.service.vector.VectorStoreFactory
import team.aikero.murmuration.service.vector.entity.InfringementImage
import team.aikero.murmuration.util.async
import team.aikero.murmuration.util.checkNotBlank
import team.aikero.murmuration.util.checkValidUrl
import team.aikero.murmuration.util.execute
import java.lang.reflect.UndeclaredThrowableException
import java.net.URI
import java.util.concurrent.Semaphore

/**
 * 侵权图库Excel读取器
 *
 * <AUTHOR>
 * @since 2025-08-06
 */
class ExcelReadListener(
    val logId: Long,
    val sql: KSqlClient,
    val transactionManager: PlatformTransactionManager,
    val imageVectorFetcher: ImageVectorFetcher,
    val vectorStoreFactory: VectorStoreFactory,
    val infringementType: String,
    val ossTemplate: OssTemplate,
    val redisson: RedissonClient,
    val dataSource: HikariDataSource,
): ReadListener<ExcelRow> {

    /**
     * 独立事务模板
     */
    private val transactionTemplate = TransactionTemplate(transactionManager).apply {
        propagationBehavior = TransactionDefinition.PROPAGATION_REQUIRES_NEW
    }

    /**
     * 使用信号量来控制虚拟线程的并发度，防止数据库连接池被耗尽
     */
    private val semaphore = Semaphore(dataSource.maximumPoolSize)

    /**
     * 读取到一行
     */
    override fun invoke(row: ExcelRow, context: AnalysisContext) {
        val rowNum = context.readRowHolder().rowIndex + 1
        log.info { "读取Excel[${logId}]第[${rowNum}]行数据" }

        // 校验行数据
        val validatedImage = validateRow(row)

        val future = async {
            // productId 相同的数据互斥，防止数据库更新冲突
            redisson.execute("IMPORT_INFRINGEMENT_EXCEL:${validatedImage.productId}") {
                // 使用信号量来控制虚拟线程的并发度，防止数据库连接池被耗尽
                semaphore.execute {
                    // 独立事务
                    transactionTemplate.execute {
                        // 插入或更新数据库
                        val savedImage = sql.save(validatedImage).modifiedEntity
                        // 插入或更新向量库
                        upsertVectorStore(savedImage)
                    }
                }
            }
        }

        future.exceptionally {
            handleException(it, context)
        }
    }

    /**
     * 校验行数据
     */
    private fun validateRow(row: ExcelRow): InfringementImage {
        // 商品id
        val productId = checkNotBlank(row.productId) { "商品id不能为空" }

        // 款式图片
        val url = checkNotBlank(row.url) { "款式图片不能为空" }
        checkValidUrl(url) { "款式图片URL格式不正确" }

        // 商品链接
        val productLink = checkNotBlank(row.productLink) { "商品链接不能为空" }
        checkValidUrl(productLink) { "商品链接URL格式不正确" }

        // 品牌名称
        val brand = checkNotBlank(row.brand) { "品牌名称不能为空" }

        // 服装类目
        val category = checkNotBlank(row.category) { "服装类目不能为空" }

        // 外部图片链接转储到内部OSS
        val ossUrl = ossTemplate.transferFrom(URI.create(url))

        return InfringementImage {
            this.productId = productId
            this.url = ossUrl
            this.type = infringementType
            this.brand = brand
            this.category = category
            this.productLink = productLink
            this.remark = row.remark
        }
    }

    /**
     * 插入或更新向量库
     */
    private fun upsertVectorStore(image: InfringementImage) {
        val vectorId = "${image.id}"
        val vector = imageVectorFetcher.fetch(image.url, SearchDimension.INFRINGEMENT)
        val searchStore = vectorStoreFactory.getSearchStore(SearchDimension.INFRINGEMENT)

        // 如果向量库中已经存在，则先删除
        searchStore.deleteById(vectorId)

        val document = Document(id = vectorId, vector = vector)
        searchStore.insert(document)
    }

    override fun doAfterAllAnalysed(context: AnalysisContext) {}

    /**
     * 发生异常时
     */
    override fun onException(ex: Exception, context: AnalysisContext) {
        handleException(ex, context)
    }

    /**
     * 处理异常
     */
    private fun handleException(ex: Throwable, context: AnalysisContext) {
        val actualException = if (ex is UndeclaredThrowableException) ex.cause else ex
        val rowNum = context.readRowHolder().rowIndex + 1
        log.error(ex) { "读取Excel[${logId}]第[${rowNum}]行发生异常: ${actualException?.message}" }

        // 保存异常信息
        sql.save(ExcelRowError {
            this.logId = <EMAIL>
            this.rowNum = rowNum
            this.reason = actualException?.message.toString()
        }, SaveMode.INSERT_ONLY)
    }
}
