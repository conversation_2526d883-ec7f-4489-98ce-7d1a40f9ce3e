package team.aikero.murmuration.service.node.task.youchuan

import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.annotation.JsonValue
import java.time.LocalDateTime

/**
 * 文生图请求
 */
data class DiffusionRequest(
    /**
     * 文本信息，长度[1-8192]
     */
    val text: String,

    /**
     * 任务结果回调通知接口
     */
    val callback: String? = null
)

/**
 * 任务详情，任务的详细信息描述
 */
data class JobInfo(
    /**
     *  图片审核结果；与urls按照数组下标对应，空代表审核通过
     */
    @JsonProperty("audits")
    val audits: List<String>,
    /**
     *  任务状态详细描述
     */
    @JsonProperty("comment")
    val comment: String,
    @JsonProperty("cost")
    val cost: JobCost,
    /**
     * 任务ID
     */
    @JsonProperty("id")
    val id: String,
    /**
     * 种子值
     */
    @JsonProperty("seed")
    val seed: Long,
    @JsonProperty("status")
    val status: JobStatus,
    /**
     * 文本，长度[1-8192]
     */
    @JsonProperty("text")
    val text: String,
    /**
     * 图片URL数组;若单个url为空代表审核未通过,原因参考audits字段的同下标元素
     */
    @JsonProperty("urls")
    val urls: List<String>
)

/**
 * 任务消耗
 */
data class JobCost(
    /**
     * 任务ID
     */
    @JsonProperty("jobId")
    val jobId: String,
    /**
     * 费用消耗值(单位分)
     */
    @JsonProperty("feeCost")
    val feeCost: Double,
    /**
     * fast任务条数消耗值(单位条)
     */
    @JsonProperty("fastCost")
    val fastCost: Double,
    /**
     * relax任务消耗值(单位条)
     */
    @JsonProperty("relaxCost")
    val relaxCost: Double,
    /**
     * 消费时间
     */
    @JsonProperty("costAt")
    val costAt: LocalDateTime
)

/**
 * Moodboard请求
 */
data class MoodboardRequest(
    /**
     * 图片URL数组, 最多30张图片
     */
    val images: List<String>,

    /**
     * 用于标识（非必需，默认同id)
     */
    val tag: String? = null
)

/**
 * Moodboard详情
 */
data class MoodboardInfo(
    /**
     * moodboard id, 可在生图任务中以--p方式引用
     */
    val id: String,

    /**
     * 图片URL数组, 最多30张图片
     */
    val images: List<String>,

    /**
     * 用于标识（非必需)
     */
    val tag: String? = null,

    /**
     * 创建时间（接口文档无注释）
     */
    val createdAt: String,

    /**
     * 更新时间（接口文档无注释）
     */
    val updatedAt: String,
)

/**
 * Moodboard分页请求
 */
data class MoodboardPageRequest(
    /**
     * 页码[1-1000]
     */
    val pageNo: Int,

    /**
     * 每页数据量[1-500]
     */
    val pageSize: Int
)

/**
 * Moodboard分页响应
 */
data class MoodboardPageResponse(
    /**
     * 详情列表
     */
    val moodboards: List<MoodboardInfo>,

    /**
     * 页码
     */
    val pageNo: Int
)

/**
 * 任务状态
 */
enum class JobStatus(@JsonValue val value: Int) {
    /**
     * 新创建
     */
    CREATED(0),

    /**
     * 执行中
     */
    EXECUTION(1),

    /**
     * 执行成功
     */
    SUCCESS(2),

    /**
     * 失败
     */
    FAIL(3);
}
