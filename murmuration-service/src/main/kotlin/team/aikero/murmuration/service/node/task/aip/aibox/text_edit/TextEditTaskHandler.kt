package team.aikero.murmuration.service.node.task.aip.aibox.text_edit

import team.aikero.murmuration.common.req.task.ai_box.TextEditHandlerRequest
import team.aikero.murmuration.core.Ability
import team.aikero.murmuration.core.Supplier
import team.aikero.murmuration.core.annotations.TaskIdentifier
import team.aikero.murmuration.core.workflow.task.TaskHandler
import team.aikero.murmuration.core.workflow.task.TaskHandlerResult
import team.aikero.murmuration.core.workflow.task.TaskResult
import team.aikero.murmuration.service.utils.ImageUtil


@TaskIdentifier(
    supplier = Supplier.AIP,
    ability = Ability.AI_BOX_TEXT_EDIT
)
class TextEditTaskHandler(
    private val executor: TextEditExecutor,
    private val imageUtil: ImageUtil
) : TaskHandler<TextEditHandlerRequest, String> {
    override fun create(request: TextEditHandlerRequest): String {
        return executor.createTask(
            TextEditInput(
                request.image,
                request.userPrompt,
                request.count,
                imageUtil.getAspectRatio(request.image)
            )
        )
    }

    override fun query(
        request: TextEditHandlerRequest,
        context: String
    ): TaskResult<List<TaskHandlerResult>> {
        val taskResult = executor.getTask(context)
        return taskResult.map { result ->
            result.result.map(TaskHandlerResult::image)
        }
    }
}