package team.aikero.murmuration.service.node.task.aip.difyTryOn

import team.aikero.blade.core.toolkit.isNotBlank
import team.aikero.blade.core.toolkit.isNotEmpty
import team.aikero.blade.core.toolkit.isNotNull
import team.aikero.murmuration.common.enums.task.TryOnResultType
import team.aikero.murmuration.common.req.task.ai_box.TryOnHandlerInputRequest
import team.aikero.murmuration.core.Ability
import team.aikero.murmuration.core.Supplier
import team.aikero.murmuration.core.annotations.TaskIdentifier
import team.aikero.murmuration.core.workflow.task.TaskHandler
import team.aikero.murmuration.core.workflow.task.TaskHandlerResult
import team.aikero.murmuration.core.workflow.task.TaskResult
import team.aikero.murmuration.service.component.ImageInfoFetcher
import team.aikero.murmuration.service.node.task.aip.kontextpod.AspectRatio
import java.math.BigDecimal
import java.math.RoundingMode

/**
 * dify try on任务处理器
 */
@TaskIdentifier(
    supplier = Supplier.AIP,
    ability = Ability.TRY_ON
)
class DifyTryOnTaskHandle(
    val executor: DifyTryOnExecutor,
    val imageInfoFetcher: ImageInfoFetcher
): TaskHandler<TryOnHandlerInputRequest, String> {
    override fun create(request: TryOnHandlerInputRequest): String {
        val (height, weight) = imageInfoFetcher.fetch(request.clothImageUrl)
        //比例 = 宽 / 高，找到最接近的比例
        val value = BigDecimal(weight).setScale(3, RoundingMode.HALF_UP) / BigDecimal(height)
        val aspectRatioValue = AspectRatio.similarValue(value.setScale(3, RoundingMode.HALF_UP))
        return executor.createTask(
            DifyTryOnInput(
                modelImageUrl =  if (request.usingMj) request.mjInfo!!.mjModelImageUrl else request.modelImage!!.modelReferenceImageUrl,// controller已判空
                clothImageUrl =  request.clothImageUrl,
                moodboardId =  if (request.usingMj) "--p ${request.mjInfo!!.moodboardId}" else null,// controller已判空
                usingMj = if (request.usingMj) "true" else "false",
                clothLength =  request.clothLength,
                usingChangeBackground = if (request.backgroundImage.isNotNull()) "true" else "false",
                usingkontext = if (request.usingkontext) "true" else "false",
                usingGenerateSwapFace = if (request.targetModelFaceImage.isNotBlank()) "true" else "false",
                targetModelFaceImgUrl = request.targetModelFaceImage,
                aspectRatio =  aspectRatioValue,
                backgroundImageUrl = request.backgroundImage?.path,
                kontextBatchSize = request.kontextBatchSize,
                tryonBatchSize = request.tryonBatchSize,
                poseRange = request.poseRange?.joinToString("\n" ),
            )
        )
    }

    override fun query(request: TryOnHandlerInputRequest, context: String): TaskResult<List<TaskHandlerResult>> {
        val taskResult = executor.getTask(context)
        return taskResult.map { output ->
            val taskHandlerResults = mutableListOf<TaskHandlerResult>()
            // validateTaskOutput已经判空
            taskHandlerResults.addAll(output.tryOnUrls!!.map { TaskHandlerResult.image(it, DifyTryOnAttribute(TryOnResultType.TRY_ON)) })
            if (output.mjUrls.isNotEmpty()) {
                taskHandlerResults.addAll(output.mjUrls!!.map { TaskHandlerResult.image(it, DifyTryOnAttribute(TryOnResultType.MJ)) })
            }
            if (output.changeBackgroundUrls.isNotEmpty()) {
                taskHandlerResults.addAll(output.changeBackgroundUrls!!.map { TaskHandlerResult.image(it, DifyTryOnAttribute(TryOnResultType.CHANGE_BACKGROUND)) })
            }
            if (output.kontextUrls.isNotEmpty()) {
                taskHandlerResults.addAll(output.kontextUrls!!.map { TaskHandlerResult.image(it, DifyTryOnAttribute(TryOnResultType.KONTEXT)) })
            }
            if (output.swapFaceUrls.isNotEmpty()) {
                taskHandlerResults.addAll(output.swapFaceUrls!!.map { TaskHandlerResult.image(it, DifyTryOnAttribute(TryOnResultType.SWAP_FACE)) })
            }
            taskHandlerResults
        }
    }
}