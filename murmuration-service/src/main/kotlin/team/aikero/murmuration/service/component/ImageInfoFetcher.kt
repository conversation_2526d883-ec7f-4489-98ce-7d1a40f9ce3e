package team.aikero.murmuration.service.component

import org.springframework.stereotype.Component
import team.aikero.blade.file.FileInfos.metadata
import team.aikero.blade.file.core.ImageMetadata
import team.aikero.blade.oss.OssTemplate
import team.aikero.blade.oss.OssTemplateExt.download
import java.net.URI

/**
 * 图片信息提取器
 *
 * <AUTHOR>
 * @since 2025-07-05
 */
@Component
class ImageInfoFetcher(val ossTemplate: OssTemplate) {

    /**
     * 获取图片信息
     *
     * @param imageUrl 图片URL
     * @return 图片信息
     */
    fun fetch(imageUrl: String): ImageInfo {
        return try {
            doFetch(imageUrl)
        }
        catch (ex: Exception) {
            throw ImageInfoFetchException(imageUrl, ex)
        }
    }

    private fun doFetch(imageUrl: String): ImageInfo {
        val metadata = ossTemplate.download(URI(imageUrl)).metadata()

        return when(metadata){
            is ImageMetadata -> ImageInfo(metadata.height?.toInt()!!, metadata.width?.toInt()!!)
            else -> throw IllegalArgumentException("不支持的文件类型 ${metadata.mediaType}")
        }
    }
}

/**
 * 图片信息
 */
data class ImageInfo(
    val height: Int,
    val width: Int,
)

class ImageInfoFetchException(imageUrl: String, ex: Exception): Exception("获取图片[${imageUrl}]信息失败", ex)
