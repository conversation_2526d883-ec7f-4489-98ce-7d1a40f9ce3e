package team.aikero.murmuration.service.node.task.aip

import com.fasterxml.jackson.annotation.JsonValue

data class CreateAiTaskRequest<T>(
    val businessId: String,
    val modelName: String,
    val taskId: String,
    val taskType: String,
    val params: T,
)

data class CreateAiTaskData(
    val taskId: String,
    val businessId: String,
)

data class GetAiTaskRequest(
    val businessId: String,
    val taskId: String,
)

data class GetAiTaskData<T>(
    val state: TaskState,
    val output: T?,
    val message: String?,
)

enum class TaskState(@JsonValue val value: Int) {
    /**
     * 待执行
     */
    PENDING(0),

    /**
     * 执行中
     */
    PROCESSING(1),

    /**
     * 成功
     */
    DONE(2),

    /**
     * 失败
     */
    FAILED(3),

    /**
     * 取消
     */
    CANCELED(4),

    /**
     * 超时失败
     */
    TIMEOUT(5);
}

data class Response<T>(
    val successful: <PERSON><PERSON><PERSON>,
    val code: Long,
    val message: String?,
    val data: T?,
)
