package team.aikero.murmuration.service.node.task.aip

import com.fasterxml.jackson.databind.ObjectMapper
import org.springframework.stereotype.Component
import team.aikero.murmuration.core.workflow.task.TaskResult
import kotlin.reflect.KClass

/**
 * 算法调度平台客户端
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@Component
@Suppress("SpringJavaInjectionPointsAutowiringInspection")
class AipClient(
    val httpClient: AipHttpClient,
    val objectMapper: ObjectMapper,
) {
    private val businessId = "murmuration"

    fun <T> createAiTask(
        ability: AipAbility,
        taskId: String,
        params: T,
    ) {
        val response = httpClient.createAiTask(
            CreateAiTaskRequest(
                businessId = businessId,
                modelName = ability.modelName,
                taskId = taskId,
                taskType = ability.taskType,
                params = params,
            )
        )

        requireSuccessful(response)
    }

    fun <T : Any> getAiTask(taskId: String, outputType: KClass<T>): TaskResult<T> {
        val response = httpClient.getAiTask(
            GetAiTaskRequest(
                businessId = businessId,
                taskId = taskId,
            )
        )

        val data = requireSuccessful(response)

        return when (data.state) {
            TaskState.PENDING, TaskState.PROCESSING -> TaskResult.Running
            TaskState.DONE -> {
                val output = data.output ?: throw NullPointerException("HTTP 响应体 data.output 为空")
                val deserialized = objectMapper.treeToValue(output, outputType.java)
                TaskResult.Completed(deserialized)
            }
            else -> TaskResult.Failed(data.message)
        }
    }

    private fun <T> requireSuccessful(response: Response<T>): T {
        if (!response.successful) {
            throw IllegalStateException("HTTP 响应体 successful 为 false: code=${response.code}, message=${response.message}")
        }

        if (response.data == null) {
            throw NullPointerException("HTTP 响应体 data 为空")
        }

        return response.data
    }
}
