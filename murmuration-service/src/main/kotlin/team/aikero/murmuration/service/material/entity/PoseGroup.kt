package team.aikero.murmuration.service.material.entity

import org.babyfish.jimmer.Formula
import org.babyfish.jimmer.sql.Default
import org.babyfish.jimmer.sql.Entity
import org.babyfish.jimmer.sql.OneToMany
import org.babyfish.jimmer.sql.Table
import team.aikero.blade.data.jimmer.entity.CreatedTime
import team.aikero.blade.data.jimmer.entity.LongId
import team.aikero.blade.data.jimmer.entity.OrgWithFilter
import team.aikero.blade.data.jimmer.entity.RevisedTime
import team.aikero.blade.data.jimmer.entity.Reviser
import team.aikero.blade.data.jimmer.entity.TenantId
import kotlin.collections.map


@Entity
@Table(
    name = "pose_group"
)
interface PoseGroup : LongId, CreatedTime, RevisedTime, Reviser, OrgWithFilter, TenantId {

    /**
     * 姿势组名称
     */
    val poseGroupName: String

    /**
     * 应用场景
     */
    val applicationScenarios: String

    /**
     * 状态 启停
     */
    @Default(value = "true")
    val asEnabled: Boolean

    @OneToMany(mappedBy = "poseGroup")
    val poses: List<Pose>
}
