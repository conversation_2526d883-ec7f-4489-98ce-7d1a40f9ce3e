package team.aikero.murmuration.service.node.task.chaoji

import team.aikero.blade.oss.OssTemplate
import team.aikero.blade.oss.OssTemplateExt.transferFrom
import team.aikero.murmuration.common.req.task.ChaoJiTryOnRequest
import team.aikero.murmuration.core.Ability
import team.aikero.murmuration.core.Supplier
import team.aikero.murmuration.core.annotations.TaskIdentifier
import team.aikero.murmuration.core.workflow.task.TaskHandler
import team.aikero.murmuration.core.workflow.task.TaskHandlerResult
import team.aikero.murmuration.core.workflow.task.TaskResult
import java.net.URI

/**
 * 潮际虚拟换衣任务处理器
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@TaskIdentifier(
    supplier = Supplier.CHAO_JI,
    ability = Ability.TRY_ON,
)
class ChaoJiTryOnTaskHandler(
    val client: ChaoJiClient,
    val ossTemplate: OssTemplate,
): TaskHandler<ChaoJiTryOnRequest, Long> {
    override fun create(request: ChaoJiTryOnRequest): Long {
        return client.tryOn(
            clothingImageUrl = request.clothingImageUrl,
            clothingType = request.clothingImageType,
            modelImageUrl = request.modelImageUrl,
            batchSize = request.n,
        )
    }

    override fun query(request: ChaoJiTryOnRequest, context: Long): TaskResult<List<TaskHandlerResult>> {
        val taskResult = client.getTaskResult(context)
        return taskResult.map { urls ->
            // 将外部图片转存到内部OSS
            val uris = urls.map(URI::create)
            val transferred = ossTemplate.transferFrom(uris)
            transferred.map(TaskHandlerResult::image)
        }
    }
}
