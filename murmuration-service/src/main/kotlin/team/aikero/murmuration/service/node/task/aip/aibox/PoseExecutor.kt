package team.aikero.murmuration.service.node.task.aip.aibox

import com.fasterxml.jackson.annotation.JsonProperty
import team.aikero.murmuration.service.node.task.aip.AipAbility
import team.aikero.murmuration.service.node.task.aip.AipClient
import team.aikero.murmuration.service.node.task.aip.AipExecutor
import team.aikero.murmuration.service.node.task.aip.AipExecutorIdentifier


@AipExecutorIdentifier(AipAbility.IMAGE_POSE_EXTRACT)
class PoseExecutor(
    client: AipClient
) : AipExecutor<PoseExtractionPromptInput, PoseExtractionPromptOutput>(client)


data class PoseExtractionPromptInput(
    @field:JsonProperty(value = "image_url")
    val imageUrl: String
)

data class PoseExtractionPromptOutput(
    @field:JsonProperty(value = "posture_prompt")
    val prompt: String
)