package team.aikero.murmuration.service.node.task.lazada

import team.aikero.blade.oss.OssTemplate
import team.aikero.blade.oss.OssTemplateExt.transferFrom
import team.aikero.murmuration.common.req.task.LazadaChangeBackgroundRequest
import team.aikero.murmuration.core.Ability
import team.aikero.murmuration.core.Supplier
import team.aikero.murmuration.core.annotations.TaskIdentifier
import team.aikero.murmuration.core.workflow.task.TaskHandler
import team.aikero.murmuration.core.workflow.task.TaskHandlerResult
import team.aikero.murmuration.core.workflow.task.TaskResult
import java.net.URI

/**
 * Lazada 换背景任务处理器
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@TaskIdentifier(
    supplier = Supplier.LAZADA,
    ability = Ability.CHANGE_BACKGROUND,
)
class LazadaChangeBackgroundTaskHandler(
    val client: LazadaClient,
    val ossTemplate: OssTemplate,
): TaskHandler<LazadaChangeBackgroundRequest, String> {

    override fun create(request: LazadaChangeBackgroundRequest): String {
        return client.changeBackground(
            ChangeBackgroundRequest(
                productImageUrl = request.imageUrl,
                backgroundCode = request.backgroundCode,
                batchSize = 1,
            )
        )
    }

    override fun query(request: LazadaChangeBackgroundRequest, context: String): TaskResult<List<TaskHandlerResult>> {
        val taskStatus = client.getTaskStatus(context)
        return taskStatus.map { urls ->
            if (urls.size != 1) {
                throw IllegalStateException("Lazada换背景任务结果数量异常: ${urls.size}")
            }

            // 将外部图片转存到内部OSS
            val uris = urls.map(URI::create)
            val transferred = ossTemplate.transferFrom(uris)
            transferred.map(TaskHandlerResult::image)
        }
    }
}
