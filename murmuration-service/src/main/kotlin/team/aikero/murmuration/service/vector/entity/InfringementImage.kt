package team.aikero.murmuration.service.vector.entity

import org.babyfish.jimmer.sql.Entity
import org.babyfish.jimmer.sql.Key

/**
 * 侵权图库
 *
 * <AUTHOR>
 * @since 2025-08-01
 */
@Entity
interface InfringementImage: Image {

    /**
     * 商品ID
     */
    @Key(group = "uniq_product_id")
    val productId: String

    /**
     * 侵权类型
     *
     * NEST字典: INFRINGEMENT_TYPE
     */
    val type: String

    /**
     * 品牌名称
     */
    val brand: String

    /**
     * 单品链接
     */
    val productLink: String

    /**
     * 服装类目
     */
    val category: String

    /**
     * 备注信息
     */
    val remark: String?
}
