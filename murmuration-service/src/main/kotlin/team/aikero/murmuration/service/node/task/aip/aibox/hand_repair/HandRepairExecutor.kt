package team.aikero.murmuration.service.node.task.aip.aibox.hand_repair

import team.aikero.murmuration.service.node.task.aip.*


@AipExecutorIdentifier(AipAbility.HAND_REPAIR)
class HandRepairExecutor(
    client: AipClient
) : AipExecutor<HandRepairInput, HandRepairOutput>(client) {

    override fun validateTaskOutput(output: HandRepairOutput) {
        if (output.resImgs.isEmpty()) {
            throw InvalidTaskOutputException("resImgs 数组为空")
        }
    }

}

data class HandRepairInput(
    val inputImage: List<String>
)

data class HandRepairOutput(
    val resImgs: List<String>
)