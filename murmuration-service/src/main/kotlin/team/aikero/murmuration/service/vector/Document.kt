package team.aikero.murmuration.service.vector

import com.aliyun.dashvector.models.Doc
import com.aliyun.dashvector.models.Vector

/**
 * 向量文档
 */
open class Document(
    val id: String,
    val vector: List<Float>,
    val fields: Map<String, Any?> = emptyMap(),
) {
    constructor(doc: Doc): this(
        id = doc.id,
        vector = doc.vector.toList(),
        fields = doc.fields,
    )

    fun toDoc(extraFields: Map<String, Any?>): Doc {
        val vector = newVector(vector)
        val allFields = fields + extraFields
        return Doc.builder()
            .id(id)
            .vector(vector)
            .fields(allFields)
            .build()
    }
}

/**
 * 带分数的向量文档
 */
class ScoredDocument(
    id: String,
    vector: List<Float>,
    score: Float,
): Document(id, vector) {
    /**
     * 余弦相似度
     */
    val similarity = (2 - score) / 2

    constructor(doc: Doc): this(
        id = doc.id,
        vector = doc.vector.toList(),
        score = doc.score,
    )
}

fun newVector(vector: List<Float>): Vector {
    return Vector.builder().value(vector).build()
}

fun Vector.toList(): List<Float> {
    return value.map { it as Float }
}
