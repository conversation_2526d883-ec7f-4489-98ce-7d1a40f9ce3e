package team.aikero.murmuration.service.controller.web

import org.babyfish.jimmer.sql.ast.mutation.SaveMode
import org.babyfish.jimmer.sql.kt.KSqlClient
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import team.aikero.blade.core.protocol.ComplexPageParam
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.PageVo
import team.aikero.blade.core.protocol.ok
import team.aikero.blade.data.jimmer.findPage
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.murmuration.service.excel.ExcelImportLog
import team.aikero.murmuration.service.excel.ExcelImportScene
import team.aikero.murmuration.service.excel.ExcelImportStatus
import team.aikero.murmuration.service.excel.infringement.ExcelImporter
import team.aikero.murmuration.service.repository.InfringementImageRepository
import team.aikero.murmuration.service.vector.entity.InfringementImage
import team.aikero.murmuration.service.vector.entity.brand
import team.aikero.murmuration.service.vector.entity.category
import team.aikero.murmuration.service.vector.entity.dto.InfringementImagePageRequest
import team.aikero.murmuration.service.vector.entity.dto.InfringementImagePageVo
import team.aikero.murmuration.util.async

/**
 * 侵权图库服务
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@RestController
@RequestMapping("/web/infringement-image")
class InfringementImageWebController(
    val sql: KSqlClient,
    val excelImporter: ExcelImporter,
    val infringementImageRepository: InfringementImageRepository,
) {

    /**
     * 获取品牌列表
     */
    @GetMapping("/brands")
    fun brands(): DataResponse<List<String>> {
        val brands = sql
            .createQuery(InfringementImage::class) {
                select(table.brand)
            }
            .distinct()
            .execute()
        return ok(brands)
    }

    /**
     * 获取品类列表
     */
    @GetMapping("/categories")
    fun categories(): DataResponse<List<String>> {
        val categories = sql
            .createQuery(InfringementImage::class) {
                select(table.category)
            }
            .distinct()
            .execute()
        return ok(categories)
    }

    /**
     * 分页查询
     */
    @PostMapping("/page")
    fun page(@RequestBody request: ComplexPageParam<InfringementImagePageRequest>): DataResponse<PageVo<InfringementImagePageVo>> {
        val page = sql.findPage(request, InfringementImagePageVo::class)
        return ok(page)
    }

    /**
     * 启用
     *
     * @param id 图片ID
     */
    @PostMapping("/enable")
    fun enable(@RequestParam id: Long): DataResponse<Unit> {
        infringementImageRepository.updateEnabled(id, true)
        return ok()
    }

    /**
     * 停用
     *
     * @param id 图片ID
     */
    @PostMapping("/disable")
    fun disable(@RequestParam id: Long): DataResponse<Unit> {
        infringementImageRepository.updateEnabled(id, false)
        return ok()
    }

    /**
     * 删除
     *
     * @param id 图片ID
     */
    @PostMapping("/delete")
    fun delete(@RequestParam id: Long): DataResponse<Unit> {
        infringementImageRepository.delete(id)
        return ok()
    }

    /**
     * 批量删除
     *
     * @param ids 图片ID列表
     */
    @PostMapping("/deleteBatch")
    fun deleteBatch(@RequestBody ids: List<Long>): DataResponse<Unit> {
        ids.forEach(infringementImageRepository::delete)
        return ok()
    }

    data class ImportRequest(
        /**
         * 导入文件URL
         */
        val url: String,

        /**
         * 侵权类型
         *
         * NEST字典: INFRINGEMENT_TYPE
         */
        val type: String,
    )

    /**
     * 导入
     */
    @PostMapping("/import")
    fun import(@RequestBody request: ImportRequest): DataResponse<Unit> {
        // 保存导入日志
        val importLog = sql.save(ExcelImportLog {
            this.url = request.url
            this.scene = ExcelImportScene.INFRINGEMENT_IMAGE
            this.status = ExcelImportStatus.PROCESSING
        }, SaveMode.INSERT_ONLY).modifiedEntity
        val logId = importLog.id
        log.info { "导入Excel[${logId}]开始" }

        async {
            val finalStatus = try {
                // 导入Excel
                excelImporter.import(importLog, request.type)
                log.info { "导入Excel[${logId}]成功" }
                ExcelImportStatus.DONE
            } catch (ex: Throwable) {
                log.error(ex) { "导入Excel[${logId}]发生异常: ${ex.message}" }
                ExcelImportStatus.FAILED
            }

            // 更新最终状态
            sql.save(ExcelImportLog {
                this.id = logId
                this.status = finalStatus
            }, SaveMode.UPDATE_ONLY)
        }

        return ok()
    }
}
