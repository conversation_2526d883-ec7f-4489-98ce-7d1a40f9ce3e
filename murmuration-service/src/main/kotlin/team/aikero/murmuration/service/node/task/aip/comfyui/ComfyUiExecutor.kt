package team.aikero.murmuration.service.node.task.aip.comfyui

import com.fasterxml.jackson.annotation.JsonProperty
import team.aikero.murmuration.service.node.task.aip.AipAbility
import team.aikero.murmuration.service.node.task.aip.AipClient
import team.aikero.murmuration.service.node.task.aip.AipExecutor
import team.aikero.murmuration.service.node.task.aip.AipExecutorIdentifier
import team.aikero.murmuration.service.node.task.aip.InvalidTaskOutputException

@AipExecutorIdentifier(AipAbility.COMFY_UI)
class ComfyUiExecutor(client: AipClient): AipExecutor<ComfyUiInput, ComfyUiOutput>(client) {

    override fun validateTaskOutput(output: ComfyUiOutput) {
        if (output.resImgs.isEmpty()) {
            throw InvalidTaskOutputException("resImgs 数组为空")
        }
    }
}

data class ComfyUiInput(
    /**
     * 上传图片，图片尺寸不宜超过1536*1536
     */
    val image: String,

    /**
     * 定义每次任务生成图片的数量
     */
    @JsonProperty("batch_size")
    val batchSize: Int,

    /**
     * 工作流名称
     */
    val workflowType: String,
)

data class ComfyUiOutput(
    val resImgs: List<String>,
)
