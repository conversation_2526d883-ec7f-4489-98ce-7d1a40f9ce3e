package team.aikero.murmuration.service.node.task.aigc_image

import org.springframework.cloud.openfeign.EnableFeignClients
import org.springframework.stereotype.Component
import team.aikero.blade.auth.util.UserContextUtil
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.sequence.id.IdHelper
import team.aikero.murmuration.core.workflow.task.TaskResult
import tech.tiangong.fashion.aigc.image.client.TextureTaskOpenClient
import tech.tiangong.fashion.aigc.image.common.vo.req.TaskBatchCreateReq
import tech.tiangong.fashion.aigc.image.common.vo.req.TextureImageAddReq
import java.math.BigDecimal

/**
 * aigc-image 客户端
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Component
@EnableFeignClients(basePackages = ["tech.tiangong.fashion.aigc.image.client"])
class AigcImageClient(val feignClient: TextureTaskOpenClient) {

    /**
     * 创建贴图任务
     *
     * @param foregroundImageUrl 前景图URL
     * @param backgroundImageUrl 背景图URL
     * @param maskImageUrl 蒙版图URL
     * @return 任务ID
     */
    fun createTextureTask(
        foregroundImageUrl: String,
        backgroundImageUrl: String,
        maskImageUrl: String,
        rotationAngle: BigDecimal? = null,
    ): Long {
        val taskId = IdHelper.getId()
        val request = TextureImageAddReq(
            taskId = taskId,
            marketingMask = maskImageUrl,
            fgUrl = foregroundImageUrl,
            bgUrl = backgroundImageUrl,
            angle = rotationAngle,
        ).apply {
            this.textureType = "10"
        }

        val systemUser = UserContextUtil.SYSTEM_USER
        val batchRequest = TaskBatchCreateReq(
            reqSource = "murmuration",
            creatorId = systemUser.id,
            creatorName = systemUser.name,
            companyId = systemUser.tenantId,
            data = listOf(request)
        )

        val response = feignClient.batchCreate(batchRequest)
        val tasks = requireSuccessful(response)

        return tasks.firstOrNull()?.textureTaskId ?: throw IllegalStateException("创建贴图任务返回数据异常")
    }

    /**
     * 查询贴图任务结果
     *
     * @param taskId 任务ID
     * @return 任务结果
     */
    fun getTextureTaskResult(taskId: Long): TaskResult<String> {
        val response = feignClient.listByIds(listOf(taskId))
        val results = requireSuccessful(response)
        val firstResult = results.firstOrNull() ?: throw IllegalStateException("查询贴图任务结果返回数据异常")

        val taskStatus = TaskStatus.fromValue(firstResult.taskStatus)
        return when (taskStatus) {
            TaskStatus.COMPLETED, TaskStatus.CALLBACK -> TaskResult.Completed(firstResult.outputUrl)
            TaskStatus.FAILED, TaskStatus.CANCELED -> TaskResult.Failed("查询贴图任务结果状态异常: $taskStatus")
            else -> TaskResult.Running
        }
    }

    private fun <T> requireSuccessful(response: DataResponse<T>): T {
        if (!response.successful) {
            throw IllegalStateException("HTTP 响应体 successful 为 false: code=${response.code}, message=${response.message}")
        }

        return response.data ?: throw NullPointerException("HTTP 响应体 data 为空")
    }
}
