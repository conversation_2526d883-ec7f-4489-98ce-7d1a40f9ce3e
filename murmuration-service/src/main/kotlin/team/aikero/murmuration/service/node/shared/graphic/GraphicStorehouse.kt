package team.aikero.murmuration.service.node.shared.graphic

import org.babyfish.jimmer.sql.*
import team.aikero.blade.data.jimmer.SnowflakeIdGenerator
import team.aikero.blade.data.jimmer.entity.CreatedTime
import team.aikero.blade.data.jimmer.entity.Creator
import team.aikero.blade.data.jimmer.entity.TenantId

/**
 * 图案库
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
@Entity
interface GraphicStorehouse: Creator, CreatedTime, TenantId {

    /**
     * 主键
     */
    @Id
    @GeneratedValue(generatorType = SnowflakeIdGenerator::class)
    val imageId: Long

    /**
     * 原图ID
     */
    val referId: Long?

    /**
     * 来源图ID
     */
    val originImage: Long?

    /**
     * 来源类型
     */
    val originType: GraphicStorehouseType

    /**
     * 图片地址
     */
    val imageUrl: String

    /**
     * 图片类型
     */
    val imageType: GraphicStorehouseType

    /**
     * 图片格式
     */
    val imageFormat: String

    /**
     * 是否选中
     */
    val selected: Boolean?
}

/**
 * 图片类型
 */
@EnumType
enum class GraphicStorehouseType(
    val code: String,
    val value: String
) {
    @EnumItem(name = "1000")
    ORIGINAL_IMAGE("1000", "原图"),
    @EnumItem(name = "2000")
    RESULT_IMAGE("2000", "AI衍生图"),
    @EnumItem(name = "3000")
    UPLOAD_IMAGE("3000", "用户上传图"),
    @EnumItem(name = "4000")
    TRANSPARENT_IMAGE("4000", "透明图"),
    @EnumItem(name = "4100")
    TRANSPARENT_SEG_IMAGE("4100", "透明分割图"),
    @EnumItem(name = "4200")
    ENLARGE_IMAGE("4200", "无损放大2K图"),
    @EnumItem(name = "5000")
    LOGO_IDENTIFY_IMAGE("5000", "图案提取图"),
    @EnumItem(name = "6000")
    FLORAL_PRINT_EXTRACTION("6000", "花型提取图"),
    @EnumItem(name = "7000")
    EXTERNAL_IMPORT("7000", "外部导入图"),
}
