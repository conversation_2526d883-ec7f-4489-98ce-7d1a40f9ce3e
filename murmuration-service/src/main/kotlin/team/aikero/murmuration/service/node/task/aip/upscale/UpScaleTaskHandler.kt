package team.aikero.murmuration.service.node.task.aip.upscale

import team.aikero.murmuration.common.req.task.UpScaleRequest
import team.aikero.murmuration.core.Ability
import team.aikero.murmuration.core.Supplier
import team.aikero.murmuration.core.annotations.TaskIdentifier
import team.aikero.murmuration.core.workflow.task.TaskHandler
import team.aikero.murmuration.core.workflow.task.TaskHandlerResult
import team.aikero.murmuration.core.workflow.task.TaskImmediatelyDoneException
import team.aikero.murmuration.core.workflow.task.TaskResult
import team.aikero.murmuration.service.component.ImageInfoFetcher

/**
 * 超分任务处理器
 *
 * <AUTHOR>
 * @since 2025-07-05
 */
@TaskIdentifier(
    supplier = Supplier.REALESRGAN,
    ability = Ability.UPSCALE,
)
class UpScaleTaskHandler(
    val executor: UpScaleExecutor,
    val imageInfoFetcher: ImageInfoFetcher,
): TaskHandler<UpScaleRequest, String> {

    // 放大倍率 = 向上取整(目标分辨率 / 最长边)
    // 举例：
    // - 目标分辨率 = 2K = 2048
    // - 最长边 = maxOf(1030, 841) = 1030
    // - 放大倍率 = ceil(2048 / 1030) = 2
    override fun create(request: UpScaleRequest): String {
        // 获取图片尺寸信息
        val (imageUrl, targetResolution) = request
        val (height, width) = imageInfoFetcher.fetch(imageUrl)

        // 计算放大倍率
        val maxEdge = maxOf(height, width)
        check(maxEdge > 0) { "图片尺寸异常" }

        // 如果最长边已经大于等于分辨率要求，则无需超分，直接返回结果
        if (maxEdge >= targetResolution.value) {
            val results = listOf(TaskHandlerResult.image(imageUrl))
            throw TaskImmediatelyDoneException(results)
        }

        // ceil(a / b) 等价于 (a + b - 1) / b
        val scaleFactor = (targetResolution.value + maxEdge - 1) / maxEdge

        return executor.createTask(UpScaleInput(
            inputImage = imageUrl,
            scalefactor = scaleFactor,
        ))
    }

    override fun query(request: UpScaleRequest, context: String): TaskResult<List<TaskHandlerResult>> {
        val taskId = context
        val taskResult = executor.getTask(taskId)
        return taskResult.map {
            val resultImageUrl = it.resImgs.first()
            listOf(TaskHandlerResult.image(resultImageUrl))
        }
    }
}
