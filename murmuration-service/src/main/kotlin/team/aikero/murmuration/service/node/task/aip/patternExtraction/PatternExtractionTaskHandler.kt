package team.aikero.murmuration.service.node.task.aip.patternExtraction

import team.aikero.murmuration.common.req.task.PatternExtractionRequest
import team.aikero.murmuration.core.Ability
import team.aikero.murmuration.core.Supplier
import team.aikero.murmuration.core.annotations.TaskIdentifier
import team.aikero.murmuration.core.workflow.task.TaskHandler
import team.aikero.murmuration.core.workflow.task.TaskHandlerResult
import team.aikero.murmuration.core.workflow.task.TaskResult
import java.util.*

/**
 * 图案提取任务处理器
 *
 * <AUTHOR>
 */
@TaskIdentifier(supplier = Supplier.AIP, ability = Ability.PATTERN_EXTRACTION)
class PatternExtractionTaskHandler(
    val executor: PatternExtractionExecutor
): TaskHandler<PatternExtractionRequest, String> {

    override fun create(request: PatternExtractionRequest): String {
        check(request.n > 0) { "生成数量必须大于0" }
        return executor.createTask(
            PatternExtractionInput(
                refImgUrl = request.imageUrl,
                count = request.n,
                taskId = UUID.randomUUID().toString(),
            )
        )
    }

    override fun query(request: PatternExtractionRequest, context: String): TaskResult<List<TaskHandlerResult>> {
        val taskResult = executor.getTask(context)

        return taskResult.map { it.resImgs.map(TaskHandlerResult::image) }
    }
}
