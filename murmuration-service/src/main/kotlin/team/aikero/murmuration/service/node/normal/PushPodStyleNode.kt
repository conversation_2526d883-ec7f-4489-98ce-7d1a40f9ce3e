package team.aikero.murmuration.service.node.normal

import team.aikero.murmuration.core.Ability
import team.aikero.murmuration.core.Supplier
import team.aikero.murmuration.core.annotations.NodeIdentifier
import team.aikero.murmuration.core.annotations.NodeProperties
import team.aikero.murmuration.core.workflow.NodeType
import team.aikero.murmuration.core.workflow.WorkflowNode
import team.aikero.murmuration.core.workflow.context.WorkflowNodeContext
import team.aikero.murmuration.core.workflow.node.NodeResult
import team.aikero.murmuration.core.workflow.node.param.Input
import team.aikero.murmuration.core.workflow.node.param.Output
import team.aikero.murmuration.core.workflow.node.param.Parameter
import team.aikero.murmuration.service.node.shared.StyleImage
import tech.tiangong.digital.print.client.PodStyleClient
import tech.tiangong.digital.print.common.req.CreatePodStyleRequest

/**
 * 推送POD选款节点
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@NodeIdentifier(name = "推送POD选款", supplier = Supplier.MURMURATION, ability = Ability.PUSH_POD_STYLE)
class PushPodStyleNode(val feignClient: PodStyleClient): WorkflowNode<PushPodStyleInput, Parameter.Empty, Output.Empty> {
    override val type = NodeType.NORMAL

    override fun execute(context: WorkflowNodeContext, input: PushPodStyleInput, parameter: Parameter.Empty): NodeResult<Output.Empty> {
        // 构建批量请求
        val requests = input.images.map {
            CreatePodStyleRequest(
                resultImageUrl = it.imageUrl,
                imageId = it.graphicImage.imageId,
                templateDetailId = it.templateImage.templateDetailId,
            )
        }

        // 批量推送
        val response = feignClient.createBatch(requests)
        if (!response.successful) {
            return NodeResult.Failed(response.message)
        }

        return NodeResult.Completed(Output.Empty)
    }
}

data class PushPodStyleInput(
    @NodeProperties(name = "款式图")
    val images: List<StyleImage>,
): Input
