package team.aikero.murmuration.service.component

import org.springframework.stereotype.Component
import team.aikero.admin.common.req.DictCodeReq
import team.aikero.admin.common.vo.DictVo
import team.aikero.admin.sdk.client.DictClient
import team.aikero.blade.core.protocol.andData

/**
 * 字典服务
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Component
class DictService(val dictClient: DictClient) {

    /**
     * 校验字典编码
     *
     * @param parentDictCode 父字典编码
     * @param dictCode 字典编码
     */
    fun checkDictCode(parentDictCode: String, dictCode: String) {
        val dictItems = listByDictCode(parentDictCode)
        dictItems.find { it.dictCode == dictCode } ?: throw IllegalArgumentException("字典编码[$dictCode]不存在")
    }

    /**
     * 根据字典编码查询字典项
     *
     * @param parentDictCode 父字典编码
     * @return 字典项列表
     */
    fun listByDictCode(parentDictCode: String): List<DictVo> {
        val request = DictCodeReq(
            dictCode = parentDictCode,
            category = parentDictCode,
            consumerCode = "MURMURATION",
        )
        val dict = dictClient.tree(request).andData()
        return dict.children!!.filter { it.state == 1 }
    }

    /**
     * 根据字典名称映射字典编码
     *
     * @param parentDictCode 父字典编码
     * @return 字典名称到字典编码的映射
     */
    fun mapNameToCode(parentDictCode: String): Map<String, String> {
        val dictItems = listByDictCode(parentDictCode)
        return dictItems.associateBy({ it.dictName }, { it.dictCode })
    }
}
