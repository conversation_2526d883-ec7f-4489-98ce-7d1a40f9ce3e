package team.aikero.murmuration.service.node.task.aip.kontextpod

import com.fasterxml.jackson.annotation.JsonProperty
import team.aikero.murmuration.service.node.task.aip.*


@AipExecutorIdentifier(AipAbility.COMFY_UI_ONLINE_CPU)
class FluxKontextExecutor(client: AipClient): AipExecutor<FluxKontextInput, FluxKontextOutput>(client) {

    override fun validateTaskOutput(output: FluxKontextOutput) {
        if (output.resImgs.isEmpty()) {
            throw InvalidTaskOutputException("resImgs 数组为空")
        }
    }
}

/**
 * 姿势裂变任务请求
 */
data class FluxKontextInput(
    /**
     * 参考图url
     */
    var images: String,

    /**
     * 提示词
     */
    var prompt: String,

    /**
     * 比例
     * @see AspectRatio
     */
    var aspectRatio: String,

    /**
     * 放大倍数
     */
    @JsonProperty(value = "upscale_by")
    var upscaleBy: String = "2",

    /**
     * 工作流类型
     */
    var workflowType: String,
)

/**
 * 姿势裂变任务输出
 */
data class FluxKontextOutput(
    /**
     * 结果图，只有当进度是100的时候才会有
     */
    var resImgs: List<String>
)
