package team.aikero.murmuration.service.vector

import com.github.benmanes.caffeine.cache.Cache
import com.github.benmanes.caffeine.cache.Caffeine
import org.apache.commons.codec.digest.DigestUtils
import java.time.Duration

private val URL_HASH_CACHE: Cache<String, String> = Caffeine.newBuilder()
    .maximumSize(500)
    .expireAfterAccess(Duration.ofMinutes(10))
    .build()

fun urlHash(url: String): String = URL_HASH_CACHE.get(url) {
    DigestUtils.md5Hex(url)
}
