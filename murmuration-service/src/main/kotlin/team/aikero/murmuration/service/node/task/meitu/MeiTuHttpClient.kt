package team.aikero.murmuration.service.node.task.meitu

import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.service.annotation.PostExchange
import team.aikero.murmuration.infra.client.RestClient

/**
 * 美图HTTP接口
 *
 * 每次调用需通过请求参数方式传递授权信息
 * api_key:{appKey}
 * api_secret:{appSecret}
 *
 * 请求头("Content-Type", "application/json")
 *
 */
@RestClient("\${domain.api.meitu:https://openapi.meitu.com}")
interface MeiTuHttpClient {

    /**
     * 发起美图任务
     */
    @PostExchange("/api/v1/sdk/sync/push")
    fun createTask(@RequestParam params: Map<String, String>, @RequestBody req: MeiTuReq): MeiTuResponse

}
