package team.aikero.murmuration.service.config

import com.aliyun.dashvector.DashVectorClient
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

@Configuration
@EnableConfigurationProperties(DashVectorProperties::class)
class DashVectorConfiguration {

    @Bean
    fun dashVectorClient(properties: DashVectorProperties): DashVectorClient {
        val (apiKey, endpoint) = properties
        return DashVectorClient(apiKey, endpoint)
    }
}
