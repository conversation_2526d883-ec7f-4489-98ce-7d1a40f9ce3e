package team.aikero.murmuration.service.node.task.aigc_image

import org.babyfish.jimmer.sql.Column
import org.babyfish.jimmer.sql.Entity
import org.babyfish.jimmer.sql.Id
import org.babyfish.jimmer.sql.Serialized

/**
 * 版型明细
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
@Entity
interface FloralPrintTemplateDetail {

    /**
     * 主键
     */
    @Id
    val templateDetailId: Long

    /**
     * 底衫图
     */
    val imageUrl: String

    /**
     * 蒙版图
     */
    @Serialized
    @Column(name = "mask_url")
    val maskImages: List<MaskImage>?

    /**
     * 贴图区域
     */
    val mappingAreaCode: String

    /**
     * 图套名称
     */
    val kitName: String

    /**
     * 图套类型
     */
    val kitType: String

    /**
     * 营销图套编码(kitType=30时必填)
     */
    val marketingAtlasCode: String?
}

/**
 * 蒙版图对象
 */
data class MaskImage(
    /**
     * 图片地址
     */
    val fileUrl: String?,
    /**
     * 旋转角度
     */
    val offsetAngle: String?,
)
