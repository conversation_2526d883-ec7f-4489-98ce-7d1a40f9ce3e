package team.aikero.murmuration.service.node.task.lingvisions.reimage

import team.aikero.blade.oss.OssTemplate
import team.aikero.blade.oss.OssTemplateExt.transferFrom
import team.aikero.murmuration.common.req.task.LingVisionsReimageRequest
import team.aikero.murmuration.core.Ability
import team.aikero.murmuration.core.Supplier
import team.aikero.murmuration.core.annotations.TaskIdentifier
import team.aikero.murmuration.core.workflow.task.TaskHandler
import team.aikero.murmuration.core.workflow.task.TaskHandlerResult
import team.aikero.murmuration.core.workflow.task.TaskResult
import team.aikero.murmuration.service.node.task.lingvisions.LingVisionsClient
import team.aikero.murmuration.service.node.task.lingvisions.ReimageProRequest
import java.net.URI

/**
 * 灵图裂变任务处理器
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@TaskIdentifier(
    supplier = Supplier.LING_VISIONS_REIMAGE,
    ability = Ability.IMAGE_DERIVATION,
)
class LingVisionsReimageTaskHandler(
    val client: LingVisionsClient,
    val ossTemplate: OssTemplate,
): TaskHandler<LingVisionsReimageRequest, String> {

    override fun create(request: LingVisionsReimageRequest): String {
        return client.reimage(
            ReimageProRequest(
                type = request.generationMode,
                image = request.imageUrl,
                similarity = request.similarity,
                bgColor = request.bgColor,
                ar = request.aspectRatio,
                num = request.n,
            )
        )
    }

    override fun query(request: LingVisionsReimageRequest, context: String): TaskResult<List<TaskHandlerResult>> {
        val jobResult = client.getJobResult(context)
        return jobResult.map { urls ->
            // 将外部图片转存到内部OSS
            val uris = urls.map(URI::create)
            val transferred = ossTemplate.transferFrom(uris)
            transferred.map(TaskHandlerResult::image)
        }
    }
}
