package team.aikero.murmuration.service.node.task.aip.upscale

import team.aikero.murmuration.service.node.task.aip.*


@AipExecutorIdentifier(AipAbility.UPSCALE)
class UpScaleExecutor(client: AipClient): AipExecutor<UpScaleInput, UpScaleOutput>(client) {

    override fun validateTaskOutput(output: UpScaleOutput) {
        if (output.resImgs.isEmpty()) {
            throw InvalidTaskOutputException("resImgs 数组为空")
        }
    }
}

data class UpScaleInput(
    /**
     * 图片链接
     */
    val inputImage: String,
    /**
     * 超分倍率
     */
    val scalefactor: Int,
)

data class UpScaleOutput(
    val resImgs: List<String>,
)
