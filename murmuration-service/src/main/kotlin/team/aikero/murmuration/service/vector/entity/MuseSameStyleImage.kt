package team.aikero.murmuration.service.vector.entity

import org.babyfish.jimmer.sql.Entity
import org.babyfish.jimmer.sql.Key

/**
 * MUSE同款图库
 *
 * <AUTHOR>
 * @since 2025-08-01
 */
@Entity
interface MuseSameStyleImage: Image {

    /**
     * SPU款号
     */
    @Key(group = "uniq_spu_code")
    val spuCode: String

    /**
     * 供给方式
     *
     * NEST字典: supply_mode
     */
    val supplyMode: String

    /**
     * 品类-字典编码
     *
     * NEST字典: clothing_category
     */
    val categoryCode: String
}
