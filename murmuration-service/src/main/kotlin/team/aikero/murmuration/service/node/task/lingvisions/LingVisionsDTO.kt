package team.aikero.murmuration.service.node.task.lingvisions

import com.fasterxml.jackson.annotation.JsonProperty
import team.aikero.murmuration.common.enums.task.BgColor
import team.aikero.murmuration.common.enums.task.GenerationMode
import java.math.BigDecimal
import java.util.UUID

/**
 * 相似图裂变进阶版请求
 */
data class ReimageProRequest(
    /**
     * 请求ID
     */
    val requestId: String = UUID.randomUUID().toString(),

    /**
     * 生成模式
     */
    val type: GenerationMode? = null,

    /**
     * 图片
     */
    val image: String,

    /**
     * 相似度
     */
    val similarity: BigDecimal? = null,

    /**
     * 仅裂变图案
     */
    val onlyPattern: Boolean? = null,

    /**
     * 圆形图案
     */
    val circle: Boolean? = null,

    /**
     * 背景颜色
     */
    val bgColor: BgColor? = null,

    /**
     * 结果图宽高比
     */
    val ar: String? = null,

    /**
     * 生图数量
     */
    val num: Int? = null,
)

/**
 * 查询任务结果请求
 */
data class GetJobResultRequest(
    /**
     * 任务ID
     */
    val jobId: String,
)

/**
 * 查询任务结果响应
 */
data class GetJobResultResponse(
    /**
     * 任务状态
     */
    val status: Status,

    /**
     * 错误信息
     */
    val message: String?,

    /**
     * 结果文件地址
     */
    val urls: List<String>,
)

/**
 * 任务状态
 */
enum class Status(val value: String) {

    /**
     * 等待执行
     */
    @JsonProperty(value = "waiting")
    WAITING("waiting"),

    /**
     * 执行中
     */
    @JsonProperty(value = "executing")
    EXECUTING("executing"),

    /**
     * 生成成功
     */
    @JsonProperty(value = "succeed")
    SUCCEED("succeed"),

    /**
     * 生成失败
     */
    @JsonProperty(value = "failed")
    FAILED("failed"),

    /**
     * 已取消
     */
    @JsonProperty(value = "canceled")
    CANCELED("canceled"),
}
