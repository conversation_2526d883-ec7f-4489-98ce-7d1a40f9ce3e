package team.aikero.murmuration.service.node.task.aip.comfyui

import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.collection.filterIf
import team.aikero.murmuration.common.req.task.ElementalDerivationRequest
import team.aikero.murmuration.core.Ability
import team.aikero.murmuration.core.Supplier
import team.aikero.murmuration.core.annotations.TaskIdentifier
import team.aikero.murmuration.core.workflow.task.TaskHandler
import team.aikero.murmuration.core.workflow.task.TaskHandlerResult
import team.aikero.murmuration.core.workflow.task.TaskResult
import team.aikero.murmuration.service.node.task.openbridge.OpenBridgeClient

/**
 * 元素化衍生任务处理器
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@TaskIdentifier(
    supplier = Supplier.COMFY_UI,
    ability = Ability.IMAGE_DERIVATION,
)
class ElementalDerivationTaskHandler(
    val executor: ComfyUiExecutor,
    val openBridgeClient: OpenBridgeClient,
): TaskHandler<ElementalDerivationRequest, String> {

    override fun create(request: ElementalDerivationRequest): String {
        check(request.n > 0) { "生成数量必须大于0" }
        return executor.createTask(ComfyUiInput(
            image = request.imageUrl,
            batchSize = request.n,
            workflowType = "JV-yuansuhuayansheng-v2.0",
        ))
    }

    override fun query(request: ElementalDerivationRequest, context: String): TaskResult<List<TaskHandlerResult>> {
        val taskId = context
        val taskResult = executor.getTask(taskId)

        return taskResult.map { output ->
            output
                .resImgs
                .filterIf(request.checkSensitiveImage) {
                    try {
                        !openBridgeClient.isSensitiveImage(it)
                    }
                    catch (ex: Exception) {
                        log.error(ex) { "敏感图检测失败: $it" }
                        true
                    }
                }
                .map(TaskHandlerResult::image)
        }
    }
}
