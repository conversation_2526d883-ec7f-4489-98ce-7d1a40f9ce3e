package team.aikero.murmuration.service.node.task.aip.changeBackgroundKontext

import com.fasterxml.jackson.annotation.JsonProperty
import team.aikero.murmuration.service.node.task.aip.*

@AipExecutorIdentifier(AipAbility.CHANGE_BACKGROUND_KONTEXT)
class ChangeBackgroundKontextExecutor(client: AipClient): AipExecutor<ChangeBackgroundKontextInput, ChangeBackgroundKontextOutput>(client) {
    override fun validateTaskOutput(output: ChangeBackgroundKontextOutput) {
        if (output.resImgs.isEmpty()) {
            throw InvalidTaskOutputException("resImgs 数组为空")
        }
    }
}

data class ChangeBackgroundKontextInput(
    /**
     * 图片链接
     */
    @field:JsonProperty("image_url")
    val imageUrl: String,

    /**
     * 比例
     * @see AspectRatio
     */
    var aspectRatio: String,

    /**
     * 背景图
     */
    @field:JsonProperty("background_image_url")
    val backgroundImageUrl: String?,

    /**
     * 提示词
     */
    var prompt: String?,

    /**
     * 生图数量
     */
    @JsonProperty("batch_size")
    val batchSize: Int,
    )

data class ChangeBackgroundKontextOutput(
    /**
     * 结果图
     */
    @JsonProperty("bg_after_super_res_urls")
    val resImgs: List<String>,

    /**
     * 反推的prompt（prompt未给则会返回这个参数）
     */
    @JsonProperty("background_prompt")
    val backgroundPrompt: String?,
)