package team.aikero.murmuration.service.material.entity

import org.babyfish.jimmer.sql.Default
import org.babyfish.jimmer.sql.DissociateAction
import org.babyfish.jimmer.sql.Entity
import org.babyfish.jimmer.sql.JoinColumn
import org.babyfish.jimmer.sql.ManyToOne
import org.babyfish.jimmer.sql.OnDissociate
import org.babyfish.jimmer.sql.Table
import team.aikero.blade.data.jimmer.entity.CreatedTime
import team.aikero.blade.data.jimmer.entity.LongId
import team.aikero.blade.data.jimmer.entity.OrgWithFilter
import team.aikero.blade.data.jimmer.entity.RevisedTime
import team.aikero.blade.data.jimmer.entity.Reviser
import team.aikero.blade.data.jimmer.entity.TenantId

@Entity
@Table(
    name = "pose"
)
interface Pose : LongId, CreatedTime, RevisedTime, Reviser, OrgWithFilter, TenantId {

    /**
     * 图片url
     */
    val imageUrl: String

    /**
     * 提示词
     */
    val prompt: String?

    /**
     * 识别状态
     */
    @Default(value = "PROCESSING")
    val identifyStatus: IdentifyStatus

    @ManyToOne
    @JoinColumn(name = "pose_group_id")
    @OnDissociate(value = DissociateAction.DELETE)
    val poseGroup: PoseGroup
}

enum class IdentifyStatus {

    /**
     * 识别中
     */
    PROCESSING,

    /**
     * 成功
     */
    SUCCEEDED,

    /**
     * 失败
     */
    FAILED
}