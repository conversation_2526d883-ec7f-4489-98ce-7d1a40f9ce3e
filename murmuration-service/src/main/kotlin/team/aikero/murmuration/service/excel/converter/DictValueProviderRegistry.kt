package team.aikero.murmuration.service.excel.converter

import team.aikero.murmuration.service.excel.annotation.DictValueProvider
import java.util.concurrent.ConcurrentHashMap

/**
 * 字典值提供者注册中心
 */
object DictValueProviderRegistry {
    private val providers = ConcurrentHashMap<String, DictValueProvider>()
    
    /**
     * 注册字典值提供者
     */
    fun registerProvider(dictType: String, provider: DictValueProvider) {
        providers[dictType] = provider
    }
    
    /**
     * 获取字典值提供者
     */
    fun getProvider(dictType: String): DictValueProvider? {
        return providers[dictType]
    }
    
    /**
     * 获取所有字典类型
     */
    fun getAllDictTypes(): Set<String> {
        return providers.keys
    }
}