package team.aikero.murmuration.service.node.task.aip.postureKontext

import com.fasterxml.jackson.annotation.JsonProperty
import team.aikero.murmuration.service.node.task.aip.*

@AipExecutorIdentifier(AipAbility.AIBOX_POSTURE_KONTEXT)
class PostureKontextExecutor(client: AipClient): AipExecutor<PostureKontextInput, PostureKontextOutput>(client) {
    override fun validateTaskOutput(output: PostureKontextOutput) {
        if (output.resImgs.isEmpty()) {
            throw InvalidTaskOutputException("resImgs 数组为空")
        }
    }
}

data class PostureKontextInput(
    /**
     * 图片链接
     */
    @field:JsonProperty("image_url")
    val imageUrl: String,

    /**
     * 比例
     * @see AspectRatio
     */
    var aspectRatio: String,

    /**
     * 姿势提示词
     */
    @field:JsonProperty("posture_prompt")
    var posturePrompt: String?,
)

data class PostureKontextOutput(
    /**
     * 结果图
     */
    @field:JsonProperty("result_image")
    val resImgs: List<String>,
)