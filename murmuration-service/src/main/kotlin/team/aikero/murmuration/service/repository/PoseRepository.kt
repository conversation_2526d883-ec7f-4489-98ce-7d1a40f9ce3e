package team.aikero.murmuration.service.repository

import org.babyfish.jimmer.View
import org.babyfish.jimmer.spring.repo.support.AbstractKotlinRepository
import org.babyfish.jimmer.spring.repository.orderBy
import org.babyfish.jimmer.sql.kt.KSqlClient
import org.babyfish.jimmer.sql.kt.ast.expression.eq
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Repository
import team.aikero.blade.data.jimmer.findPage
import team.aikero.murmuration.service.material.entity.IdentifyStatus
import team.aikero.murmuration.service.material.entity.Pose
import team.aikero.murmuration.service.material.entity.id
import team.aikero.murmuration.service.material.entity.identifyStatus
import team.aikero.murmuration.service.material.entity.poseGroup
import kotlin.reflect.KClass

@Repository
class PoseRepository(
    sql: KSqlClient
) : AbstractKotlinRepository<Pose, Long>(sql) {

    fun <V : View<Pose>> pageByGroupIdAndIdentifyStatus(
        pageable: Pageable,
        groupId: Long,
        identifyStatus: IdentifyStatus,
        view: KClass<V>
    ) =
        createQuery {
            where(table.poseGroup.id eq groupId)
            where(table.identifyStatus eq identifyStatus)
            orderBy(pageable.sort)
            select(table.fetch(view))
        }.findPage(pageable)

}