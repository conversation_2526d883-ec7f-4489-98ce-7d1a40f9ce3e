package team.aikero.murmuration.service.node.task.aip.changeBackgroundKontext

import team.aikero.murmuration.common.req.task.ai_box.ChangeBackgroundHandlerInputRequest
import team.aikero.murmuration.core.Ability
import team.aikero.murmuration.core.Supplier
import team.aikero.murmuration.core.annotations.TaskIdentifier
import team.aikero.murmuration.core.workflow.task.TaskHandler
import team.aikero.murmuration.core.workflow.task.TaskHandlerResult
import team.aikero.murmuration.core.workflow.task.TaskResult
import team.aikero.murmuration.service.utils.ImageUtil

/**
 * Kontext换背景任务处理器
 */
@TaskIdentifier(
    supplier = Supplier.AIP,
    ability = Ability.CHANGE_BACKGROUND
)
class ChangeBackgroundKontextTaskHandle(
    val executor: ChangeBackgroundKontextExecutor,
    private val imageUtil: ImageUtil
): TaskHandler<ChangeBackgroundHandlerInputRequest, String>  {
    override fun create(request: ChangeBackgroundHandlerInputRequest): String {
        return executor.createTask(
            ChangeBackgroundKontextInput(
                imageUrl =  request.imageUrl,
                aspectRatio =  imageUtil.getAspectRatio(request.imageUrl),
                backgroundImageUrl =  request.scenePicture.path,
                prompt =  request.scenePicture.caption,
                batchSize =  request.batchSize,
            )
        )
    }

    override fun query(request: ChangeBackgroundHandlerInputRequest, context: String): TaskResult<List<TaskHandlerResult>> {
        val taskId = context
        val taskResult = executor.getTask(taskId)
        return taskResult.map { output ->
            output.resImgs.map { TaskHandlerResult.image(it, output.backgroundPrompt) }
        }
    }
}