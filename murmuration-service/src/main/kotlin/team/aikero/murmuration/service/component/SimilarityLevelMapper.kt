package team.aikero.murmuration.service.component

import org.springframework.stereotype.Component
import team.aikero.murmuration.common.enums.SearchDimension
import team.aikero.murmuration.service.config.DashVectorProperties

/**
 * 相似度等级映射器
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Component
class SimilarityLevelMapper(val dashVectorProperties: DashVectorProperties) {

    /**
     * 相似度 -> 相似等级
     *
     * (-♾️, 0.8] -> 0
     * (0.8, 0.9] -> 1
     * (0.9, +♾️) -> 2
     */
    fun mapSimilarityToLevel(similarity: Float, dimension: SearchDimension): Int {
        // 0.8, 0.9
        val thresholds = dashVectorProperties.thresholds[dimension] ?: throw IllegalStateException("未配置[${dimension}]的相似度阈值")
        val index = thresholds.indexOfFirst { similarity <= it }
        return if (index == -1) thresholds.size else index
    }
}
