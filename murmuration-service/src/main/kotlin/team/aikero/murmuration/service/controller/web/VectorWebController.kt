package team.aikero.murmuration.service.controller.web

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.convertValue
import org.babyfish.jimmer.sql.kt.KSqlClient
import org.babyfish.jimmer.sql.kt.ast.expression.valueIn
import org.springframework.web.bind.annotation.*
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.ok
import team.aikero.murmuration.common.enums.SearchDimension
import team.aikero.murmuration.common.req.BatchSearchSimilarityRequest
import team.aikero.murmuration.common.vo.DocumentVo
import team.aikero.murmuration.service.component.ImageVectorFetcher
import team.aikero.murmuration.service.component.SimilarityLevelMapper
import team.aikero.murmuration.service.controller.inner.VectorInnerController
import team.aikero.murmuration.service.vector.VectorStoreFactory
import team.aikero.murmuration.service.vector.entity.Image
import team.aikero.murmuration.service.vector.entity.InfringementImage
import team.aikero.murmuration.service.vector.entity.MuseSameStyleImage
import team.aikero.murmuration.service.vector.entity.id

/**
 * 向量服务
 *
 * <AUTHOR>
 * @since 2025-08-01
 */
@RestController
@RequestMapping("/web/vector")
class VectorWebController(
    val vectorInnerController: VectorInnerController,
    val vectorStoreFactory: VectorStoreFactory,
    val sql: KSqlClient,
    val objectMapper: ObjectMapper,
    val imageVectorFetcher: ImageVectorFetcher,
    val similarityLevelMapper: SimilarityLevelMapper,
) {

    /**
     * 检索图片相似度
     *
     * @param url 图片URL
     * @param dimensions 检索维度(支持传多个)
     * @return 各维度对应的检索信息
     */
    @GetMapping("/search-similarity")
    fun searchSimilarity(
        @RequestParam url: String,
        @RequestParam dimensions: Set<SearchDimension>,
    ) = vectorInnerController.searchSimilarity(url, dimensions)

    /**
     * 批量检索图片相似度
     *
     * @param request 批量检索请求
     * @return 文档列表
     */
    @PostMapping("/batch-search-similarity")
    fun batchSearchSimilarity(@RequestBody request: BatchSearchSimilarityRequest) = vectorInnerController.batchSearchSimilarity(request)

    /**
     * 查看topK
     *
     * @param url 图片URL
     * @param dimension 检索维度
     * @param topK topK
     * @return topK结果
     */
    @GetMapping("/top-k")
    fun topK(
        @RequestParam url: String,
        @RequestParam dimension: SearchDimension,
        @RequestParam topK: Int,
    ): DataResponse<List<DocumentVo>> {
        // 检索topK
        val vector = imageVectorFetcher.fetch(url, dimension)
        val searchStore = vectorStoreFactory.getSearchStore(dimension)
        val tops = searchStore.topK(vector, topK)
        val imageIds = tops.map { it.id.toLong() }

        // 组装返回结果
        // id -> image
        val imageMap: Map<Long, Image> = when (dimension) {
            SearchDimension.INFRINGEMENT -> sql.executeQuery(InfringementImage::class) {
                where(table.id valueIn imageIds)
                select(table)
            }
            SearchDimension.MUSE_SAME_STYLE -> sql.executeQuery(MuseSameStyleImage::class) {
                where(table.id valueIn imageIds)
                select(table)
            }
        }.associateBy { it.id }

        val documents = tops.map { doc ->
            val image = imageMap[doc.id.toLong()] ?: throw NullPointerException("根据文档ID[${doc.id}]找不到图库数据")
            val similarityLevel = similarityLevelMapper.mapSimilarityToLevel(doc.similarity, dimension)
            DocumentVo(
                url = image.url,
                similarityLevel = similarityLevel,
                fields = objectMapper.convertValue(image),
            )
        }
        return ok(documents)
    }
}
