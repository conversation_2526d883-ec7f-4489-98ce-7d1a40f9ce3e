package team.aikero.murmuration.service.node.task.aip.four_k

import team.aikero.murmuration.service.node.task.aip.*

@AipExecutorIdentifier(AipAbility.FOUR_K)
class FourKExecutor(client: AipClient): AipExecutor<FourKInput, FourKOutput>(client) {
    override fun validateTaskOutput(output: FourKOutput) {
        if (output.resImgs.isEmpty()) {
            throw InvalidTaskOutputException("resImgs 数组为空")
        }
    }
}

data class FourKInput(
    /**
     * 图片链接
     */
    val inputImage: String,

    /**
     * 输入图类型，real_model（对模特服装细节有细化的想象，有可能细化不合理）、（cad、mannequin、three_d_render已弃用）、其他自定义文本如“SMART_DESIGN”则返回通用模式的超分结果
     */
    val modelType: String = "SMART_DESIGN",
)

data class FourKOutput(
    val resImgs: List<String>,
)