package team.aikero.murmuration.service.node.task.meitu.cutouts

import team.aikero.blade.core.toolkit.isNotNull
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.toJson
import team.aikero.murmuration.core.Ability
import team.aikero.murmuration.core.Supplier
import team.aikero.murmuration.core.annotations.TaskIdentifier
import team.aikero.murmuration.core.workflow.task.TaskHandler
import team.aikero.murmuration.core.workflow.task.TaskHandlerResult
import team.aikero.murmuration.core.workflow.task.TaskImmediatelyDoneException
import team.aikero.murmuration.core.workflow.task.TaskResult
import team.aikero.murmuration.service.node.task.meitu.*
import tech.tiangong.fashion.aigc.image.client.ImageOpenClient
import tech.tiangong.fashion.aigc.image.common.vo.req.CuttingImageReq
import tech.tiangong.fashion.aigc.image.common.vo.req.TransparentImageReq

/**
 * 抠图任务处理器
 */
@TaskIdentifier(
    supplier = Supplier.MEI_TU,
    ability = Ability.CUTOUTS,
)
class MeiTuCutoutsTaskHandler(
    val meiTuClient: MeiTuClient,
    val imageOpenClient: ImageOpenClient
) : TaskHandler<MeiTuCutoutsReq, Unit> {

    override fun create(request: MeiTuCutoutsReq) {
        val response = imageOpenClient.transparent(TransparentImageReq(request.imageUrl))
        log.info { "判断透明图响应:${response.toJson()}" }
        check(response.successful) {"判断透明图失败"}

        val transparentUrl = if (response.data == false) meiTu(request) else request.imageUrl

        val imageUrl = cropFashion(transparentUrl)

        throw TaskImmediatelyDoneException(listOf(TaskHandlerResult.image(imageUrl)))
    }

    override fun query(request: MeiTuCutoutsReq, context: Unit): TaskResult<List<TaskHandlerResult>> {
        TODO("任务立即完成，不需要执行 query")
    }

    /**
     * 调用美图抠图
     */
    private fun meiTu(request: MeiTuCutoutsReq): String {
        val response = meiTuClient.createTask(
            MeiTuReq(
                initImages = listOf(MeiTuImageReq(request.imageUrl)),
                params = request.meiTuParameterReq?.let {
                    MeiTuParamsReq(it).toJson()
                }
            ))

        log.info { "meitu reqImage:${request.imageUrl}, resp:${response.toJson()}" }
        check(response.succeed()) { "美图任务创建失败: ${response.message}" }
        check(response.data?.result != null) { "美图任务响应为空" }
        check(response.data.result.urls != null) { "美图任务响应结果为空" }

        return response.data.result.urls.first()
    }

    /**
     * 透明图裁剪
     */
    private fun cropFashion(imageUrl: String): String {
        val response = imageOpenClient.cutting(CuttingImageReq(imageUrl))
        check(response.successful) {"透明图裁剪失败"}
        check(response.data.isNotNull()) {"透明图裁剪结果为空"}
        log.info { "cropFashion resp:${response.data}, req:${imageUrl}" }
        return response.data!!
    }

}

/**
 * 美图抠图任务参数
 */
data class MeiTuCutoutsReq(
    /**
     * 参考图url
     */
    var imageUrl: String,

    /**
     * 额外参数详细信息
     */
    var meiTuParameterReq: MeiTuParameterReq? = null
)
