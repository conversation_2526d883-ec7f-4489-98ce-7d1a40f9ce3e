package team.aikero.murmuration.service.repository

import org.babyfish.jimmer.sql.ast.mutation.SaveMode
import org.babyfish.jimmer.sql.kt.KSqlClient
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import team.aikero.blade.data.jimmer.repository.BladeKotlinRepository
import team.aikero.murmuration.common.enums.SearchDimension
import team.aikero.murmuration.service.vector.VectorStoreFactory
import team.aikero.murmuration.service.vector.entity.InfringementImage

/**
 * 侵权图库公共CRUD逻辑
 *
 * <AUTHOR>
 * @since 2025-08-07
 */
@Component
class InfringementImageRepository(
    sql: KSqlClient,
    vectorStoreFactory: VectorStoreFactory,
) : BladeKotlinRepository<InfringementImage, Long>(sql)  {

    /**
     * 向量库
     */
    val searchStore = vectorStoreFactory.getSearchStore(SearchDimension.INFRINGEMENT)

    /**
     * 更新启用状态
     */
    @Transactional(rollbackFor = [Exception::class])
    fun updateEnabled(id: Long, enabled: Bo<PERSON>an) {
        // 更新数据库
        save(InfringementImage {
            this.id = id
            this.enabled = enabled
        }, SaveMode.UPDATE_ONLY)

        // 更新向量库
        searchStore.updateFields("$id", mapOf("enabled" to enabled))
    }

    /**
     * 删除
     */
    @Transactional(rollbackFor = [Exception::class])
    fun delete(id: Long) {
        // 删除数据库
        deleteById(id)

        // 删除向量库
        searchStore.deleteById("$id")
    }
}
