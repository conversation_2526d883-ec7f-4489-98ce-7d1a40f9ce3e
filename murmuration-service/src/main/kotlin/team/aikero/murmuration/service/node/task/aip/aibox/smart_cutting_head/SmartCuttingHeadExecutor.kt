package team.aikero.murmuration.service.node.task.aip.aibox.smart_cutting_head

import team.aikero.murmuration.service.node.task.aip.AipAbility
import team.aikero.murmuration.service.node.task.aip.AipClient
import team.aikero.murmuration.service.node.task.aip.AipExecutor
import team.aikero.murmuration.service.node.task.aip.AipExecutorIdentifier
import team.aikero.murmuration.service.node.task.aip.aibox.hand_repair.HandRepairInput
import team.aikero.murmuration.service.node.task.aip.aibox.hand_repair.HandRepairOutput

/**
 * 智能裁头
 */
@AipExecutorIdentifier(AipAbility.SMART_CUTTING_HEAD)
class SmartCuttingHeadExecutor(
    client: AipClient
) : AipExecutor<SmartCuttingHeadInput, SmartCuttingHeadOutput>(client) {

}

data class SmartCuttingHeadInput(
    val inputImgs: List<String>,
    val topLoc: String,
    val size: String
)

data class SmartCuttingHeadOutput(
    val resImgs: List<String>
)