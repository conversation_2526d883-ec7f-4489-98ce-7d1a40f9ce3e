package team.aikero.murmuration.service.node.task.aip.aibox.hand_repair

import team.aikero.murmuration.common.req.task.ai_box.HandRepairHandlerRequest
import team.aikero.murmuration.common.req.task.ai_box.HandRepairRequest
import team.aikero.murmuration.core.Ability
import team.aikero.murmuration.core.Supplier
import team.aikero.murmuration.core.annotations.TaskIdentifier
import team.aikero.murmuration.core.workflow.task.TaskHandler
import team.aikero.murmuration.core.workflow.task.TaskHandlerResult
import team.aikero.murmuration.core.workflow.task.TaskResult

/**
 * 手部修复
 */
@TaskIdentifier(
    supplier = Supplier.AIP,
    ability = Ability.AI_BOX_HAND_REPAIR
)
class HandRepairTaskHandler(
    private val executor: HandRepairExecutor,
) : TaskHandler<HandRepairHandlerRequest, String> {
    override fun create(request: HandRepairHandlerRequest): String {
        return executor.createTask(
            HandRepairInput(
                List(request.count) { request.inputImage }
            )
        )
    }

    override fun query(
        request: HandRepairHandlerRequest,
        context: String
    ): TaskResult<List<TaskHandlerResult>> {
        val taskResult = executor.getTask(context)
        return taskResult.map {
            it.resImgs.map(TaskHandlerResult::image)
        }
    }
}