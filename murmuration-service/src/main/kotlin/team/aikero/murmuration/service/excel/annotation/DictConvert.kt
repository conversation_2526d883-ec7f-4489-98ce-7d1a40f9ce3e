package team.aikero.murmuration.service.excel.annotation

import kotlin.reflect.KClass

/**
 * 字典转换注解
 * 用于标记字段需要进行字典值转换
 *
 * @param dictType 字典类型
 * @param targetClass 目标类型
 * @param provider 字典值提供者
 * @param dropDownConfig 下拉配置
 */
@Target(AnnotationTarget.FIELD)
@Retention(AnnotationRetention.RUNTIME)
annotation class DictConvert(
    val dictType: String,
    val targetClass: KClass<*> = String::class,
    val provider: KClass<*> = DictValueProvider::class,
    val dropDownConfig: DropDownConfig = DropDownConfig()
)

/**
 * 下拉配置
 */
annotation class DropDownConfig(
    val enabled: Boolean = false,
    val sheetName: String = "字典选项",
    val firstRow: Int = 1,
    val firstCol: Int = 1,
    val lastCol: Int = 1
)

/**
 * 字典值提供者接口
 */
interface DictValueProvider {
    /**
     * 获取字典映射关系
     */
    fun getDictMapping(dictType: String): Map<String, Any>
    
    /**
     * 获取反向映射
     */
    fun getReverseMapping(dictType: String): Map<Any, String>
    
    /**
     * 获取下拉选项
     */
    fun getDropDownOptions(dictType: String): List<String>
}