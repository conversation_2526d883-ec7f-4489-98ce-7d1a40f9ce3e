package team.aikero.murmuration.service.utils

import org.springframework.stereotype.Component
import team.aikero.murmuration.service.component.ImageInfoFetcher
import team.aikero.murmuration.service.node.task.aip.kontextpod.AspectRatio
import java.math.BigDecimal
import java.math.RoundingMode

@Component
class ImageUtil(
    private val imageInfoFetcher: ImageInfoFetcher
) {

    fun getAspectRatio(imageUrl: String): String {
        val (height, weight) = imageInfoFetcher.fetch(imageUrl)
        //比例 = 宽 / 高，找到最接近的比例
        val value = BigDecimal(weight).setScale(3, RoundingMode.HALF_UP) /
                BigDecimal(height)
        return AspectRatio.similarValue(value.setScale(3, RoundingMode.HALF_UP))
    }
}