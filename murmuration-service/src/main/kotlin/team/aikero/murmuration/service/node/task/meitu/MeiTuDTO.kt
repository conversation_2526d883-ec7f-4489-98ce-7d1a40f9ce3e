package team.aikero.murmuration.service.node.task.meitu

import com.fasterxml.jackson.annotation.JsonProperty
import jakarta.validation.constraints.NotBlank

/**
* 接口凭证
*/
data class Certificate(
    /**
     * 机构号
     */
    @JsonProperty("api_key")
    val apiKey: String = "9e879f79f33b4addbe9beed646cc0270",

    /**
     * 授权码
     */
    @JsonProperty("api_secret")
    val apiSecret: String = "c04bcf95c09b444c969a52cff6cbe751"
) {
    fun toMap(): Map<String, String> {
        return mapOf(
            "api_key" to apiKey,
            "api_secret" to apiSecret
        )
    }
}

/**
 * 美图任务参数
 */
data class MeiTuReq(
    /**
     * 原始图片地址
     */
    @JsonProperty("init_images")
    var initImages: List<MeiTuImageReq>,

    /**
     * 额外参数的json信息（来自MeiTuParamsReq）
     */
    var params: String? = null,

    /**
     * 请求超时时间，单位秒
     */
    @JsonProperty("sync_timeout")
    var syncTimeout: Int = 120,

    /**
     * 任务类型，固定“mtlab”
     */
    @JsonProperty("task_type")
    var taskType: String = "mtlab",

    /**
     * 任务地址，固定“v1/sod”
     */
    var task: String = "v1/sod",
)

data class MeiTuImageReq(
    /**
     * 原图地址
     */
    @field:NotBlank(message = "原图地址不能为空")
    var url: String,
)

/**
 * 额外参数信息
 */
data class MeiTuParamsReq(
    /**
     * 额外参数详细信息
     */
    var parameter: MeiTuParameterReq? = null,

    )

/**
 * 额外参数详细信息
 */
open class MeiTuParameterReq(
    /**
     * “url”：抠图结果以短期有效的url返回
     */
    @JsonProperty("rsp_media_type")
    var rspMediaType: String? = null,

    /**
     * 是否返回mask图，True只返回mask图，False返回结果图
     */
    var nMask: Boolean = false,

    /**
     * 选择要使用的抠图模型，传0：表示使用人像抠图；传1：表示使用商品抠图；传2：表示使用图形抠图。若不传，模型内部会自动判断选择使用哪个模型
     */
    @JsonProperty("model_type")
    var modelType: Int? = null,

    /**
     * 图标类型可以添加用户交互框参数，坐标应当使用相对坐标，示例：[[[0.01, 0.814], [0.12, 0.814], [0.12, 0.96], [0.01, 0.96]]
     */
    var userboxes: String? = null,

    /**
     * 是否返回黑白图，True只返回黑白mask图，False返回四通道Mask图，默认为False
     */
    var blackwhite: Boolean = false,

    /**
     * 是否返回目标位置，True返回目标位置top_x,top_y,bottom_x,bottom_y,默认为False
     */
    var nbox: Boolean = true,
)


/**
 * 美图奇想大模型-智能抠图 返回报文
 * // 请求成功示例
 * {
 *         "code": 0,
 *         "data": {
 *                 "status": 10,
 *                 "result": {
 *                         "id": "1cc79381-d367-4630-a7bc-0633fbc72637",
 *                         "urls": ["https://obs.mtlab.meitu.com/mtopen/7071f5094b73406da8db0136fc1f7831/MTY4OTgzNjQwMA==/50a84e28-d3e9-453b-b486-da758c5b88cd.jpg"],
 *                         "parameters": {
 *                                 "Kind": 0,
 *                                 "bottom_x": "None",
 *                                 "bottom_y": "None",
 *                                 "exist_salient": true,
 *                                 "process_time": 120.13816833496094,
 *                                 "pull_time": 88.46640586853027,
 *                                 "rsp_media_type": "url",
 *                                 "top_x": "None",
 *                                 "top_y": "None",
 *                                 "use_fe": true,
 *                                 "version": "2.4.0"
 *                         }
 *                 },
 *                 "progress": 1,
 *                 "predict_elapsed": 10000
 *         }
 * }
 * }
 */
data class MeiTuResponse(
    /**
     * code=0时表示请求成功，code>0表示请求失败
     */
    val code: Int,

    /**
     * 错误信息
     */
    val message: String? = null,

    /**
     * 返回任务处理详情
     */
    val data: MeiTuModelVo? = null,
) {
    /**
     * 成功的响应
     */
    fun succeed(): Boolean = code == 0

    /**
     * 失败的响应
     */
    fun failed(): Boolean = code != 0
}

/**
 * 美图任务处理详情
 */
data class MeiTuModelVo(
    /**
     * 状态码:
     * 9=超时，2=失败，10=成功
     */
    val status: Int,

    /**
     * 美图任务处理结果内容
     */
    val result: MeiTuResultVo? = null,

    /**
     * 任务进度(未用，含义不明)
     */
    var progress: Int? = null,

    /**
     * 预测已用(未用，含义不明)
     */
    @JsonProperty("predict_elapsed")
    var predictElapsed: Int? = null,
) {
    /**
     * 成功的响应
     */
    fun succeed(): Boolean = status == 10

    /**
     * 失败的响应
     */
    fun failed(): Boolean = status != 10
}

/**
 * 美图任务处理结果内容
 */
data class MeiTuResultVo(
    /**
     * 任务ID
     */
    var id: String? = null,

    /**
     * code=0时表示请求成功，code>0表示请求失败
     */
    val code: Int? = null,

    /**
     * 处理结果图片集
     */
    val urls: MutableList<String>? = null,

    /**
     * 额外返回参数
     */
    val parameters: MeiTuResultParametersVo? = null,

    )

/**
 * 额外返回参数
 */
data class MeiTuResultParametersVo(
    /**
     * 0：表示人像；1：表示商品；2：表示图形
     */
    @JsonProperty("Kind")
    var kind: Int? = null,

    /**
     * 右下角X坐标
     */
    @JsonProperty("bottom_x")
    var bottomX: Int,

    /**
     * 右下角Y坐标
     */
    @JsonProperty("bottom_y")
    var bottomY: Int,

    /**
     * 表示是否有显著性目标
     */
    @JsonProperty("exist_salient")
    var existSalient: Boolean,

    /**
     * ‘jpg’，表示 media_data 是经过 base64 压缩过的图片
     */
    @JsonProperty("rsp_media_type")
    var rspMediaType: String? = null,

    /**
     * 左上角X坐标
     */
    @JsonProperty("top_x")
    var topX: Int,

    /**
     * 左上角Y坐标
     */
    @JsonProperty("top_y")
    var topY: Int,

    /**
     * true:表示需要使用前景估计，false:表示不需要使用前景估计
     */
    @JsonProperty("use_fe")
    var useFe: Boolean,

    /**
     * 版本号
     */
    var version: String,
)
