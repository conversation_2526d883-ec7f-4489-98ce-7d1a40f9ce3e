package team.aikero.murmuration.service.node.task.lazada

import com.fasterxml.jackson.annotation.JsonProperty
import team.aikero.murmuration.common.enums.task.ClothingType

/**
 * 虚拟换衣请求
 */
data class TryOnClothRequest(
    /**
     * 默认False，保持模特身材、⼿部、配饰等
     */
    @JsonProperty("keep_model")
    val keepModel: Boolean? = null,

    /**
     * ⽤于上下装⼀起更换等场景，输⼊下装的图像
     */
    @JsonProperty("additional_cloth_image_url")
    val additionalClothImageUrl: String? = null,

    /**
     * 输⼊的服装Url
     */
    @JsonProperty("cloth_image_url")
    val clothImageUrl: String,

    /**
     * 换装类型
     */
    @JsonProperty("type")
    val type: ClothingType,

    /**
     * ⽣图数量
     */
    @JsonProperty("batch_size")
    val batchSize: Int,

    /**
     * 模特参考图
     */
    @JsonProperty("model_reference_image_url")
    val modelReferenceImageUrl: String,

    /**
     * 图片比例
     */
    @JsonProperty("ratio")
    val ratio: String? = null,
)

/**
 * AI换脸请求
 */
data class ChangeFaceRequest(
    /**
     * 原始图URL
     */
    @JsonProperty("raw_image_url")
    val rawImageUrl: String,

    /**
     * 模特编码
     */
    @JsonProperty("model_code")
    val modelCode: String,

    /**
     * 生图数量
     */
    @JsonProperty("batch_size")
    val batchSize: Int,

    /**
     * 图片比例
     */
    @JsonProperty("ratio")
    val ratio: String? = null,
)

/**
 * AI换背景请求
 */
data class ChangeBackgroundRequest(
    @JsonProperty("product_image_url")
    val productImageUrl: String,

    @JsonProperty("background_code")
    val backgroundCode: String,

    @JsonProperty("batch_size")
    val batchSize: Int,

    @JsonProperty("ratio")
    val ratio: String? = null,
)

/**
 * Lazada响应格式
 */
data class Response<T>(
    @JsonProperty("code")
    val code: String?,

    @JsonProperty("message")
    val message: String?,

    @JsonProperty("request_id")
    val requestId: String?,

    val result: T?,
)

/**
 * 创建任务结果
 */
data class CreateTaskResult(
    val success: Boolean,
    val resultCode: String?,
    val resultMessage: String?,
    val taskId: String?,
)

/**
 * 查询任务结果
 */
data class GetTaskResult(
    val success: Boolean,
    val resultCode: String?,
    val resultMessage: String?,
    val data: List<String>?,
    val status: Status?,
    val failMessage: String?,
) {
    enum class Status(val value: String) {
        /**
         * 等待中
         */
        @JsonProperty("waiting")
        WAITING("waiting"),

        /**
         * 运⾏中
         */
        @JsonProperty("running")
        RUNNING("running"),

        /**
         * 取消
         */
        @JsonProperty("cancelled")
        CANCELED("cancelled"),

        /**
         * 失败
         */
        @JsonProperty("fail")
        FAILED("fail"),

        /**
         * 成功
         */
        @JsonProperty("success")
        SUCCESS("success"),
    }
}
