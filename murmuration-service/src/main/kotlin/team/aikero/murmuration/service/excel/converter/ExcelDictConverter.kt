package team.aikero.murmuration.service.excel.converter

import org.springframework.stereotype.Component
import team.aikero.murmuration.service.excel.annotation.DictConvert
import team.aikero.murmuration.service.excel.annotation.DictValueProvider
import java.lang.reflect.Field

/**
 * 字典转换器 - 核心转换功能
 */
@Component
class ExcelDictConverter {
    
    /**
     * 转换对象中的字典值
     */
    fun <T> convertDictValues(obj: T): T {
        if (obj == null) return obj
        
        val clazz = obj::class.java
        clazz.declaredFields.forEach { field ->
            val dictConvert = field.getAnnotation(DictConvert::class.java)
            if (dictConvert != null) {
                convertField(obj, field, dictConvert)
            }
        }
        
        return obj
    }
    
    /**
     * 从字符串值映射转换对象
     * 这个方法用于处理从 Excel 读取的字符串数据
     */
    @Suppress("UNCHECKED_CAST")
    fun <T> convertFromStringValues(source: Map<String, String>, targetClass: Class<T>): T {
        val target = targetClass.getDeclaredConstructor().newInstance()
        
        targetClass.declaredFields.forEach { field ->
            val dictConvert = field.getAnnotation(DictConvert::class.java)
            if (dictConvert != null) {
                val stringValue = source[field.name]
                if (stringValue != null) {
                    convertFieldFromString(target, field, dictConvert, stringValue)
                }
            }
        }
        
        return target as T
    }
    
    /**
     * 批量转换对象列表中的字典值
     */
    fun <T> convertDictValues(list: List<T>): List<T> {
        return list.map { convertDictValues(it) }
    }
    
    /**
     * 反向转换 - 将字典值转换为显示值
     */
    fun <T> convertToDisplayValues(obj: T): T {
        if (obj == null) return obj
        
        val clazz = obj::class.java
        clazz.declaredFields.forEach { field ->
            val dictConvert = field.getAnnotation(DictConvert::class.java)
            if (dictConvert != null) {
                convertFieldToDisplay(obj, field, dictConvert)
            }
        }
        
        return obj
    }
    
    /**
     * 批量反向转换
     */
    fun <T> convertToDisplayValues(list: List<T>): List<T> {
        return list.map { convertToDisplayValues(it) }
    }
    
    /**
     * 获取字段的字典映射
     */
    fun getDictMapping(field: Field, dictConvert: DictConvert): Map<String, Any> {
        val provider = DictValueProviderRegistry.getProvider(dictConvert.dictType)
            ?: throw IllegalArgumentException("No provider found for dict type: ${dictConvert.dictType}")
        
        return provider.getDictMapping(dictConvert.dictType)
    }
    
    /**
     * 获取字段的下拉选项
     */
    fun getDropDownOptions(field: Field, dictConvert: DictConvert): List<String> {
        val provider = DictValueProviderRegistry.getProvider(dictConvert.dictType)
            ?: throw IllegalArgumentException("No provider found for dict type: ${dictConvert.dictType}")
        
        return provider.getDropDownOptions(dictConvert.dictType)
    }
    
    /**
     * 转换单个字段
     */
    private fun convertField(obj: Any, field: Field, dictConvert: DictConvert) {
        try {
            field.isAccessible = true
            val value = field.get(obj)
            
            if (value != null) {
                val provider = DictValueProviderRegistry.getProvider(dictConvert.dictType)
                    ?: throw IllegalArgumentException("No provider found for dict type: ${dictConvert.dictType}")
                
                val mapping = provider.getDictMapping(dictConvert.dictType)
                val convertedValue = mapping[value.toString()]
                
                if (convertedValue != null) {
                    val targetValue = convertToTargetType(convertedValue, dictConvert.targetClass.java)
                    field.set(obj, targetValue)
                }
            }
        } catch (e: Exception) {
            throw RuntimeException("Failed to convert field ${field.name}", e)
        }
    }
    
    /**
     * 从字符串值转换单个字段
     */
    private fun convertFieldFromString(obj: Any, field: Field, dictConvert: DictConvert, stringValue: String) {
        try {
            field.isAccessible = true
            
            val provider = DictValueProviderRegistry.getProvider(dictConvert.dictType)
                ?: throw IllegalArgumentException("No provider found for dict type: ${dictConvert.dictType}")
            
            val mapping = provider.getDictMapping(dictConvert.dictType)
            val convertedValue = mapping[stringValue]
            
            if (convertedValue != null) {
                val targetValue = convertToTargetType(convertedValue, dictConvert.targetClass.java)
                field.set(obj, targetValue)
            } else {
                // 如果没有找到映射，保持原始字符串值
                field.set(obj, stringValue)
            }
        } catch (e: Exception) {
            throw RuntimeException("Failed to convert field ${field.name} from string value", e)
        }
    }
    
    /**
     * 反向转换单个字段
     */
    private fun convertFieldToDisplay(obj: Any, field: Field, dictConvert: DictConvert) {
        try {
            field.isAccessible = true
            val value = field.get(obj)
            
            if (value != null) {
                val provider = DictValueProviderRegistry.getProvider(dictConvert.dictType)
                    ?: throw IllegalArgumentException("No provider found for dict type: ${dictConvert.dictType}")
                
                val reverseMapping = provider.getReverseMapping(dictConvert.dictType)
                val displayValue = reverseMapping[value]
                
                if (displayValue != null) {
                    field.set(obj, displayValue)
                }
            }
        } catch (e: Exception) {
            throw RuntimeException("Failed to convert field ${field.name} to display value", e)
        }
    }
    
    /**
     * 转换为目标类型
     */
    @Suppress("UNCHECKED_CAST")
    private fun <T> convertToTargetType(value: Any, targetClass: Class<T>): T {
        return when (targetClass) {
            String::class.java -> value.toString() as T
            Int::class.java, Integer::class.java -> value.toString().toInt() as T
            Long::class.java, java.lang.Long::class.java -> value.toString().toLong() as T
            Double::class.java, java.lang.Double::class.java -> value.toString().toDouble() as T
            Boolean::class.java, java.lang.Boolean::class.java -> value.toString().toBoolean() as T
            else -> value as T
        }
    }
}