package team.aikero.murmuration.service.node.task.openbridge

import org.springframework.stereotype.Component
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.toJson
import team.aikero.murmuration.util.requiredProperty
import team.aikero.murmuration.core.workflow.task.TaskCreationRetryableException
import team.aikero.murmuration.core.workflow.task.TaskResult
import team.aikero.murmuration.service.node.task.chaoji.GetTaskResultData
import tech.tiangong.fashion.aigc.image.common.enums.TextureImageTypeEnum.Companion.result
import java.util.*

@Component
@Suppress("SpringJavaInjectionPointsAutowiringInspection")
class OpenBridgeClient(val httpClient: OpenBridgeHttpClient) {

    /**
     * 敏感图检测
     *
     * @param imageUrl 图片URL
     * @return 是否为敏感图
     */
    fun isSensitiveImage(imageUrl: String): Boolean {
        val clientCode = requiredProperty<String>("open-bridge.clientCode")
        val riskLevelThreshold = requiredProperty<ImageModerationData.RiskLevel>("open-bridge.riskLevelThreshold")

        val response = httpClient.imageModeration(
            clientCode = clientCode,
            request = ImageModerationRequest(
                image = imageUrl,
                serviceList = listOf("baselineCheck_cb"),
                requestId = UUID.randomUUID().toString(),
            ),
        )

        val (resultList) = requireSuccessful(response)
        val (riskLevel, labels) = resultList.firstOrNull()
            ?: throw NullPointerException("HTTP 响应体 data.resultList 为空")

        val isSensitive = riskLevel.isHigherThan(riskLevelThreshold)
        if (isSensitive) {
            log.info { "图片[${imageUrl}]为敏感图: 风险等级=${riskLevel}, 标签=${labels?.toJson()}" }
        }

        return isSensitive
    }

    /**
     * 去除水印
     *
     * @param imageUrl 图片URL
     * @return 任务taskId
     */
    fun createRemoveWaterMark(imageUrl: String): String {
        val clientCode = requiredProperty<String>("open-bridge.clientCode")
        try {
            val response = httpClient.removeWatermark(
                clientCode = clientCode,
                request = RemoveWatermarkRequest(
                    imageUrl
                )
            )

            //qbs大于2的时候返回错误码429
            if (response.code == "429") {
                throw TaskCreationRetryableException(response)
            }

            val taskId = requireSuccessful(response)

            return taskId
        } catch (ex: Exception) {
            throw ex
        }
    }

    fun queryRemoveWatermark(taskId: String): TaskResult<List<String>> {
        val clientCode = requiredProperty<String>("open-bridge.clientCode")

        val response = httpClient.getRemoveWaterMarkResult(
            clientCode, taskId
        )

        val data = requireSuccessful(response)
        return when (data.status) {
            RemoveWatermarkData.Status.SUCCESS ->
                TaskResult.Completed(listOf(data.imageUrl!!))

            else ->
                TaskResult.Running
        }
    }

    private fun <T> requireSuccessful(response: Response<T>): T {
        if (!response.successful) {
            throw IllegalStateException("HTTP 响应体 successful 为 false: code=${response.code}, message=${response.message}")
        }

        if (response.data == null) {
            throw NullPointerException("HTTP 响应体 data 为空")
        }

        return response.data
    }
}
