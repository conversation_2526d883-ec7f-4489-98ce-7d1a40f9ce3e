package team.aikero.murmuration.service.node.task.aip.patternExtraction

import team.aikero.murmuration.service.node.task.aip.*

/**
 * 花型3.0-logo提取
 *
 * https://yapi.tiangong.site/project/192/interface/api/11650
 *
 * <AUTHOR>
 */
@AipExecutorIdentifier(AipAbility.PATTERN_EXTRACTION_V3)
class PatternExtractionExecutor(client: AipClient): AipExecutor<PatternExtractionInput, PatternExtractionOutput>(client) {

    override fun validateTaskOutput(output: PatternExtractionOutput) {
        if (output.resImgs.isEmpty()) {
            throw InvalidTaskOutputException("resImgs 数组为空")
        }
    }
}

data class PatternExtractionInput(
    /**
     * 参考图
     */
    val refImgUrl: String,

    /**
     * 生成数量
     */
    val count: Int,

    /**
     * 工作流名称
     */
    val taskId: String,
)

data class PatternExtractionOutput(
    /**
     * 结果图
     */
    val resImgs: List<String>,
)
