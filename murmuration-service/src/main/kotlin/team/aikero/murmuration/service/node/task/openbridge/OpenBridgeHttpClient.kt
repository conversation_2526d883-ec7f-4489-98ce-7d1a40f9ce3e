package team.aikero.murmuration.service.node.task.openbridge

import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestHeader
import org.springframework.web.service.annotation.GetExchange
import org.springframework.web.service.annotation.PostExchange
import team.aikero.murmuration.infra.client.RestClient

/**
 * 公共开发部代理接口
 * [docs](https://yapi.tiangong.site/project/174/interface/api)
 *
 * <AUTHOR>
 * @since 2025-07-17
 */
@RestClient("https://openbridge.tiangong.tech/open-bridge/api")
interface OpenBridgeHttpClient {

    /**
     * 图片检测
     *
     * https://yapi.tiangong.site/project/174/interface/api/7574
     */
    @PostExchange("/image/moderation")
    fun imageModeration(
        @RequestHeader("Client-Code") clientCode: String,
        @RequestBody request: ImageModerationRequest,
    ): Response<ImageModerationData>

    @PostExchange("/zuotang/remove-watermark")
    fun removeWatermark(
        @RequestHeader("Client-Code") clientCode: String,
        @RequestBody request: RemoveWatermarkRequest,
    ): Response<String>

    @GetExchange("/zuotang/remove-watermark/{taskId}")
    fun getRemoveWaterMarkResult(
        @RequestHeader("Client-Code") clientCode: String,
        @PathVariable taskId: String
    ): Response<RemoveWatermarkData>
}
