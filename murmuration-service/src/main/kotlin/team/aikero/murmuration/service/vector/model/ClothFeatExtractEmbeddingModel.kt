package team.aikero.murmuration.service.vector.model

import org.springframework.retry.RetryCallback
import org.springframework.retry.support.RetryTemplate
import org.springframework.stereotype.Component
import team.aikero.blade.oss.OssTemplate
import team.aikero.blade.oss.OssTemplateExt.transferFrom
import team.aikero.murmuration.core.workflow.task.TaskResult
import team.aikero.murmuration.service.node.task.aip.AipAbility
import team.aikero.murmuration.service.node.task.aip.vector.ClothFeatExtractExecutor
import team.aikero.murmuration.service.node.task.aip.vector.ClothFeatExtractInput
import team.aikero.murmuration.service.vector.EmbeddingModel
import team.aikero.murmuration.service.vector.RetryableException
import team.aikero.murmuration.service.vector.TaskFailedException
import java.net.URI
import java.time.Duration

/**
 * 服装向量提取 - 嵌入模型实现
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
@Component
class ClothFeatExtractEmbeddingModel(
    val executor: ClothFeatExtractExecutor,
    val ossTemplate: OssTemplate,
) : EmbeddingModel {

    override val ability = AipAbility.CLOTH_FEAT_EXTRACT

    private val retryTemplate = RetryTemplate.builder()
        .maxAttempts(50)
        .fixedBackoff(Duration.ofMillis(200))
        .retryOn(RetryableException::class.java)
        .build()

    override fun embed(url: String): List<Float> {
        // 这里需要转成内部OSS地址，不然算法调度平台那边会报错
        val ossUrl = ossTemplate.transferFrom(URI.create(url))
        val taskId = executor.createTask(ClothFeatExtractInput(ossUrl))

        return retryTemplate.execute(RetryCallback<List<Float>, RetryableException> {
            val result = executor.getTask(taskId)
            when (result) {
                is TaskResult.Running -> throw RetryableException()
                is TaskResult.Failed -> throw TaskFailedException(result.error)
                is TaskResult.Completed -> result.value.input_imgvector
            }
        })
    }
}
