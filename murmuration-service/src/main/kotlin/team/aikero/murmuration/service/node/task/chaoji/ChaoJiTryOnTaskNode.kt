package team.aikero.murmuration.service.node.task.chaoji

import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.Size
import org.babyfish.jimmer.sql.kt.KSqlClient
import team.aikero.murmuration.common.enums.task.ClothingType
import team.aikero.murmuration.common.req.task.ChaoJiTryOnRequest
import team.aikero.murmuration.core.Ability
import team.aikero.murmuration.core.Supplier
import team.aikero.murmuration.core.annotations.NodeIdentifier
import team.aikero.murmuration.core.annotations.NodeProperties
import team.aikero.murmuration.core.workflow.context.WorkflowNodeContext
import team.aikero.murmuration.core.workflow.node.builtin.SimpleTaskResult
import team.aikero.murmuration.core.workflow.node.builtin.TaskNode
import team.aikero.murmuration.core.workflow.node.param.Input
import team.aikero.murmuration.core.workflow.node.param.Output
import team.aikero.murmuration.core.workflow.node.param.Parameter
import team.aikero.murmuration.core.workflow.task.readTaskStorage
import team.aikero.murmuration.service.node.shared.Image
import team.aikero.murmuration.service.node.shared.StyleImage
import team.aikero.murmuration.service.node.shared.groupByKit

/**
 * 潮际虚拟换衣任务节点
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@NodeIdentifier("潮际虚拟换衣", Supplier.CHAO_JI, Ability.TRY_ON)
class ChaoJiTryOnTaskNode(val sql: KSqlClient) : TaskNode<ChaoJiTryOnInput, ChaoJiTryOnParameter, ChaoJiTryOnOutput>() {
    override fun createTask(context: WorkflowNodeContext, input: ChaoJiTryOnInput, parameter: ChaoJiTryOnParameter) {
        // 占坑匹配机制说明：
        // 同一个图案下，同一个图套的所有衣服，只能给一个模特穿
        // 先根据图案分组，每个组内部再根据图套分组
        val kits = input.clothingImages
            .groupBy { it.graphicImage }
            .mapValues { it.value.groupByKit() }
            .values
            .flatten()
        val modelImages = input.modelImages

        // 取两者的最小长度，保证不会越界
        val minSize = minOf(kits.size, modelImages.size)

        // 按顺序配对
        val pairs = (0 until minSize).map { i ->
            modelImages[i] to kits[i]
        }

        // 循环发起虚拟换衣任务
        for ((modelImage, kit) in pairs) {
            // 模特要和图套下的每一件衣服配对
            for (image in kit) {
                taskManager.createTask(
                    nodeInstanceId = context.node.id,
                    supplier = Supplier.CHAO_JI,
                    ability = Ability.TRY_ON,
                    request = ChaoJiTryOnRequest(
                        clothingImageUrl = image.getUrl(),
                        clothingImageType = parameter.clothingImageType,
                        modelImageUrl = modelImage.getUrl(),
                        n = parameter.n,
                    ),
                    // 存储任务ID和输入图片的关系
                    storage = image,
                )
            }
        }
    }

    override fun assembleOutput(context: WorkflowNodeContext, taskResults: List<SimpleTaskResult>): ChaoJiTryOnOutput {
        // 找回任务ID和输入图片的关系
        val storage = taskManager.readTaskStorage<StyleImage>(context.node.id)
        val images = taskResults.map {
            val image = storage[it.taskId] ?: throw IllegalStateException("找不到任务[${it.taskId}]对应的关系")
            // 将输入图片附带的信息继续传递到输出图片中，但替换其中的URL
            image.copy(imageUrl = it.url)
        }
        return ChaoJiTryOnOutput(images)
    }
}

data class ChaoJiTryOnInput(
    /**
     * 服装图
     */
    @NodeProperties(name = "款式图")
    @field:Size(min = 1)
    val clothingImages: List<StyleImage>,

    /**
     * 模特图
     */
    @NodeProperties(name = "模特图")
    @field:Size(min = 1)
    val modelImages: List<Image>,
) : Input

data class ChaoJiTryOnParameter(
    /**
     * 生图数量
     */
    @NodeProperties(name = "生图数量")
    @field:Min(1)
    @field:Max(8)
    val n: Int=4,

    /**
     * 服装图类型
     */
    @NodeProperties(name = "服装图类型")
    val clothingImageType: ClothingType= ClothingType.UPPER,
): Parameter

data class ChaoJiTryOnOutput(
    /**
     * 换衣结果
     */
    @NodeProperties(name = "换衣后款式图")
    val images: List<StyleImage>,
) : Output
