package team.aikero.murmuration.service.excel

import org.babyfish.jimmer.sql.Entity
import org.babyfish.jimmer.sql.ManyToOne
import team.aikero.blade.data.jimmer.entity.CreatedTime
import team.aikero.blade.data.jimmer.entity.LongId

/**
 * Excel行错误
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Entity
interface ExcelRowError: LongId, CreatedTime {

    /**
     * 导入日志
     */
    @ManyToOne
    val log: ExcelImportLog

    /**
     * 所在行号
     */
    val rowNum: Int

    /**
     * 错误原因
     */
    val reason: String
}
