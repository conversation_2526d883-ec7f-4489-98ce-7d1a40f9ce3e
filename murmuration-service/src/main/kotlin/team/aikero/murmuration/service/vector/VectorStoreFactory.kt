package team.aikero.murmuration.service.vector

import com.aliyun.dashvector.DashVectorClient
import org.springframework.stereotype.Component
import team.aikero.murmuration.common.enums.SearchDimension
import com.aliyun.dashvector.models.requests.CreateCollectionRequest
import com.aliyun.dashvector.proto.CollectionInfo
import com.aliyun.dashvector.proto.FieldType
import team.aikero.murmuration.service.node.task.aip.AipAbility
import team.aikero.murmuration.util.env

/**
 * 向量库工厂
 *
 * <AUTHOR>
 * @since 2025-08-01
 */
@Component
class VectorStoreFactory(val dashVectorClient: DashVectorClient) {

    /**
     * 获取基础向量库（专门用来存储图片向量值）
     */
    fun getBaseStore(ability: AipAbility): VectorStore {
        val collectionName = getBaseCollectionName(ability)
        return VectorStore(
            collection = dashVectorClient.get(collectionName),
            hasEnabledField = false,
        )
    }

    /**
     * 获取检索向量库（专门用来检测图片相似度）
     */
    fun getSearchStore(dimension: SearchDimension): VectorStore {
        val collectionName = getSearchCollectionName(dimension)
        return VectorStore(
            collection = dashVectorClient.get(collectionName),
            hasEnabledField = true,
        )
    }

    /**
     * 创建基础向量库
     */
    fun createBaseStore(ability: AipAbility, fieldSchemas: Map<String, FieldType> = emptyMap()) {
        val collectionName = getBaseCollectionName(ability)
        createStore(collectionName, fieldSchemas)
    }

    /**
     * 创建检索向量库
     */
    fun createSearchStore(dimension: SearchDimension, fieldSchemas: Map<String, FieldType> = emptyMap()) {
        val collectionName = getSearchCollectionName(dimension)
        createStore(collectionName, fieldSchemas)
    }

    /**
     * 创建向量库
     */
    private fun createStore(collectionName: String, fieldSchemas: Map<String, FieldType>) {
        val request = CreateCollectionRequest.builder()
            .name(collectionName)
            .dimension(1792)
            .metric(CollectionInfo.Metric.cosine)
            .dataType(CollectionInfo.DataType.FLOAT)
            .filedsSchema(fieldSchemas)
            .build()
        val response = dashVectorClient.create(request)
        if (!response.isSuccess) {
            throw IllegalStateException("创建向量库[${collectionName}]失败: code=${response.code}, message=${response.message}, requestId=${response.requestId}")
        }
    }

    /**
     * 获取基础向量库集合名称
     *
     * 格式：{环境}_base_{算法调度平台能力}
     * 示例：dev_base_cloth_feat_extract
     */
    private fun getBaseCollectionName(ability: AipAbility): String {
        return "${env}_base_${ability.name.lowercase()}"
    }

    /**
     * 获取检索向量库集合名称
     *
     * 格式：{环境}_search_{检索维度}
     * 示例：dev_search_muse_same_style
     */
    private fun getSearchCollectionName(dimension: SearchDimension): String {
        return "${env}_search_${dimension.name.lowercase()}"
    }
}
