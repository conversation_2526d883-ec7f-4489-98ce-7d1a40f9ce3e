package team.aikero.murmuration.service.node.task.lazada

import team.aikero.blade.oss.OssTemplate
import team.aikero.blade.oss.OssTemplateExt.transferFrom
import team.aikero.murmuration.common.req.task.LazadaTryOnRequest
import team.aikero.murmuration.core.Ability
import team.aikero.murmuration.core.Supplier
import team.aikero.murmuration.core.annotations.TaskIdentifier
import team.aikero.murmuration.core.workflow.task.TaskHandler
import team.aikero.murmuration.core.workflow.task.TaskHandlerResult
import team.aikero.murmuration.core.workflow.task.TaskResult
import java.net.URI

/**
 * Lazada 虚拟换衣任务处理器
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@TaskIdentifier(
    supplier = Supplier.LAZADA,
    ability = Ability.TRY_ON,
)
class LazadaTryOnTaskHandler(
    val client: LazadaClient,
    val ossTemplate: OssTemplate,
): TaskHandler<LazadaTryOnRequest, String> {

    override fun create(request: LazadaTryOnRequest): String {
        return client.tryOnCloth(
            TryOnClothRequest(
                clothImageUrl = request.clothingImageUrl,
                type = request.clothingImageType,
                batchSize = request.n,
                modelReferenceImageUrl = request.modelImageUrl,
            )
        )
    }

    override fun query(request: LazadaTryOnRequest, context: String): TaskResult<List<TaskHandlerResult>> {
        val taskStatus = client.getTaskStatus(context)
        return taskStatus.map { urls ->
            // 将外部图片转存到内部OSS
            val uris = urls.map(URI::create)
            val transferred = ossTemplate.transferFrom(uris)
            transferred.map(TaskHandlerResult::image)
        }
    }
}
