package team.aikero.murmuration.service.node.task.chaoji

import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestHeader
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.service.annotation.GetExchange
import org.springframework.web.service.annotation.PostExchange
import team.aikero.murmuration.infra.client.RestClient

/**
 * 潮际好麦标准API文档
 *
 * [docs](http://doc.metac-inc.com/marketing/open-api.html)
 */
@RestClient("https://open.metac-inc.com/api")
interface ChaoJiHttpClient {

    /**
     * 服饰抠图
     *
     * http://doc.metac-inc.com/marketing/open-api.html#u670du9970u62a0u56fe0a3ca20id3du670du9970u62a0u56fe3e203ca3e
     */
    @PostExchange
    fun segmentClothing(@RequestHeader headers: Map<String, String>, @RequestBody request: SegmentClothingRequest): Response<SegmentClothingData>

    /**
     * 虚拟换衣
     *
     * http://doc.metac-inc.com/marketing/open-api.html#u6a21u7279u8bd5u8863-u5e73u94fau4ebau53f0u8bd5u88630a3ca20id3du6a21u7279u8bd5u8863-u5e73u94fau4ebau53f0u8bd5u88633e203ca3e
     */
    @PostExchange
    fun tryOn(@RequestHeader headers: Map<String, String>, @RequestBody request: TryOnRequest): Response<Long>

    /**
     * 查询任务
     *
     * http://doc.metac-inc.com/marketing/open-api.html#u67e5u8be2u4efbu52a10a3ca20id3du67e5u8be2u4efbu52a13e203ca3e
     */
    @GetExchange
    fun getTaskResult(@RequestHeader headers: Map<String, String>, @RequestParam id: Long): Response<GetTaskResultData>
}
