package team.aikero.murmuration.service.excel.example

import team.aikero.murmuration.service.excel.annotation.DictConvert
import team.aikero.murmuration.service.excel.annotation.DictValueProvider
import team.aikero.murmuration.service.excel.converter.DictValueProviderRegistry

/**
 * 示例字典值提供者 - 性别字典
 */
class GenderDictProvider : DictValueProvider {
    override fun getDictMapping(dictType: String): Map<String, Any> {
        return when (dictType) {
            "gender" -> mapOf(
                "男" to 1,
                "女" to 2,
                "未知" to 0
            )
            else -> emptyMap()
        }
    }
    
    override fun getReverseMapping(dictType: String): Map<Any, String> {
        return when (dictType) {
            "gender" -> mapOf(
                1 to "男",
                2 to "女",
                0 to "未知"
            )
            else -> emptyMap()
        }
    }
    
    override fun getDropDownOptions(dictType: String): List<String> {
        return when (dictType) {
            "gender" -> listOf("男", "女", "未知")
            else -> emptyList()
        }
    }
}

/**
 * 示例字典值提供者 - 状态字典
 */
class StatusDictProvider : DictValueProvider {
    override fun getDictMapping(dictType: String): Map<String, Any> {
        return when (dictType) {
            "status" -> mapOf(
                "启用" to true,
                "禁用" to false
            )
            else -> emptyMap()
        }
    }
    
    override fun getReverseMapping(dictType: String): Map<Any, String> {
        return when (dictType) {
            "status" -> mapOf(
                true to "启用",
                false to "禁用"
            )
            else -> emptyMap()
        }
    }
    
    override fun getDropDownOptions(dictType: String): List<String> {
        return when (dictType) {
            "status" -> listOf("启用", "禁用")
            else -> emptyList()
        }
    }
}

/**
 * 示例字典值提供者 - 优先级字典
 */
class PriorityDictProvider : DictValueProvider {
    override fun getDictMapping(dictType: String): Map<String, Any> {
        return when (dictType) {
            "priority" -> mapOf(
                "高" to "HIGH",
                "中" to "MEDIUM",
                "低" to "LOW"
            )
            else -> emptyMap()
        }
    }
    
    override fun getReverseMapping(dictType: String): Map<Any, String> {
        return when (dictType) {
            "priority" -> mapOf(
                "HIGH" to "高",
                "MEDIUM" to "中",
                "LOW" to "低"
            )
            else -> emptyMap()
        }
    }
    
    override fun getDropDownOptions(dictType: String): List<String> {
        return when (dictType) {
            "priority" -> listOf("高", "中", "低")
            else -> emptyList()
        }
    }
}

/**
 * 示例数据类
 */
data class UserExample(
    var name: String? = null,
    
    @DictConvert(
        dictType = "gender",
        targetClass = Int::class,
        dropDownConfig = team.aikero.murmuration.service.excel.annotation.DropDownConfig(
            enabled = true,
            firstRow = 1,
            firstCol = 1,
            lastCol = 1
        )
    )
    var gender: Int? = null,
    
    @DictConvert(
        dictType = "status",
        targetClass = Boolean::class,
        dropDownConfig = team.aikero.murmuration.service.excel.annotation.DropDownConfig(
            enabled = true,
            firstRow = 1,
            firstCol = 2,
            lastCol = 2
        )
    )
    var status: Boolean? = null,
    
    @DictConvert(
        dictType = "priority",
        targetClass = String::class,
        dropDownConfig = team.aikero.murmuration.service.excel.annotation.DropDownConfig(
            enabled = true,
            firstRow = 1,
            firstCol = 3,
            lastCol = 3
        )
    )
    var priority: String? = null
)

/**
 * 字典提供者初始化器
 */
object DictProviderInitializer {
    fun init() {
        DictValueProviderRegistry.registerProvider("gender", GenderDictProvider())
        DictValueProviderRegistry.registerProvider("status", StatusDictProvider())
        DictValueProviderRegistry.registerProvider("priority", PriorityDictProvider())
    }
}