package team.aikero.murmuration.service.node.task.aip.aibox.text_edit

import com.fasterxml.jackson.annotation.JsonProperty
import team.aikero.murmuration.service.node.task.aip.AipAbility
import team.aikero.murmuration.service.node.task.aip.AipClient
import team.aikero.murmuration.service.node.task.aip.AipExecutor
import team.aikero.murmuration.service.node.task.aip.AipExecutorIdentifier
import team.aikero.murmuration.service.node.task.aip.aibox.smart_cutting_head.SmartCuttingHeadInput
import team.aikero.murmuration.service.node.task.aip.aibox.smart_cutting_head.SmartCuttingHeadOutput

/**
 * 文本编辑
 */
@AipExecutorIdentifier(AipAbility.TEXT_EDIT)
class TextEditExecutor(
    client: AipClient
) : AipExecutor<TextEditInput, TextEditOutput>(client) {

}

data class TextEditInput(
    @field:JsonProperty("image_url")
    val imageUrl: String,
    @field:JsonProperty("user_prompt")
    val userPrompt: String,
    @field:JsonProperty("batch_size")
    val count: Int,
    val aspectRatio: String
)

data class TextEditOutput(
    val result: List<String>
)