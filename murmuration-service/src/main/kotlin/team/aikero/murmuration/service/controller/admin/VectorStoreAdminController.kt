package team.aikero.murmuration.service.controller.admin

import com.aliyun.dashvector.DashVectorClient
import com.aliyun.dashvector.models.CollectionMeta
import com.aliyun.dashvector.proto.FieldType
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.ok
import team.aikero.murmuration.common.enums.SearchDimension
import team.aikero.murmuration.service.node.task.aip.AipAbility
import team.aikero.murmuration.service.vector.Document
import team.aikero.murmuration.service.vector.VectorStoreFactory

/**
 * 向量库管理服务
 *
 * <AUTHOR>
 * @since 2025-08-01
 */
@RestController
@RequestMapping("/admin/vector-store")
class VectorStoreAdminController(
    val vectorStoreFactory: VectorStoreFactory,
    val dashVectorClient: DashVectorClient,
) {

    /**
     * 初始化向量库
     */
    @PostMapping("/initCollections")
    fun initCollections(): DataResponse<Unit> {
        // 服装向量提取-基础向量库
        vectorStoreFactory.createBaseStore(AipAbility.CLOTH_FEAT_EXTRACT)
        // MUSE同款-检索向量库
        vectorStoreFactory.createSearchStore(SearchDimension.MUSE_SAME_STYLE, mapOf(
            "enabled" to FieldType.BOOL,
        ))
        // 侵权-检索向量库
        vectorStoreFactory.createSearchStore(SearchDimension.INFRINGEMENT, mapOf(
            "enabled" to FieldType.BOOL,
        ))

        return ok()
    }

    /**
     * 描述向量库
     *
     * @param collectionName 向量库名称
     */
    @GetMapping("/describeCollection")
    fun describeCollection(@RequestParam collectionName: String): DataResponse<CollectionMeta> {
        val response = dashVectorClient.describe(collectionName)
        return ok(response.output)
    }

    /**
     * 删除向量库
     *
     * @param collectionName 向量库名称
     */
    @PostMapping("/deleteCollection")
    fun deleteCollection(@RequestParam collectionName: String): DataResponse<Unit> {
        dashVectorClient.delete(collectionName)
        return ok()
    }

    /**
     * 删除检索向量
     *
     * @param dimension 检索维度
     * @param imageId 图片ID
     */
    @PostMapping("/deleteVector")
    fun deleteVector(@RequestParam dimension: SearchDimension, @RequestParam imageId: String): DataResponse<Unit> {
        val searchStore = vectorStoreFactory.getSearchStore(dimension)
        searchStore.deleteById(imageId)
        return ok()
    }

    /**
     * 查看检索向量
     */
    @GetMapping("/viewVector")
    fun viewVector(@RequestParam dimension: SearchDimension, @RequestParam imageId: String): DataResponse<Document?> {
        val searchStore = vectorStoreFactory.getSearchStore(dimension)
        val document = searchStore.findById(imageId)
        return ok(document)
    }
}
