package team.aikero.murmuration.service.component

import org.springframework.stereotype.Component
import team.aikero.murmuration.common.enums.SearchDimension
import team.aikero.murmuration.service.vector.Document
import team.aikero.murmuration.service.vector.EmbeddingModelRegistry
import team.aikero.murmuration.service.vector.VectorStore
import team.aikero.murmuration.service.vector.VectorStoreFactory
import team.aikero.murmuration.service.vector.urlHash

/**
 * 图片向量提取器
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Component
class ImageVectorFetcher(
    val vectorStoreFactory: VectorStoreFactory,
    val embeddingModelRegistry: EmbeddingModelRegistry,
) {

    /**
     * 获取向量（如果基础向量库中没有，则先提取向量）
     *
     * @param url 图片URL
     * @param dimension 检索维度
     * @return 向量
     */
    fun fetch(url: String, dimension: SearchDimension): List<Float> {
        val urlHash = urlHash(url)

        // 基础向量库
        val baseStore = getBaseStore(dimension)

        // 如果基础向量库中没有，则先提取向量
        val vector = baseStore.findById(urlHash)?.vector ?: extractVectorAndInsert(
            url = url,
            urlHash = urlHash,
            dimension = dimension,
        )
        return vector
    }

    /**
     * 提取向量并入库
     */
    private fun extractVectorAndInsert(url: String, urlHash: String, dimension: SearchDimension): List<Float> {
        // 提取向量
        val model = embeddingModelRegistry.findByDimension(dimension)
        val vector = model.embed(url)

        // 入库
        val baseStore = getBaseStore(dimension)
        val document = Document(
            id = urlHash,
            vector = vector,
        )
        baseStore.insert(document)

        return vector
    }

    /**
     * 获取基础向量库
     */
    fun getBaseStore(dimension: SearchDimension): VectorStore {
        val ability = embeddingModelRegistry.findAbilityByDimension(dimension)
        return vectorStoreFactory.getBaseStore(ability)
    }
}
