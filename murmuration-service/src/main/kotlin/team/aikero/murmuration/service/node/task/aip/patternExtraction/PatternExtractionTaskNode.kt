package team.aikero.murmuration.service.node.task.aip.patternExtraction

import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import team.aikero.murmuration.common.req.task.PatternExtractionRequest
import team.aikero.murmuration.core.Ability
import team.aikero.murmuration.core.Supplier
import team.aikero.murmuration.core.annotations.NodeIdentifier
import team.aikero.murmuration.core.annotations.NodeProperties
import team.aikero.murmuration.core.workflow.context.WorkflowNodeContext
import team.aikero.murmuration.core.workflow.node.builtin.SimpleTaskResult
import team.aikero.murmuration.core.workflow.node.param.Parameter
import team.aikero.murmuration.service.node.shared.ImageListInput
import team.aikero.murmuration.service.node.shared.ImageListOutput
import team.aikero.murmuration.service.node.shared.node.SmartGraphicStorehouseTaskNode
import team.aikero.murmuration.service.node.shared.graphic.GraphicStorehouseType

/**
 * 图案提取任务节点
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@NodeIdentifier(name = "图案提取", supplier = Supplier.AIP, ability = Ability.PATTERN_EXTRACTION)
class PatternExtractionTaskNode : SmartGraphicStorehouseTaskNode<ImageListInput, PatternExtractionParameter, ImageListOutput>() {

    override fun createTask(
        context: WorkflowNodeContext,
        input: ImageListInput,
        parameter: PatternExtractionParameter
    ) {
        for (image in input.images) {
            taskManager.createTask(
                nodeInstanceId = context.node.id,
                supplier = Supplier.AIP,
                ability = Ability.PATTERN_EXTRACTION,
                request = PatternExtractionRequest(
                    imageUrl = image.getUrl(),
                    n = parameter.n,
                ),
            )
        }
    }

    override fun assembleOutput(context: WorkflowNodeContext, taskResults: List<SimpleTaskResult>): ImageListOutput {
        val graphicImages = taskResults
            .map { detectAndConvert(it.url, GraphicStorehouseType.LOGO_IDENTIFY_IMAGE, null) }
            .saveToGraphicImage()

        return ImageListOutput(graphicImages)
    }
}

data class PatternExtractionParameter(
    /**
     * 生图数量
     */
    @NodeProperties(name = "生图数量", value = "2")
    @field:Max(8)
    @field:Min(1)
    val n: Int = 2,
) : Parameter
