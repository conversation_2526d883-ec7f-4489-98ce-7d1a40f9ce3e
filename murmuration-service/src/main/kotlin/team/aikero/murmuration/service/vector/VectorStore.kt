package team.aikero.murmuration.service.vector

import com.aliyun.dashvector.DashVectorCollection
import com.aliyun.dashvector.models.requests.FetchDocRequest
import com.aliyun.dashvector.models.responses.Response
import com.aliyun.dashvector.models.requests.InsertDocRequest
import com.aliyun.dashvector.models.requests.QueryDocRequest
import com.aliyun.dashvector.models.requests.DeleteDocRequest
import com.aliyun.dashvector.models.requests.UpdateDocRequest
import com.aliyun.dashvector.models.requests.UpsertDocRequest

/**
 * 向量库
 *
 * <AUTHOR>
 * @since 2025-08-01
 */
class VectorStore(
    val collection: DashVectorCollection,
    val hasEnabledField: Boolean,
) {

    /**
     * 根据ID获取文档
     */
    fun findById(id: String): Document? {
        val request = FetchDocRequest.builder().id(id).build()
        val response = collection.fetch(request)
        val output = requireSuccessful(response)
        return output[id]?.let(::Document)
    }

    /**
     * 插入文档
     */
    fun insert(document: Document) {
        val fields = if (hasEnabledField) mapOf("enabled" to true) else emptyMap()
        val doc = document.toDoc(fields)
        val request = InsertDocRequest.builder().doc(doc).build()
        val response = collection.insert(request)
        requireSuccessful(response)
    }

    /**
     * 删除文档
     */
    fun deleteById(id: String) {
        val request = DeleteDocRequest.builder().id(id).build()
        val response = collection.delete(request)
        requireSuccessful(response)
    }

    /**
     * 更新文档字段
     *
     * @param id 文档ID
     * @param fields 增量更新的字段
     */
    fun updateFields(id: String, fields: Map<String, Any>) {
        val document = findById(id) ?: throw IllegalStateException("文档不存在: $id")
        val doc = document.toDoc(fields)
        val request = UpdateDocRequest.builder().doc(doc).build()
        val response = collection.update(request)
        requireSuccessful(response)
    }

    /**
     * 检索topK文档
     */
    fun topK(vector: List<Float>, topK: Int): List<ScoredDocument> {
        val vector = newVector(vector)
        val builder = QueryDocRequest.builder()
            .vector(vector)
            .topk(topK)
            .includeVector(true)
        if (hasEnabledField) builder.filter("enabled = true")
        val response = collection.query(builder.build())
        val output = requireSuccessful(response)
        return output.map(::ScoredDocument)
    }

    /**
     * 检索top1相似度
     */
    fun top1Similarity(vector: List<Float>): Float {
        val documents = this.topK(vector, 1)
        return documents.firstOrNull()?.similarity ?: 0f
    }

    private fun <T> requireSuccessful(response: Response<T>): T {
        if (!response.isSuccess) {
            throw IllegalStateException("DashVector 响应 isSuccess 为 false: code=${response.code}, message=${response.message}, requestId=${response.requestId}")
        }

        if (response.output == null) {
            throw NullPointerException("DashVector 响应 output 为空")
        }

        return response.output
    }
}
