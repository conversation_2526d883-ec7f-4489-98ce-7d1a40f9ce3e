package team.aikero.murmuration.service.node.task.aip.vector

import team.aikero.murmuration.service.node.task.aip.AipAbility
import team.aikero.murmuration.service.node.task.aip.AipClient
import team.aikero.murmuration.service.node.task.aip.AipExecutor
import team.aikero.murmuration.service.node.task.aip.AipExecutorIdentifier
import team.aikero.murmuration.service.node.task.aip.InvalidTaskOutputException

@AipExecutorIdentifier(AipAbility.CLOTH_FEAT_EXTRACT)
class ClothFeatExtractExecutor(client: AipClient): AipExecutor<ClothFeatExtractInput, ClothFeatExtractOutput>(client) {

    override fun validateTaskOutput(output: ClothFeatExtractOutput) {
        if (output.input_imgvector.isEmpty()) {
            throw InvalidTaskOutputException("input_imgvector 数组为空")
        }
    }
}

data class ClothFeatExtractInput(val inputImage: String)
data class ClothFeatExtractOutput(val input_imgvector: List<Float>)
