package team.aikero.murmuration.service.controller.web

import org.babyfish.jimmer.sql.kt.KSqlClient
import org.springframework.web.bind.annotation.*
import team.aikero.blade.core.protocol.ComplexPageParam
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.PageVo
import team.aikero.blade.core.protocol.ok
import team.aikero.blade.data.jimmer.findPage
import team.aikero.murmuration.service.repository.MuseSameStyleImageRepository
import team.aikero.murmuration.service.vector.entity.dto.MuseSameStyleImagePageRequest
import team.aikero.murmuration.service.vector.entity.dto.MuseSameStyleImagePageVo

/**
 * MUSE同款图库服务
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@RestController
@RequestMapping("/web/muse-same-style-image")
class MuseSameStyleImageWebController(
    val sql: KSqlClient,
    val museSameStyleImageRepository: MuseSameStyleImageRepository,
) {

    /**
     * 分页查询
     */
    @PostMapping("/page")
    fun page(@RequestBody request: ComplexPageParam<MuseSameStyleImagePageRequest>): DataResponse<PageVo<MuseSameStyleImagePageVo>> {
        val page = sql.findPage(request, MuseSameStyleImagePageVo::class)
        return ok(page)
    }

    /**
     * 启用
     *
     * @param id 图片ID
     */
    @PostMapping("/enable")
    fun enable(@RequestParam id: Long): DataResponse<Unit> {
        museSameStyleImageRepository.updateEnabled(id, true)
        return ok()
    }

    /**
     * 停用
     *
     * @param id 图片ID
     */
    @PostMapping("/disable")
    fun disable(@RequestParam id: Long): DataResponse<Unit> {
        museSameStyleImageRepository.updateEnabled(id, false)
        return ok()
    }

    /**
     * 删除
     *
     * @param id 图片ID
     */
    @PostMapping("/delete")
    fun delete(@RequestParam id: Long): DataResponse<Unit> {
        museSameStyleImageRepository.delete(id)
        return ok()
    }

    /**
     * 批量删除
     *
     * @param ids 图片ID列表
     */
    @PostMapping("/deleteBatch")
    fun deleteBatch(@RequestBody ids: List<Long>): DataResponse<Unit> {
        ids.forEach(museSameStyleImageRepository::delete)
        return ok()
    }
}
