package team.aikero.murmuration.service.node.shared

import jakarta.validation.constraints.Size
import team.aikero.murmuration.core.annotations.NodeProperties
import team.aikero.murmuration.core.workflow.node.param.Input
import team.aikero.murmuration.core.workflow.node.param.Output

/**
 * 图片列表输入
 */
data class ImageListInput(
    /**
     * 图片列表
     */
    @NodeProperties(name = "图片列表")
    @field:Size(min = 1)
    val images: List<Image>
): Input
/**
 * 图片列表输出
 */
data class ImageListOutput(
    /**
     * 图片列表
     */
    @NodeProperties(name = "图片列表")
    val images: List<Image>
): Output
