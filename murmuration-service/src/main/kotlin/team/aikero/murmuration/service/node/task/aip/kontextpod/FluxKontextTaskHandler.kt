package team.aikero.murmuration.service.node.task.aip.kontextpod

import team.aikero.murmuration.common.req.task.KontextPodRequest
import team.aikero.murmuration.core.Ability
import team.aikero.murmuration.core.Supplier
import team.aikero.murmuration.core.annotations.TaskIdentifier
import team.aikero.murmuration.core.workflow.task.TaskHandler
import team.aikero.murmuration.core.workflow.task.TaskHandlerResult
import team.aikero.murmuration.core.workflow.task.TaskResult
import team.aikero.murmuration.service.component.ImageInfoFetcher
import java.math.BigDecimal
import java.math.RoundingMode

/**
 * 姿势裂变任务处理器
 */
@TaskIdentifier(
    supplier = Supplier.COMFY_UI,
    ability = Ability.POSTURAL_FISSION,
)
class FluxKontextTaskHandler(
    val fluxKontextExecutor: FluxKontextExecutor,
    val imageInfoFetcher: ImageInfoFetcher
) : TaskHandler<KontextPodRequest, String> {

    override fun create(request: KontextPodRequest): String {
        val (height, weight) = imageInfoFetcher.fetch(request.imageUrl)
        //比例 = 宽 / 高，找到最接近的比例
        val value = BigDecimal(weight).setScale(3, RoundingMode.HALF_UP) / BigDecimal(height)
        val aspectRatioValue = AspectRatio.similarValue(value.setScale(3, RoundingMode.HALF_UP))
        return fluxKontextExecutor.createTask(
            FluxKontextInput(
                images = request.imageUrl,
                prompt = request.prompt,
                aspectRatio = aspectRatioValue,
                workflowType = "liblib_API",
            )
        )
    }

    override fun query(request: KontextPodRequest, context: String): TaskResult<List<TaskHandlerResult>> {
        val taskResult = fluxKontextExecutor.getTask(context)
        return taskResult.map {
            val resultImageUrl = it.resImgs.first()
            listOf(TaskHandlerResult.image(resultImageUrl))
        }
    }

}

/**
 * 比例
 */
enum class AspectRatio(
    val value: String,
    val calculatedValue: BigDecimal,    //比例对应的数值
) {
    ONE_TO_ONE("1:1", BigDecimal("1.000")),
    TWO_TO_THREE("2:3", BigDecimal("0.667")),
    THREE_TO_TWO("3:2", BigDecimal("1.500")),
    THREE_TO_FOUR("3:4", BigDecimal("0.750")),
    FOUR_TO_THREE("4:3", BigDecimal("1.333")),
    NINE_TO_SIXTEEN("9:16", BigDecimal("0.563")),
    SIXTEEN_TO_NINE("16:9", BigDecimal("1.778")),
    NINE_TO_TWENTY_ONE("9:21", BigDecimal("0.429")),
    TWENTY_ONE_TO_NINE("21:9", BigDecimal("2.333"));

    companion object {
        fun similarValue(value: BigDecimal): String {
            return entries.toTypedArray()
                .minBy { aspectRatio ->
                    (aspectRatio.calculatedValue - value).abs()
                }.value
        }
    }
}
