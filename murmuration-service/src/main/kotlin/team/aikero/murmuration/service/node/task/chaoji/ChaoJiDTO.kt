package team.aikero.murmuration.service.node.task.chaoji

import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.annotation.JsonValue
import team.aikero.murmuration.common.enums.task.ClothingType

/**
 * 服饰抠图请求
 */
data class SegmentClothingRequest(
    /**
     * 款式图，url格式
     */
    val image: String,
    val method: String = "clothseg",

    /**
     * 服饰分割传参 'upper','lower','overall',默认值为'overall'
     */
    @JsonProperty("cate_token")
    val cateToken: ClothingType,
)

/**
 * 服饰抠图数据
 */
data class SegmentClothingData(
    @JsonProperty("image_mask")
    val imageMask: String,
)

/**
 * 虚拟换衣请求
 */
data class TryOnRequest(
    /**
     * 输入的服装图像，url格式
     */
    @JsonProperty("image_cloth")
    val imageCloth: String,

    /**
     * 针对image_cloth的mask，决定取其中哪一部分作为服饰输入，url格式
     */
    @JsonProperty("mask_cloth")
    val maskCloth: String? = null,

    /**
     * 输入的人物图像列表，成员为字符串，为对应试穿人物图像的url，目前只取第一个成员
     */
    @JsonProperty("list_images_human")
    val listImagesHuman: List<String>,

    /**
     * 字符串，可以有upper,lower,overall三种取值，组合换装时选overall，上装传image_cloth，下装传image_cloth1
     * 分别对应上半身，下半身，全身
     */
    @JsonProperty("cloth_length")
    val clothLength: ClothingType,

    /**
     * 生成图片数量 [1, 8]区间整数，默认8
     */
    @JsonProperty("batch_size")
    val batchSize: Int,
)

/**
 * 查询任务数据
 */
data class GetTaskResultData(
    /**
     * 状态
     */
    val status: Status,

    /**
     * 失败原因
     */
    val failReason: String?,

    /**
     * 任务产出详情
     */
    val marketingModelTaskOutputVOS: List<Output>,
) {
    /**
     * 状态
     */
    enum class Status(@JsonValue val value: Int) {
        /**
         * 处理中
         */
        PROCESSING(1),

        /**
         * 成功
         */
        SUCCESS(2),

        /**
         * 失败
         */
        FAILED(3),

        /**
         * 离线待调度
         */
        WAITING_FOR_SCHEDULE(5),

        /**
         * 部分失败
         */
        PARTIAL_FAILED(6),

        /**
         * 排队中
         */
        QUEUEING(7),
        ;
    }

    /**
     * 任务产出
     */
    data class Output(
        /**
         * 输出作品url
         */
        val workOutputUrl: String,
    )
}

data class Response<T>(
    val requestId: String?,
    val code: Long?,
    val message: String?,
    val data: T?,
    val succ: Boolean,
)
