package team.aikero.murmuration.service.controller.web

import org.babyfish.jimmer.sql.kt.KSqlClient
import org.babyfish.jimmer.sql.kt.ast.expression.eq
import org.springframework.web.bind.annotation.*
import team.aikero.blade.core.protocol.ComplexPageParam
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.PageVo
import team.aikero.blade.core.protocol.ok
import team.aikero.blade.data.jimmer.findPage
import team.aikero.blade.sequence.id.IdHelper
import team.aikero.murmuration.core.material.dto.*
import team.aikero.murmuration.service.material.entity.StyleManage
import team.aikero.murmuration.service.material.entity.id
import team.aikero.murmuration.service.node.task.youchuan.YouChuanClient

/**
 * 风格管理
 */
@RestController
@RequestMapping("/web/style/manage")
class StyleManageController(
    val sqlClient: KSqlClient,
    val client: YouChuanClient
) {

    /**
     * 分页列表
     */
    @PostMapping("/page")
    fun page(@RequestBody pageReq: ComplexPageParam<StyleManagePageReq>): DataResponse<PageVo<StyleManagePage>> {
        return ok(sqlClient.findPage(pageReq, StyleManagePage::class))
    }

    /**
     * 风格定义（新增和更新）
     */
    @PostMapping("/defined")
    fun defined(@RequestBody saveReq: StyleManageDefinedReq): DataResponse<Unit> {
        var moodboardId = saveReq.styleCode
        if (saveReq.id == null || moodboardId == null) {
            moodboardId = client.createMoodboard(saveReq.imageMaterial, saveReq.styleName)
        } else {
            client.updateMoodboard(moodboardId, saveReq.imageMaterial)
        }

        sqlClient.save(StyleManage {
            id = saveReq.id ?: IdHelper.getId()
            styleCode = moodboardId
            styleName = saveReq.styleName
            applicationBusiness = saveReq.applicationBusiness
            imageMaterial = saveReq.imageMaterial
        }).modifiedEntity

        return ok()
    }

    /**
     * 风格删除
     */
//    @DeleteMapping("/{id}")
//    @PreCheckPermission(name = "POD风格化-风格删除", value = ["murmuration.styleManage.delete"])
    fun delete(@PathVariable id: Long): DataResponse<Unit> {
        sqlClient.executeDelete(StyleManage::class) {
            where(table.id eq id)
        }
        return ok()
    }

    /**
     * 列表
     */
    @PostMapping("/list")
    fun list(@RequestBody req:StyleManageListReq): DataResponse<List<StyleManageListVo>> {
        return ok(
            sqlClient.findAll(StyleManageListVo::class){
                where(req)
            }
        )
    }
}
