package team.aikero.murmuration.service.controller.web

import org.babyfish.jimmer.spring.repository.orderBy
import org.babyfish.jimmer.sql.ast.mutation.AssociatedSaveMode
import org.babyfish.jimmer.sql.ast.mutation.SaveMode
import org.babyfish.jimmer.sql.kt.KSqlClient
import org.babyfish.jimmer.sql.kt.ast.expression.eq
import org.babyfish.jimmer.sql.kt.ast.expression.isNotNull
import org.babyfish.jimmer.sql.kt.ast.expression.valueIn
import org.babyfish.jimmer.sql.kt.fetcher.newFetcher
import org.springframework.retry.RetryCallback
import org.springframework.retry.support.RetryTemplate
import org.springframework.web.bind.annotation.*
import team.aikero.blade.core.exception.BusinessException
import team.aikero.blade.core.protocol.ComplexPageParam
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.PageVo
import team.aikero.blade.core.protocol.ok
import team.aikero.blade.data.jimmer.findPage
import team.aikero.blade.data.jimmer.orderBy
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.protocol.paging.toPageableOrDefaultSort
import team.aikero.murmuration.core.material.dto.*
import team.aikero.murmuration.core.workflow.entity.by
import team.aikero.murmuration.core.workflow.task.TaskResult
import team.aikero.murmuration.service.controller.exception.RetryableException
import team.aikero.murmuration.service.material.entity.*
import team.aikero.murmuration.service.node.task.aip.aibox.PoseExecutor
import team.aikero.murmuration.service.node.task.aip.aibox.PoseExtractionPromptInput
import team.aikero.murmuration.service.repository.PoseRepository
import java.time.Duration
import java.util.concurrent.Executors
import javax.xml.transform.stax.StAXResult

/**
 * 姿势组管理
 */
@RestController
@RequestMapping("/web/pose-group")
class PoseMangeController(
    private val sql: KSqlClient,
    private val poseRepository: PoseRepository,
    private val poseExecutor: PoseExecutor
) {

    // region pose group

    /**
     * 分页列表
     * @param req 分页参数请求
     */
    @PostMapping("/page")
    fun pagePoseGroup(
        @RequestBody req: ComplexPageParam<PoseGroupPageReq>
    ): DataResponse<PageVo<PoseGroupPage>> =
        ok(sql.findPage(req, PoseGroupPage::class))

    /**
     * 获取启用的系统姿势库
     */
    @PostMapping("/page/pose-library")
    fun getPoseLibrary(
        @RequestBody req: ComplexPageParam<PoseLibraryReq>
    ): DataResponse<PageVo<PoseLibrary>> {
        val pageable = req.toPageableOrDefaultSort()
        val result = sql.createQuery(PoseGroup::class) {
            where(req.filters)
            where(table.asEnabled eq true)
            orderBy(pageable)
            select(table.fetch(POSE_GROUP_LIBRARY_FETCHER))
        }.findPage(pageable)
        return ok(PageVo(result.pageNum, result.total, result.list.map { PoseLibrary(it) }))
    }

    /**
     * 通过id获取姿势组名
     */
    @GetMapping("/{id}")
    fun getPoseGroup(
        @PathVariable id: Long
    ): DataResponse<String> =
        ok(
            sql.createQuery(PoseGroup::class) {
                where(table.id eq id)
                select(table.poseGroupName)
            }.fetchOne()
        )


    /**
     * 新增姿势组
     *
     * @param req 新增姿势组请求
     */
    @PostMapping
    fun create(@RequestBody req: PoseGroupReq): DataResponse<Unit> {
        validateImageNumber(req.poses.size)
        val poses =
            sql.saveCommand(req, SaveMode.INSERT_ONLY, AssociatedSaveMode.APPEND)
                .execute(POSE_GROUP_EXTRACT_PROMPT_FETCHER)
                .modifiedEntity
                .poses
        batchUpdatePromptAsync(poses)
        return ok()
    }

    /**
     * 编辑姿势组
     *
     * @param req 编辑姿势组请求
     */
    @PutMapping("/{id}")
    fun update(@PathVariable id: Long, @RequestBody req: PoseGroupReq): DataResponse<Unit> {
        val originPoses = req.poses.filter { it.id != null }
        val modifiedPoses = req.poses.filterNot { it.id != null }

        sql.save(
            req.toEntity {
                this.id = id
                this.poses = originPoses.map { it.toEntity() }
            }, SaveMode.UPDATE_ONLY
        )

        val poses = sql.saveCommand(
            req.toEntity {
                this.id = id
                this.poses = modifiedPoses.map { it.toEntity() }
            }, SaveMode.UPDATE_ONLY, AssociatedSaveMode.APPEND
        )
            .execute(POSE_GROUP_EXTRACT_PROMPT_FETCHER)
            .modifiedEntity
            .poses

        batchUpdatePromptAsync(poses)
        return ok()
    }

    /**
     * 启用和关闭姿势组
     *
     * @param id 姿势组id
     * @param asEnable 是否启用
     */
    @PatchMapping("/{id}")
    fun checkEnable(
        @PathVariable id: Long,
        @RequestParam asEnable: Boolean
    ): DataResponse<Unit> {
        sql.createUpdate(PoseGroup::class) {
            set(table.asEnabled, asEnable)
            where(table.id eq id)
        }.execute()
        return ok()
    }

    /**
     * 删除姿势组
     *
     * @param id 姿势组id 路径参数
     */
    @DeleteMapping("/{id}")
    fun delete(@PathVariable id: Long): DataResponse<Unit> {
        sql.createDelete(PoseGroup::class) {
            where(table.id eq id)
        }.execute()
        return ok()
    }

    // endregion

    //region pose

    /**
     *
     * 根据姿势组id获取姿势组详情分页列表
     *  @param req 获取姿势组详情分页请求
     */
    @PostMapping("/pose/page")
    fun pagePose(
        @RequestBody req: ComplexPageParam<PosePageReq>
    ): DataResponse<PageVo<PosePage>> =
        ok(
            sql.findPage(req, PosePage::class)
        )

    /**
     * 添加单个图片
     *
     * @param req 添加单个图片请求
     */
    @PostMapping("/{groupId}/pose")
    fun createPose(@PathVariable groupId: Long, @RequestBody req: PoseCreateReq): DataResponse<Unit> {
        val pose =
            poseRepository
                .saveCommand(req.toEntity { this.poseGroupId = groupId }, SaveMode.INSERT_ONLY)
                .execute(POSE_EXTRACT_PROMPT_FETCHER)
                .modifiedEntity
        updatePromptAsync(pose)
        return ok()
    }

    /**
     * 编辑单个姿势
     *  @param req 编辑单个姿势请求
     */
    @PutMapping("/pose/{id}")
    fun updatePose(
        @PathVariable id: Long,
        @RequestBody req: PoseUpdateReq
    ): DataResponse<Unit> {
        if (req.asChange) {
            val pose =
                poseRepository
                    .saveCommand(req.toEntity {
                        this.id = id
                        this.identifyStatus = IdentifyStatus.PROCESSING
                    }, SaveMode.UPDATE_ONLY)
                    .execute(POSE_EXTRACT_PROMPT_FETCHER)
                    .modifiedEntity
            updatePromptAsync(pose)
        } else {
            if (req.prompt.isNullOrBlank()) {
                throw BusinessException("不允许为空")
            }
            poseRepository.save(
                req.toEntity {
                    this.id = id
                    this.prompt = req.prompt
                }, SaveMode.UPDATE_ONLY
            )
        }
        return ok()
    }

    /**
     * 根据id删除指定姿势
     * @param id 姿势id
     */
    @DeleteMapping("/pose/{id}")
    fun deletePose(@PathVariable id: Long): DataResponse<Unit> {
        sql.createDelete(Pose::class) {
            where(table.id eq id)
        }.execute()
        return ok()
    }

    /**
     * 批量删除姿势
     * @param ids 姿势id集合
     */
    @PostMapping("/pose/batch")
    fun deletePoseBatch(@RequestBody ids: List<Long>): DataResponse<Unit> {
        sql.createDelete(Pose::class) {
            where(table.id valueIn ids)
        }.execute()
        return ok()
    }

    // endregion

    private fun validateImageNumber(size: Int) {
        if (size > MAX_POSES_SIZE) {
            throw BusinessException("单组创建数量不能超过100张姿势图")
        }
    }

    private val retryTemplate = RetryTemplate.builder()
        .maxAttempts(30)
        .fixedBackoff(Duration.ofSeconds(6))
        .retryOn(RetryableException::class.java)
        .build()

    private val executor = Executors.newVirtualThreadPerTaskExecutor()

    private fun batchUpdatePromptAsync(poses: List<Pose>) {
        executor.execute {
            poses.forEach {
                executor.execute {
                    getTaskResult(it)
                }
            }
        }
    }

    private fun updatePromptAsync(pose: Pose) {
        executor.execute {
            getTaskResult(pose)
        }
    }

    private fun getTaskResult(originPose: Pose) {
        val taskId = poseExecutor.createTask(PoseExtractionPromptInput(originPose.imageUrl))
        log.info { "姿势提取任务${taskId}创建成功,姿势id为${originPose.id}" }
        val pose = retryTemplate.execute(RetryCallback<Pose, RetryableException> {
            when (val result = poseExecutor.getTask(taskId)) {
                is TaskResult.Running -> throw RetryableException()
                is TaskResult.Failed -> Pose {
                    this.id = originPose.id
                    this.identifyStatus = IdentifyStatus.FAILED
                }

                is TaskResult.Completed -> Pose {
                    this.id = originPose.id
                    this.prompt = result.value.prompt
                    this.identifyStatus = IdentifyStatus.SUCCEEDED
                }
            }
        })
        poseRepository.save(pose, SaveMode.UPDATE_ONLY)
        log.info { "姿势提取任务${taskId}完成,识别状态为${pose.identifyStatus},姿势id为${originPose.id}" }
    }

    companion object {
        private const val MAX_POSES_SIZE = 100
        private val POSE_GROUP_EXTRACT_PROMPT_FETCHER = newFetcher(PoseGroup::class).by {
            poses {
                imageUrl()
            }
        }
        private val POSE_EXTRACT_PROMPT_FETCHER = newFetcher(Pose::class).by {
            imageUrl()
        }
        private val POSE_GROUP_LIBRARY_FETCHER = newFetcher(PoseGroup::class).by {
            poseGroupName()
            poses({
                filter {
                    where(table.identifyStatus eq IdentifyStatus.SUCCEEDED)
                }
            }) {
                prompt()
                imageUrl()
            }
        }
    }

}