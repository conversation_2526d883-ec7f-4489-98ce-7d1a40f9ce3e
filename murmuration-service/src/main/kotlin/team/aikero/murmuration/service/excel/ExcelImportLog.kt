package team.aikero.murmuration.service.excel

import org.babyfish.jimmer.sql.Entity
import org.babyfish.jimmer.sql.OneToMany
import team.aikero.blade.data.jimmer.entity.CreatedTime
import team.aikero.blade.data.jimmer.entity.Creator
import team.aikero.blade.data.jimmer.entity.LongId
import team.aikero.blade.data.jimmer.entity.RevisedTime

/**
 * Excel导入日志
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Entity
interface ExcelImportLog : LongId, CreatedTime, Creator, RevisedTime {

    /**
     * 导入文件URL
     */
    val url: String

    /**
     * 导入场景
     */
    val scene: ExcelImportScene

    /**
     * 导入状态
     */
    val status: ExcelImportStatus

    /**
     * 行错误
     */
    @OneToMany(mappedBy = "log")
    val rowErrors: List<ExcelRowError>
}

/**
 * Excel导入场景
 */
enum class ExcelImportScene {
    /**
     * 侵权图库
     */
    INFRINGEMENT_IMAGE
}

/**
 * Excel导入状态
 */
enum class ExcelImportStatus {
    PROCESSING,
    DONE,
    FAILED,
}
