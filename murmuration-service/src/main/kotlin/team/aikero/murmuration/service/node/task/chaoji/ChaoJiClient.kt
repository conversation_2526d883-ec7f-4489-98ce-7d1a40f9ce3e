package team.aikero.murmuration.service.node.task.chaoji

import org.apache.commons.codec.digest.DigestUtils
import org.springframework.stereotype.Component
import team.aikero.murmuration.common.enums.task.ClothingType
import team.aikero.murmuration.util.requiredProperty
import team.aikero.murmuration.core.workflow.task.TaskResult
import team.aikero.murmuration.service.node.task.chaoji.GetTaskResultData.Status
import java.util.*

@Component
@Suppress("SpringJavaInjectionPointsAutowiringInspection")
class ChaoJiClient(val httpClient: ChaoJiHttpClient) {

    /**
     * 服饰抠图
     *
     * @param imageUrl 图片URL
     * @param cateToken 类别
     * @return 抠图结果URL
     */
    fun segmentClothing(
        imageUrl: String,
        cateToken: ClothingType,
    ): String {
        val headers = makeHeaders("marketing_algorithm_cloth_seg")
        val response = httpClient.segmentClothing(
            headers,
            SegmentClothingRequest(
                image = imageUrl,
                cateToken = cateToken,
            )
        )

        val data = requireSuccessful(response)
        return data.imageMask
    }

    /**
     * 虚拟换衣
     *
     * @param clothingImageUrl 服装图URL
     * @param clothingType 服装类型
     * @param modelImageUrl 模特图URL
     * @param batchSize 生图数量
     * @return 任务ID
     */
    fun tryOn(
        clothingImageUrl: String,
        clothingType: ClothingType,
        modelImageUrl: String,
        batchSize: Int,
    ): Long {
        check(batchSize in 1..8) { "生成数量必须在1-8之间" }
        val headers = makeHeaders("marketing_algorithm_flat_lay_mannequin_tryon")
        val response = httpClient.tryOn(
            headers,
            TryOnRequest(
                imageCloth = clothingImageUrl,
                listImagesHuman = listOf(modelImageUrl),
                clothLength = clothingType,
                batchSize = batchSize,
            )
        )
        return requireSuccessful(response)
    }

    /**
     * 查询任务
     *
     * @param taskId 任务ID
     * @return 任务结果(URL数组)
     */
    fun getTaskResult(taskId: Long): TaskResult<List<String>> {
        val headers = makeHeaders("marketing_model_task_fetchWithMarketingModelTaskOutputPO_id")
        val response = httpClient.getTaskResult(headers, taskId)
        val data = requireSuccessful(response)
        return when (data.status) {
            Status.PROCESSING, Status.WAITING_FOR_SCHEDULE, Status.QUEUEING -> TaskResult.Running
            Status.SUCCESS -> {
                val urls = data.marketingModelTaskOutputVOS.map { it.workOutputUrl }
                TaskResult.Completed(urls)
            }
            Status.FAILED, Status.PARTIAL_FAILED -> TaskResult.Failed(data.failReason)
        }
    }

    private fun makeHeaders(apiName: String): Map<String, String> {
        val appKey = "marketing-server"
        val accessKey = requiredProperty<String>("chaoji.accessKey")
        val secret = requiredProperty<String>("chaoji.secret")
        val timestamp = System.currentTimeMillis()

        val sign = DigestUtils.md5Hex("${appKey}${accessKey}${timestamp}${secret}")

        return mapOf(
            "apiName" to apiName,
            "requestId" to UUID.randomUUID().toString(),
            "appKey" to appKey,
            "accessKey" to accessKey,
            "timestamp" to "$timestamp",
            "sign" to sign,
        )
    }

    private fun <T> requireSuccessful(response: Response<T>): T {
        if (!response.succ) {
            throw IllegalStateException("HTTP 响应体 succ 为 false: code=${response.code}, message=${response.message}, requestId=${response.requestId}")
        }

        if (response.data == null) {
            throw NullPointerException("HTTP 响应体 data 为空")
        }

        return response.data
    }
}
