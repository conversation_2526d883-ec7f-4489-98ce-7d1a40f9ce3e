package team.aikero.murmuration.service.node.task.aip

import com.fasterxml.jackson.databind.node.ObjectNode
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.service.annotation.PostExchange
import team.aikero.murmuration.infra.client.RestClient

/**
 * 算法调度平台HTTP接口
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@RestClient("\${domain.api.aip:}")
interface AipHttpClient {

    /**
     * 创建任务
     *
     * https://yapi.tiangong.site/project/47/interface/api/2716
     */
    @PostExchange("/aigc-scheduler-server/aigc/v1/task/createAiTask")
    fun createAiTask(@RequestBody request: CreateAiTaskRequest<*>): Response<CreateAiTaskData>

    /**
     * 查询任务
     *
     * https://yapi.tiangong.site/project/47/interface/api/2722
     */
    @PostExchange("/aigc-scheduler-server/aigc/v1/task/getAiTask")
    fun getAiTask(@RequestBody request: GetAiTaskRequest): Response<GetAiTaskData<ObjectNode>>
}
