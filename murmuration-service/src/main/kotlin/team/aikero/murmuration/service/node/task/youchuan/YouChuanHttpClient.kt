package team.aikero.murmuration.service.node.task.youchuan

import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestHeader
import org.springframework.web.service.annotation.GetExchange
import org.springframework.web.service.annotation.PostExchange
import org.springframework.web.service.annotation.PutExchange
import team.aikero.murmuration.infra.client.RestClient

/**
 * Midjourney 中国实验室【悠船】
 *
 * [docs](https://docs.youchuan.cn/v1/tob/docs)
 *
 * 目前仅支持 v6 | v6.1 | niji 6 | v7模型
 *
 * 每次调用需通过header方式传递授权信息
 *
 * x-youchuan-app:{机构号}
 * x-youchuan-secret:{授权码}
 *
 * 提示词参数使用请参考 https://wp.youchuan.cn/%e6%8e%92%e5%88%97%e6%8f%90%e7%a4%ba/
 *
 *  暂不支持 repeat/personalize/{}排列提示词
 * 【高级编辑/转绘】任务仅支持【高清】操作
 *
 * <AUTHOR>
 */
@RestClient("\${domain.api.youchuan:https://ali.youchuan.cn}")
interface YouChuanHttpClient {

    /**
     * 文生图
     *
     * https://docs.youchuan.cn/v1/tob/docs#tag/API/operation/api_Diffusion
     */
    @PostExchange("/v1/tob/diffusion")
    fun diffusion(
        @RequestHeader("x-youchuan-app") app: String,
        @RequestHeader("x-youchuan-secret") secret: String,
        @RequestBody request: DiffusionRequest,
    ): JobInfo

    /**
     * 查询任务信息
     *
     * https://docs.youchuan.cn/v1/tob/docs#tag/%E8%B4%A6%E6%88%B7%E7%AE%A1%E7%90%86/operation/api_QueryJob
     */
    @GetExchange("/v1/tob/job/{jobId}")
    fun getJobInfo(
        @RequestHeader("x-youchuan-app") app: String,
        @RequestHeader("x-youchuan-secret") secret: String,
        @PathVariable jobId: String,
    ): JobInfo

    /**
     * 创建Moodboard
     *
     * https://docs.youchuan.cn/v1/tob/docs#tag/Moodboard/operation/api_CreateMoodboard
     */
    @PostExchange("/v1/tob/moodboard")
    fun createMoodboard(
        @RequestHeader("x-youchuan-app") app: String,
        @RequestHeader("x-youchuan-secret") secret: String,
        @RequestBody request: MoodboardRequest,
    ): MoodboardInfo

    /**
     * 更新Moodboard
     *
     * https://docs.youchuan.cn/v1/tob/docs#tag/Moodboard/operation/api_UpdateMoodboard
     */
    @PutExchange("/v1/tob/moodboard/{id}")
    fun updateMoodboard(
        @RequestHeader("x-youchuan-app") app: String,
        @RequestHeader("x-youchuan-secret") secret: String,
        @PathVariable id: String,
        @RequestBody request: MoodboardRequest,
    ): MoodboardInfo

    /**
     * Moodboard分页列表
     *
     * https://docs.youchuan.cn/v1/tob/docs#tag/Moodboard/operation/api_ListMoodboards
     */
    @GetExchange("/v1/tob/moodboards")
    fun pageMoodboard(
        @RequestHeader("x-youchuan-app") app: String,
        @RequestHeader("x-youchuan-secret") secret: String,
        request: MoodboardPageRequest,
    ): MoodboardPageResponse
}
