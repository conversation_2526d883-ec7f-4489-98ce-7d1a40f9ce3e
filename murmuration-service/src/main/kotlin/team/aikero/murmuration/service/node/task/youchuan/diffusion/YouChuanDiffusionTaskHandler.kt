package team.aikero.murmuration.service.node.task.youchuan.diffusion

import org.springframework.stereotype.Component
import team.aikero.blade.core.toolkit.isNotBlank
import team.aikero.blade.core.toolkit.isNotEmpty
import team.aikero.blade.oss.OssTemplate
import team.aikero.blade.oss.OssTemplateExt.transferFrom
import team.aikero.murmuration.common.req.task.YouChuanDiffusionRequest
import team.aikero.murmuration.core.Ability
import team.aikero.murmuration.core.Supplier
import team.aikero.murmuration.core.annotations.TaskIdentifier
import team.aikero.murmuration.core.workflow.task.TaskHandler
import team.aikero.murmuration.core.workflow.task.TaskHandlerResult
import team.aikero.murmuration.core.workflow.task.TaskResult
import team.aikero.murmuration.service.node.task.youchuan.YouChuanClient
import java.net.URI

/**
 * 悠船文生图任务处理器
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Component
class YouChuanDiffusionTaskHandler(
    val client: YouChuanClient,
    val ossTemplate: OssTemplate,
): TaskHandler<YouChuanDiffusionRequest, String> {

    override fun create(request: YouChuanDiffusionRequest): String {
        var prompt = request.prompt
        val bottomImageUrls = buildList {
            request.referenceImageUrl.takeIf { it.isNotBlank() }?.let(::add)
            request.bottomImageUrls?.let { addAll(it) }
        }

        // 拼接底图URL
        if (bottomImageUrls.isNotEmpty()) {
            prompt = "${bottomImageUrls.joinToString(" ")} $prompt"
        }

        // 拼接风格ID
        if (request.moodboardId.isNotBlank()) {
            prompt += " --p ${request.moodboardId}"
        }

        // 拼接参照风格图URL
        if (request.referenceStyleImageUrls.isNotEmpty()) {
            prompt += " --sref ${request.referenceStyleImageUrls!!.joinToString(" ")}"
        }

        // 拼接参照角色/万物图URL，版本=7则拼接参照万物参数，否则拼接参照角色参数
        if (request.referenceRoleImageUrls.isNotEmpty()) {
            prompt += if (isVersion7(prompt)) " --oref " else " --cref "
            prompt += request.referenceRoleImageUrls!!.joinToString(" ")
        }

        return client.diffusion(prompt)
    }

    override fun query(request: YouChuanDiffusionRequest, context: String): TaskResult<List<TaskHandlerResult>> {
        val jobResult = client.getJobResult(context)
        return jobResult.map { urls ->
            // 将外部图片转存到内部OSS
            val uris = urls.map(URI::create)
            val transferred = ossTemplate.transferFrom(uris)
            transferred.map(TaskHandlerResult::image)
        }
    }

    /**
     * 判断提示词中的版本是否=7
     */
    private fun isVersion7(prompt: String): Boolean {
        val regex = Regex("""--v\s*(\d+)""")
        val version = regex.findAll(prompt)
            .lastOrNull()
            ?.groupValues
            ?.get(1)
        return version == "7"
    }
}

/**
 * 悠船图案衍生任务处理器
 */
@TaskIdentifier(
    supplier = Supplier.MIDJOURNEY,
    ability = Ability.IMAGE_DERIVATION,
)
class YouChuanImageDerivationTaskHandler(delegate: YouChuanDiffusionTaskHandler) : TaskHandler<YouChuanDiffusionRequest, String> by delegate

/**
 * 悠船模特图衍生任务处理器
 */
@TaskIdentifier(
    supplier = Supplier.MIDJOURNEY,
    ability = Ability.MODEL_IMAGE_DERIVATION,
)
class YouChuanModelImageDerivationTaskHandler(delegate: YouChuanDiffusionTaskHandler) : TaskHandler<YouChuanDiffusionRequest, String> by delegate
