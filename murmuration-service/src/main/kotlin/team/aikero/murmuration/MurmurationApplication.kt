package team.aikero.murmuration

import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.runApplication
import org.springframework.cache.annotation.EnableCaching
import org.springframework.cloud.openfeign.EnableFeignClients
import org.springframework.scheduling.annotation.EnableAsync

@EnableAsync
@EnableCaching
@EnableFeignClients(basePackages = [
    "team.aikero.*",
    "tech.tiangong.digital.print.client",
])
@SpringBootApplication(scanBasePackages = ["team.aikero.murmuration"])
class MurmurationApplication

fun main(args: Array<String>) {
    runApplication<MurmurationApplication>(*args)
}
