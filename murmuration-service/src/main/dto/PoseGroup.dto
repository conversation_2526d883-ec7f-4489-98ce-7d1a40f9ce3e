export team.aikero.murmuration.service.material.entity.PoseGroup
    -> package team.aikero.murmuration.core.material.dto

specification PoseGroupPageReq {
    like(poseGroupName)
    applicationScenarios
    asEnabled
    creatorId
    ge(createdTime)
    le(createdTime)
}

PoseGroupPage {
    id
    poseGroupName
    applicationScenarios
    asEnabled
    creatorId
    creatorName
    createdTime
    poses{
        id
        imageUrl
    }
}

input PoseGroupReq {
    poseGroupName
    applicationScenarios
    poses {
        id?
        imageUrl
    }
}

specification PoseLibraryReq{
    applicationScenarios
}

PoseLibrary{
    id
    poseGroupName
    poses{
        id
        prompt
        imageUrl
    }
}
