<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="60 seconds" debug="false">
    <property name="encoding" value="utf-8"/>
    <!-- console 日志输出格式 由于目前OpenTelemetry只接入了trace  链路依靠日志关联打印输出  [trace_id - %X{trace_id}] [span_id - %X{span_id}] -->
    <property name="consoleOutputPattern"
              value="[%d{yyyy-MM-dd HH:mm:ss.SSS}] [%-5level] [trace_id - %X{trace_id}] [span_id - %X{span_id}] [node_id - %X{node_id}] [task_id - %X{task_id}] [%c] - %msg%n"/>
    <!-- http://logback.qos.ch/manual/layouts.html#customConversionSpecifier -->
    <!-- 控制输出字符数 -->
    <!--    <conversionRule conversionWord="limitedMessage"-->
    <!--                    converterClass="team.aikero.blade.logging.converter.LimitedMessageClassicConverter"/>-->
    <springProperty scope="context" name="loggingOutputLen" source="logging.outputLen" defaultValue="0"/>
    <!--输出到控制台 -->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${consoleOutputPattern}</pattern>
            <charset>${encoding}</charset>
        </encoder>
    </appender>
    <root level="info">
        <appender-ref ref="console"/>
    </root>
</configuration>
