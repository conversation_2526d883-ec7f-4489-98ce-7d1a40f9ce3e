package team.aikero.murmuration.component

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import team.aikero.murmuration.MurmurationApplication
import team.aikero.murmuration.service.component.DictService

@DisplayName("字典服务测试")
@SpringBootTest(classes = [MurmurationApplication::class])
class DictServiceTest(@Autowired val dictService: DictService) {

    @Test
    @DisplayName("校验成功")
    fun validateSuccessfully() {
        dictService.checkDictCode("supply_mode", "Manufacturer")
    }

    @Test
    @DisplayName("校验失败")
    fun validateFailed() {
        val exception = assertThrows<IllegalArgumentException> {
            dictService.checkDictCode("supply_mode", "InvalidCode")
        }
        assertThat(exception.message).isEqualTo("字典编码[InvalidCode]不存在")
    }
}
