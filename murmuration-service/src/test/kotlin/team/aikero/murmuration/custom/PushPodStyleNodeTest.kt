package team.aikero.murmuration.custom

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.mockito.kotlin.mock
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import team.aikero.blade.auth.withSystemUser
import team.aikero.murmuration.MurmurationApplication
import team.aikero.murmuration.core.workflow.context.WorkflowNodeContext
import team.aikero.murmuration.core.workflow.node.param.Parameter
import team.aikero.murmuration.service.node.normal.PushPodStyleInput
import team.aikero.murmuration.service.node.normal.PushPodStyleNode
import team.aikero.murmuration.service.node.shared.GraphicImage
import team.aikero.murmuration.service.node.shared.StyleImage
import team.aikero.murmuration.service.node.shared.TemplateImage

@Disabled("开发阶段使用")
@SpringBootTest(classes = [MurmurationApplication::class])
class PushPodStyleNodeTest(@Autowired val node: PushPodStyleNode) {

    @Test
    @DisplayName("推送POD选款成功")
    fun executeSuccessfully() = withSystemUser {
        val context = mock<WorkflowNodeContext>()
        val input = PushPodStyleInput(
            images = listOf(
                StyleImage(
                    imageUrl = "https://chuangxin-oss-cdn.tiangong.tech/16402d6103c33041a5aa2074cb441d69.jpg",
                    graphicImage = GraphicImage(7343461249546985476),
                    templateImage = TemplateImage(7343460719512788994),
                )
            )
        )
        val result = node.execute(context, input, Parameter.Empty)
        assertThat(result).matches { it.isCompleted() }
    }
}
