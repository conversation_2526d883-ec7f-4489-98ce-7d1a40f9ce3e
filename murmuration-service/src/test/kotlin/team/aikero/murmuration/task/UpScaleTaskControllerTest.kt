package team.aikero.murmuration.task

import org.assertj.core.api.Assertions.assertThat
import org.awaitility.Awaitility.await
import org.babyfish.jimmer.sql.kt.KSqlClient
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import team.aikero.blade.auth.withSystemUser
import team.aikero.blade.util.json.parseJson
import team.aikero.murmuration.MurmurationApplication
import team.aikero.murmuration.common.enums.task.Resolution
import team.aikero.murmuration.common.req.task.UpScaleRequest
import team.aikero.murmuration.core.workflow.entity.TaskInstance
import team.aikero.murmuration.core.workflow.entity.TaskStatus
import team.aikero.murmuration.event.listener.TaskStatusNotification
import team.aikero.murmuration.infra.rocketmq.AliyunRocketMQClient
import team.aikero.murmuration.service.controller.task.inner.UpScaleTaskController
import java.util.*
import java.util.concurrent.TimeUnit

@DisplayName("超分任务Controller测试")
@SpringBootTest(classes = [MurmurationApplication::class])
class UpScaleTaskControllerTest(
    @Autowired val controller: UpScaleTaskController,
    @Autowired val sql: KSqlClient,
    @Autowired val rocketMQClient: AliyunRocketMQClient,
) {

    @Test
    @DisplayName("创建任务成功")
    fun createTaskSuccessfully() = withSystemUser {
        val bizId = UUID.randomUUID().toString()
        val bizType = "UNIT_TEST"
        val response = controller.createTask(
            bizId = bizId,
            bizType = bizType,
            request = UpScaleRequest(
                "https://chuangxin-oss-cdn.tiangong.tech/08d226a46d0f31e1881ff56caddc7682.png",
                Resolution.R_2K,
            )
        )
        assertThat(response.successful).isTrue()
        val taskId = response.data!!

        // 等待任务成功
        await()
            .atMost(30, TimeUnit.SECONDS)
            .pollDelay(2, TimeUnit.SECONDS)
            .pollInterval(2, TimeUnit.SECONDS)
            .until {
                rocketMQClient.getMessageByKey("$taskId") != null
            }

        // 拉取MQ消息
       val payload = rocketMQClient.getMessageByKey("$taskId")!!.parseJson<TaskStatusNotification>()
       assertThat(payload.taskId).isEqualTo(taskId)
       assertThat(payload.bizId).isEqualTo(bizId)
       assertThat(payload.bizType).isEqualTo(bizType)
       assertThat(payload.status).isEqualTo(TaskStatus.COMPLETED)
       assertThat(payload.results).hasSize(1)
    }
}
