package team.aikero.murmuration.vector

import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import team.aikero.murmuration.MurmurationApplication
import team.aikero.murmuration.service.vector.model.ClothFeatExtractEmbeddingModel
import kotlin.time.measureTime

@DisplayName("服装向量提取嵌入模型测试")
@SpringBootTest(classes = [MurmurationApplication::class])
class ClothFeatExtractEmbeddingModelTest(@Autowired val model: ClothFeatExtractEmbeddingModel) {

    @Test
    @DisplayName("提取向量成功")
    fun extractVectorsSuccessfully() {
        repeat(20) {
            Thread.ofVirtual().start {
                val duration = measureTime {
                    model.embed("https://chuangxin-oss-cdn.tiangong.tech/tiangong_e2b3370dfbde4a13936e016c19c3d648.png")
                }
                println("请求耗时：${duration.inWholeMilliseconds}ms")
            }
        }
    }
}
