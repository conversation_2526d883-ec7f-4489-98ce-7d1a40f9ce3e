package team.aikero.murmuration

import org.awaitility.Awaitility
import org.babyfish.jimmer.sql.kt.KSqlClient
import org.babyfish.jimmer.sql.kt.ast.expression.eq
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.annotation.DirtiesContext
import team.aikero.blade.auth.withUser
import team.aikero.blade.sequence.id.IdHelper
import team.aikero.blade.user.entity.CurrentUser
import team.aikero.murmuration.controller.web.NodeDefinitionArgs
import team.aikero.murmuration.core.Ability
import team.aikero.murmuration.core.Supplier
import team.aikero.murmuration.core.workflow.engine.GraphEngine
import team.aikero.murmuration.core.workflow.entity.*
import team.aikero.murmuration.core.workflow.node.param.GenericData
import team.aikero.murmuration.service.node.shared.ImageListInput
import team.aikero.murmuration.service.node.shared.SimpleImage
import java.util.concurrent.TimeUnit

/**
 * 图引擎集成测试
 *
 * <AUTHOR>
 */
@SpringBootTest(classes = [MurmurationApplication::class])
@DirtiesContext
class GraphEngineTest {

    @Autowired
    lateinit var graphEngine: GraphEngine

    @Autowired
    lateinit var sqlClient: KSqlClient

    /**
     * 测试工作流定义
     */
    @Test
    @Disabled
    fun `test workflow definition`() = withUser(CurrentUser(1, "test", "test", 2, 1)) {
        sqlClient.insert(workflowDefinition(IdHelper.getId()))
        sqlClient.insert(workflowDefinition1(IdHelper.getId()))
        sqlClient.insert(workflowDefinition2(IdHelper.getId()))
        sqlClient.insert(workflowDefinition3(IdHelper.getId()))
    }

    /**
     * 测试工作流执行
     */
    @Test
    @Disabled
    fun `test workflow execution`() = withUser(CurrentUser(1, "test", "test", 2, 1)) {

        // 启动工作流
        val workflowInstanceId = graphEngine.startWorkflow(
            workflowDefinitionId = 7344304503656898561,
            input = GenericData.Companion.from(ImageListInput(listOf(
                SimpleImage("https://chuangxin-oss-cdn.tiangong.tech/tiangong_950f7a71a6cb479488027f5e3766363f.jpg"),
                SimpleImage("https://chuangxin-oss-cdn.tiangong.tech/tiangong_247630c9b2f34f69a11510c2a30de19d.jpg"),
                SimpleImage("https://chuangxin-oss-cdn.tiangong.tech/tiangong_af88d61f415143f9a31b8b92d0f5586d.jpg"),
                SimpleImage("https://chuangxin-oss-cdn.tiangong.tech/tiangong_aaed6f885fd44ed3be1ccd0e344366e4.jpg"),
            ))).data as Map<String, Any>,
            parameter = mapOf(),
            nodeArgs = listOf(
                NodeDefinitionArgs(
                    "texture",
                    null,
                    mapOf("templateDetailIds" to listOf("7343460719512788994")),
                    null
                ),
                NodeDefinitionArgs(
                    "modelImageDerivation",
                    null,
                    mapOf("prompt" to "美国20岁男孩模特穿着正肩纯棉白短袖T恤，胸口正中央大型戴墨镜的卡通老鹰头像（潮玩3D渲染），粉色渐变背景波点，场景是一个白色背景墙，全身照。 --ar 1:1 --fast --raw --profile m7334490763905990664 m7334512081732894723 m7333344910709358596 m7334042983991345164 --stylize 100 --v 7"),
                    null
                ),
                NodeDefinitionArgs(
                    "posturalFission",
                    null,
                    mapOf("prompts" to listOf("双手插兜，面部看向左前方", "双手抱头放在脑后，面部看向镜头")),
                    null
                ),
            )
        )

        // 等待工作流完成
        Awaitility.await()
            .atMost(2, TimeUnit.HOURS)
            .pollDelay(5, TimeUnit.SECONDS)
            .pollInterval(20, TimeUnit.SECONDS)
            .until {
                graphEngine.getWorkflowStatus(workflowInstanceId).status == WorkflowStatus.COMPLETED
            }
    }

    @Test
    @Disabled
    fun `test workflow retry`() = withUser(CurrentUser(1, "test", "test", 2, 1)) {
        val workflowInstanceId = 7345331784445759489

        // 重试失败的工作流
        graphEngine.retryWorkflow(workflowInstanceId)

        // 等待工作流完成
        Awaitility.await()
            .atMost(1, TimeUnit.HOURS)
            .pollDelay(5, TimeUnit.SECONDS)
            .pollInterval(20, TimeUnit.SECONDS)
            .until {
                graphEngine.getWorkflowStatus(workflowInstanceId).status.isFinished()
            }
    }

    private fun findNodeMetadataBySupplierAndAbility(supplier: Supplier, ability: Ability): NodeMetadata {
        return sqlClient.executeQuery(NodeMetadata::class) {
            where(table.supplier.eq(supplier))
            where(table.ability.eq(ability))
            select(table.fetchBy {
                allScalarFields()
            })
        }.firstOrNull() ?: throw IllegalArgumentException("WorkflowNode metadata not found: $supplier:$ability")
    }

    /**
     * 创建一个简单的测试工作流定义
     */
    private fun workflowDefinition(definitionId: Long): WorkflowDefinition = WorkflowDefinition {
        id = definitionId
        name = "裂变图案-模特多姿势试衣"
        category = "图案开款"
        nodeDefinitions = listOf(
            NodeDefinition {
                nodeKey = "start"
                review = false
                skip = true
                nodeMetadata = NodeMetadata {
                    supplier = Supplier.MURMURATION
                    ability=Ability.START
                }
            },
            NodeDefinition {
                nodeKey = "imageDerivation"
                review = true
                skip = false
                input = mapOf("images" to "#{start.images}")
                nodeMetadata = NodeMetadata {
                    supplier = Supplier.COMFY_UI
                    ability=Ability.IMAGE_DERIVATION
                }
            },
            NodeDefinition {
                nodeKey = "upscale1"
                review = false
                skip = true
                input = mapOf("images" to "#{imageDerivation.images}")
                nodeMetadata = NodeMetadata {
                    supplier = Supplier.REALESRGAN
                    ability=Ability.UPSCALE
                }
            },
            NodeDefinition {
                nodeKey = "cutout"
                review = false
                skip = true
                input = mapOf("images" to "#{upscale1.images}")
                nodeMetadata = NodeMetadata {

                    supplier = Supplier.MEI_TU
                    ability=Ability.CUTOUTS
                }
            },
            NodeDefinition {
                nodeKey = "upscale2"
                review = false
                skip = true
                input = mapOf("images" to "#{cutout.images}")
                nodeMetadata = NodeMetadata {

                    supplier = Supplier.REALESRGAN
                    ability=Ability.UPSCALE
                }
            },
            NodeDefinition {
                nodeKey = "texture"
                review = false
                skip = false
                input = mapOf("images" to "#{upscale2.images}")
                nodeMetadata = NodeMetadata {

                    supplier = Supplier.AIGC_IMAGE
                    ability=Ability.TEXTURE
                }
            },
            NodeDefinition {
                nodeKey = "modelImageDerivation"
                review = true
                skip = false
                input = mapOf()
                nodeMetadata = NodeMetadata {

                    supplier = Supplier.MIDJOURNEY
                    ability=Ability.MODEL_IMAGE_DERIVATION
                }
            },
            NodeDefinition {
                nodeKey = "tryon"
                review = true
                skip = false
                input =
                    mapOf("clothingImages" to "#{texture.images}", "modelImages" to "#{modelImageDerivation.images}")
                nodeMetadata = NodeMetadata {

                    supplier = Supplier.CHAO_JI
                    ability=Ability.TRY_ON
                }
            },
            NodeDefinition {
                nodeKey = "posturalFission"
                review = true
                skip = false
                input = mapOf("images" to "#{tryon.images}")
                nodeMetadata = NodeMetadata {

                    supplier = Supplier.COMFY_UI
                    ability=Ability.POSTURAL_FISSION
                }
            },
            NodeDefinition {
                nodeKey = "pushPod"
                review = false
                skip = true
                input = mapOf("images" to "#{tryon.images} + #{posturalFission.images}")
                nodeMetadata = NodeMetadata {

                    supplier = Supplier.MURMURATION
                    ability=Ability.PUSH_POD_STYLE
                }
            },
            NodeDefinition {
                nodeKey = "end"
                review = false
                skip = true
                input = mapOf("images" to "#{posturalFission.images}")
                nodeMetadata = NodeMetadata {

                    supplier = Supplier.MURMURATION
                    ability=Ability.END
                }
            }
        )

        // 3. 创建边定义
        edges = listOf(
            Edge {
                sourceNodeKey = "start"
                targetNodeKey = "imageDerivation"
            },
            Edge {
                sourceNodeKey = "start"
                targetNodeKey = "modelImageDerivation"
            },
            Edge {
                sourceNodeKey = "imageDerivation"
                targetNodeKey = "upscale1"
            },
            Edge {
                sourceNodeKey = "upscale1"
                targetNodeKey = "cutout"
            },
            Edge {
                sourceNodeKey = "cutout"
                targetNodeKey = "upscale2"
            },
            Edge {
                sourceNodeKey = "upscale2"
                targetNodeKey = "texture"
            },
            Edge {
                sourceNodeKey = "texture"
                targetNodeKey = "tryon"
            },
            Edge {
                sourceNodeKey = "modelImageDerivation"
                targetNodeKey = "tryon"
            },
            Edge {
                sourceNodeKey = "tryon"
                targetNodeKey = "posturalFission"
            },
            Edge {
                sourceNodeKey = "posturalFission"
                targetNodeKey = "pushPod"
            },
            Edge {
                sourceNodeKey = "pushPod"
                targetNodeKey = "end"
            })

    }
    private fun workflowDefinition1(definitionId: Long): WorkflowDefinition = WorkflowDefinition {
        id = definitionId
        name = "图案素材-模特多姿势试衣"
        category = "图案开款"
        nodeDefinitions = listOf(
            NodeDefinition {
                nodeKey = "start"
                review = false
                skip = true
                nodeMetadata = NodeMetadata {

                    supplier = Supplier.MURMURATION
                    ability=Ability.START
                }
            },
            NodeDefinition {
                nodeKey = "upscale1"
                review = false
                skip = true
                input = mapOf("images" to "#{start.images}")
                nodeMetadata = NodeMetadata {

                    supplier = Supplier.REALESRGAN
                    ability=Ability.UPSCALE
                }
            },
            NodeDefinition {
                nodeKey = "cutout"
                review = false
                skip = true
                input = mapOf("images" to "#{upscale1.images}")
                nodeMetadata = NodeMetadata {

                    supplier = Supplier.MEI_TU
                    ability=Ability.CUTOUTS
                }
            },
            NodeDefinition {
                nodeKey = "upscale2"
                review = false
                skip = true
                input = mapOf("images" to "#{cutout.images}")
                nodeMetadata = NodeMetadata {

                    supplier = Supplier.REALESRGAN
                    ability=Ability.UPSCALE
                }
            },
            NodeDefinition {
                nodeKey = "texture"
                review = false
                skip = false
                input = mapOf("images" to "#{upscale2.images}")
                nodeMetadata = NodeMetadata {

                    supplier = Supplier.AIGC_IMAGE
                    ability=Ability.TEXTURE
                }
            },
            NodeDefinition {
                nodeKey = "modelImageDerivation"
                review = true
                skip = false
                input = mapOf()
                nodeMetadata = NodeMetadata {

                    supplier = Supplier.MIDJOURNEY
                    ability=Ability.MODEL_IMAGE_DERIVATION
                }
            },
            NodeDefinition {
                nodeKey = "tryon"
                review = true
                skip = false
                input =
                    mapOf("clothingImages" to "#{texture.images}", "modelImages" to "#{modelImageDerivation.images}")
                nodeMetadata = NodeMetadata {

                    supplier = Supplier.CHAO_JI
                    ability=Ability.TRY_ON
                }
            },
            NodeDefinition {
                nodeKey = "posturalFission"
                review = true
                skip = false
                input = mapOf("images" to "#{tryon.images}")
                nodeMetadata = NodeMetadata {

                    supplier = Supplier.COMFY_UI
                    ability=Ability.POSTURAL_FISSION
                }
            },
            NodeDefinition {
                nodeKey = "pushPod"
                review = false
                skip = true
                input = mapOf("images" to "#{tryon.images} + #{posturalFission.images}")
                nodeMetadata = NodeMetadata {

                    supplier = Supplier.MURMURATION
                    ability=Ability.PUSH_POD_STYLE
                }
            },
            NodeDefinition {
                nodeKey = "end"
                review = false
                skip = true
                input = mapOf("images" to "#{posturalFission.images}")
                nodeMetadata = NodeMetadata {

                    supplier = Supplier.MURMURATION
                    ability=Ability.END
                }
            }
        )

        // 3. 创建边定义
        edges = listOf(
            Edge {
                sourceNodeKey = "start"
                targetNodeKey = "upscale1"
            },
            Edge {
                sourceNodeKey = "start"
                targetNodeKey = "modelImageDerivation"
            },
            Edge {
                sourceNodeKey = "upscale1"
                targetNodeKey = "cutout"
            },
            Edge {
                sourceNodeKey = "cutout"
                targetNodeKey = "upscale2"
            },
            Edge {
                sourceNodeKey = "upscale2"
                targetNodeKey = "texture"
            },
            Edge {
                sourceNodeKey = "texture"
                targetNodeKey = "tryon"
            },
            Edge {
                sourceNodeKey = "modelImageDerivation"
                targetNodeKey = "tryon"
            },
            Edge {
                sourceNodeKey = "tryon"
                targetNodeKey = "posturalFission"
            },
            Edge {
                sourceNodeKey = "posturalFission"
                targetNodeKey = "pushPod"
            },
            Edge {
                sourceNodeKey = "pushPod"
                targetNodeKey = "end"
            })

    }

    private fun workflowDefinition2(definitionId: Long): WorkflowDefinition = WorkflowDefinition {
        id = definitionId
        name = "图案裂变款"
        category = "图案开款"
        nodeDefinitions = listOf(
            NodeDefinition {
                nodeKey = "start"
                review = false
                skip = true
                nodeMetadata = NodeMetadata {

                    supplier = Supplier.MURMURATION
                    ability=Ability.START
                }
            },
            NodeDefinition {
                nodeKey = "imageDerivation"
                review = true
                skip = false
                input = mapOf("images" to "#{start.images}")
                nodeMetadata = NodeMetadata {

                    supplier = Supplier.COMFY_UI
                    ability=Ability.IMAGE_DERIVATION
                }
            },
            NodeDefinition {
                nodeKey = "upscale1"
                review = false
                skip = true
                input = mapOf("images" to "#{imageDerivation.images}")
                nodeMetadata = NodeMetadata {

                    supplier = Supplier.REALESRGAN
                    ability=Ability.UPSCALE
                }
            },
            NodeDefinition {
                nodeKey = "cutout"
                review = false
                skip = true
                input = mapOf("images" to "#{upscale1.images}")
                nodeMetadata = NodeMetadata {

                    supplier = Supplier.MEI_TU
                    ability=Ability.CUTOUTS

                }
            },
            NodeDefinition {
                nodeKey = "upscale2"
                review = false
                skip = true
                input = mapOf("images" to "#{cutout.images}")
                nodeMetadata = NodeMetadata {

                    supplier = Supplier.REALESRGAN
                    ability=Ability.UPSCALE
                }
            },
            NodeDefinition {
                nodeKey = "texture"
                review = false
                skip = false
                input = mapOf("images" to "#{upscale2.images}")
                nodeMetadata = NodeMetadata {

                    supplier = Supplier.AIGC_IMAGE
                    ability=Ability.TEXTURE
                }
            },
            NodeDefinition {
                nodeKey = "pushPod"
                review = false
                skip = true
                input = mapOf("images" to "#{texture.images}")
                nodeMetadata = NodeMetadata {

                    supplier = Supplier.MURMURATION
                    ability=Ability.PUSH_POD_STYLE
                }
            },
            NodeDefinition {
                nodeKey = "end"
                review = false
                skip = true
                input = mapOf("images" to "")
                nodeMetadata = NodeMetadata {

                    supplier = Supplier.MURMURATION
                    ability=Ability.END
                }
            }
        )

        // 3. 创建边定义
        edges = listOf(
            Edge {
                sourceNodeKey = "start"
                targetNodeKey = "imageDerivation"
            },
            Edge {
                sourceNodeKey = "imageDerivation"
                targetNodeKey = "upscale1"
            },
            Edge {
                sourceNodeKey = "upscale1"
                targetNodeKey = "cutout"
            },
            Edge {
                sourceNodeKey = "cutout"
                targetNodeKey = "upscale2"
            },
            Edge {
                sourceNodeKey = "upscale2"
                targetNodeKey = "texture"
            },
            Edge {
                sourceNodeKey = "texture"
                targetNodeKey = "pushPod"
            },
            Edge {
                sourceNodeKey = "pushPod"
                targetNodeKey = "end"
            })

    }

    private fun workflowDefinition3(definitionId: Long): WorkflowDefinition = WorkflowDefinition {
        id = definitionId
        name = "图案素材款"
        category = "图案开款"
        nodeDefinitions = listOf(
            NodeDefinition {
                nodeKey = "start"
                review = false
                skip = true
                nodeMetadata = NodeMetadata {

                    supplier = Supplier.MURMURATION
                    ability=Ability.START
                }
            },
            NodeDefinition {
                nodeKey = "upscale1"
                review = false
                skip = true
                input = mapOf("images" to "#{start.images}")
                nodeMetadata = NodeMetadata {

                    supplier = Supplier.REALESRGAN
                    ability=Ability.UPSCALE
                }
            },
            NodeDefinition {
                nodeKey = "cutout"
                review = false
                skip = true
                input = mapOf("images" to "#{upscale1.images}")
                nodeMetadata = NodeMetadata {

                    supplier = Supplier.MEI_TU
                    ability=Ability.CUTOUTS
                }
            },
            NodeDefinition {
                nodeKey = "upscale2"
                review = false
                skip = true
                input = mapOf("images" to "#{cutout.images}")
                nodeMetadata = NodeMetadata {

                    supplier = Supplier.REALESRGAN
                    ability=Ability.UPSCALE
                }
            },
            NodeDefinition {
                nodeKey = "texture"
                review = false
                skip = false
                input = mapOf("images" to "#{upscale2.images}")
                nodeMetadata = NodeMetadata {

                    supplier = Supplier.AIGC_IMAGE
                    ability=Ability.TEXTURE
                }
            },
            NodeDefinition {
                nodeKey = "pushPod"
                review = false
                skip = true
                input = mapOf("images" to "#{texture.images}")
                nodeMetadata = NodeMetadata {

                    supplier = Supplier.MURMURATION
                    ability=Ability.PUSH_POD_STYLE
                }
            },
            NodeDefinition {
                nodeKey = "end"
                review = false
                skip = true
                input = mapOf("images" to "")
                nodeMetadata = NodeMetadata {

                    supplier = Supplier.MURMURATION
                    ability=Ability.END
                }
            }
        )

        // 3. 创建边定义
        edges = listOf(
            Edge {
                sourceNodeKey = "start"
                targetNodeKey = "upscale1"
            },
            Edge {
                sourceNodeKey = "upscale1"
                targetNodeKey = "cutout"
            },
            Edge {
                sourceNodeKey = "cutout"
                targetNodeKey = "upscale2"
            },
            Edge {
                sourceNodeKey = "upscale2"
                targetNodeKey = "texture"
            },
            Edge {
                sourceNodeKey = "texture"
                targetNodeKey = "pushPod"
            },
            Edge {
                sourceNodeKey = "pushPod"
                targetNodeKey = "end"
            })

    }
}
