package team.aikero.murmuration.sdk.client

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import team.aikero.blade.test.mock.withMockedUser
import team.aikero.murmuration.MurmurationApplication
import team.aikero.murmuration.common.req.AddMuseSameStyleImageRequest
import team.aikero.murmuration.service.controller.inner.MuseSameStyleImageInnerController
import java.util.UUID

@SpringBootTest(classes = [MurmurationApplication::class])
class MuseSameStyleImageClientTest(@Autowired val controller: MuseSameStyleImageInnerController) {

    @Test
    fun test() = withMockedUser {
        val spuCode = UUID.randomUUID().toString()
        val request = AddMuseSameStyleImageRequest(
            url = "https://chuangxin-oss-cdn.tiangong.tech/tiangong_bb764468c3a7467f99777779ba584740.png",
            spuCode = spuCode,
            supplyMode = "Manufacturer",
            categoryCode = "010102",
        )

        run {
            val response = controller.add(request, false)
            assertThat(response.successful).isTrue()
        }

        run {
            val response = controller.add(request, false)
            assertThat(response.successful).isTrue()
        }
    }

    @Test
    fun add() = withMockedUser {
        val request = AddMuseSameStyleImageRequest(
            url = "https://chuangxin-oss-cdn.tiangong.tech/tiangong_bb764468c3a7467f99777779ba584740.png",
            spuCode = "SPU款号1",
            supplyMode = "Manufacturer",
            categoryCode = "品类",
        )
        val response = controller.add(request, false)
        assertThat(response.successful).isTrue()
    }
}
