package team.aikero.murmuration.ai.chaoji

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import team.aikero.murmuration.MurmurationApplication
import team.aikero.murmuration.common.enums.task.ClothingType
import team.aikero.murmuration.core.workflow.task.TaskResult
import team.aikero.murmuration.service.node.task.chaoji.ChaoJiClient

@Disabled("开发阶段使用")
@SpringBootTest(classes = [MurmurationApplication::class])
class ChaoJiClientTest(@Autowired val client: ChaoJiClient) {

    @Test
    @DisplayName("服饰抠图")
    fun segmentClothing() {
        val maskImageUrl = client.segmentClothing(
            imageUrl = "https://oss.yunbanfang.cn/2a44d6a3-01a8-4f3b-93ce-507b803230b8/tiangong_3549e4f51bbd46e689e98a5a5f05f5f3.PNG",
            cateToken = ClothingType.UPPER,
        )
        assertThat(maskImageUrl).isNotEmpty()
    }

    @Test
    @DisplayName("虚拟换衣")
    fun tryOn() {
        val taskId = client.tryOn(
            clothingImageUrl = "https://oss.yunbanfang.cn/2a44d6a3-01a8-4f3b-93ce-507b803230b8/tiangong_3549e4f51bbd46e689e98a5a5f05f5f3.PNG",
            clothingType = ClothingType.UPPER,
            modelImageUrl = "https://chuangxin-oss-cdn.tiangong.tech/tiangong_a18e63da1f0744a5ad31a31668fd2d8d.jpg",
            batchSize = 4,
        )
        assertThat(taskId).isGreaterThan(0)
    }

    @Test
    @DisplayName("查询任务")
    fun getTaskResult() {
        val taskResult = client.getTaskResult(2286277) as TaskResult.Completed<List<String>>
        val urls = taskResult.value
        assertThat(urls).hasSize(4).allMatch { it.isNotEmpty() }
    }
}
