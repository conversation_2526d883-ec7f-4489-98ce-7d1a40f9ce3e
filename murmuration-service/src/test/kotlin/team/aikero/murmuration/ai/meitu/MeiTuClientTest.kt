package team.aikero.murmuration.ai.meitu

import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import team.aikero.blade.util.json.toJson
import team.aikero.murmuration.MurmurationApplication
import team.aikero.murmuration.service.node.task.meitu.*

@Disabled("开发阶段使用")
@SpringBootTest(classes = [MurmurationApplication::class])
class MeiTuClientTest {

    @Test
    fun meitu(@Autowired meiTuClient: MeiTuClient) {
        val meiTuParamsReq = MeiTuParamsReq(MeiTuParameterReq().apply {
            nMask = false
            modelType = 2
            nbox = true
        })
        val meituReq = MeiTuReq(
            initImages = listOf(MeiTuImageReq("https://oss-datawork.oss-cn-hangzhou.aliyuncs.com/ai_images/server/UPscale/3693618586259454603.png")),
            params = meiTuParamsReq.toJson()
        )
        val createTask = meiTuClient.createTask(meituReq)
        println(createTask.toJson())
    }
}
