package team.aikero.murmuration.ai.youchuan

import org.assertj.core.api.Assertions.assertThat
import org.awaitility.Awaitility.await
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import team.aikero.blade.util.json.toJson
import team.aikero.murmuration.MurmurationApplication
import team.aikero.murmuration.core.workflow.task.TaskResult
import team.aikero.murmuration.service.node.task.youchuan.YouChuanClient
import java.util.concurrent.TimeUnit

@Disabled("开发阶段使用")
@SpringBootTest(classes = [MurmurationApplication::class])
class YouChuanClientTest(@Autowired val client: YouChuanClient) {

    @Test
    @Disabled("耗时太久了")
    fun diffusion() {
        val jobId = client.diffusion("一只小狗")
        await()
            .atMost(2, TimeUnit.MINUTES)
            .pollDelay(30, TimeUnit.SECONDS)
            .pollInterval(10, TimeUnit.SECONDS)
            .until {
                client.getJobResult(jobId).isCompleted()
            }
        val asyncResult = client.getJobResult(jobId) as TaskResult.Completed<List<String>>
        val urls = asyncResult.value
        assertThat(urls).isNotEmpty()
    }

    @Test
    fun moodboard() {
        val pageMoodboard = client.pageMoodboard()
        println(pageMoodboard.toJson())
    }
}
