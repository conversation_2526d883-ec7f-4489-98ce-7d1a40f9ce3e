package team.aikero.murmuration.ai.openbridge

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import team.aikero.murmuration.MurmurationApplication
import team.aikero.murmuration.service.node.task.openbridge.OpenBridgeClient

@Disabled("开发阶段使用")
@SpringBootTest(classes = [MurmurationApplication::class])
class OpenBridgeClientTest(@Autowired val client: OpenBridgeClient) {

    @Test
    fun nonSensitiveImage() {
        val isSensitive = client.isSensitiveImage("https://chuangxin-oss-cdn.tiangong.tech/b56466a5af8c3de398007c0d1fc7f5a2.jpg")
        assertThat(isSensitive).isEqualTo(false)
    }
}
