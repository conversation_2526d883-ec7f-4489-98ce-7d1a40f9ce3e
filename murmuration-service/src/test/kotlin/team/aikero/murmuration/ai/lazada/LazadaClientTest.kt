package team.aikero.murmuration.ai.lazada

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import team.aikero.murmuration.MurmurationApplication
import team.aikero.murmuration.core.workflow.task.TaskResult
import team.aikero.murmuration.service.node.task.lazada.ChangeBackgroundRequest
import team.aikero.murmuration.service.node.task.lazada.ChangeFaceRequest
import team.aikero.murmuration.service.node.task.lazada.LazadaClient

@Disabled("开发阶段使用")
@SpringBootTest(classes = [MurmurationApplication::class])
class LazadaClientTest(@Autowired val client: LazadaClient) {

    /**
     * AI换脸
     */
    @Test
    fun changeFace() {
        val taskId = client.changeFace(
            ChangeFaceRequest(
                rawImageUrl = "https://chuangxin-oss-cdn.tiangong.tech/tiangong_9a254e963c214af08943e277e295903d.png",
                modelCode = "PH-29",
                batchSize = 1,
            )
        )
        assertThat(taskId).isNotEmpty()
    }

    /**
     * AI换背景
     */
    @Test
    fun changeBackground() {
        val taskId = client.changeBackground(
            ChangeBackgroundRequest(
                productImageUrl = "https://filebroker-cdn.lazada.sg/kf/Se211093bff144252b0422cb28d6c4d2ei.jpg",
                backgroundCode = "油画装饰",
                batchSize = 1,
            )
        )
        assertThat(taskId).isNotEmpty()
    }

    /**
     * 查询任务状态(成功)
     */
    @Test
    fun getTaskStatusSuccessfully() {
        val taskStatus = client.getTaskStatus("29962") as TaskResult.Completed<List<String>>
        assertThat(taskStatus.value).isNotEmpty()
    }

    /**
     * 查询任务状态(失败)
     */
    @Test
    fun getTaskStatusFailed() {
        val taskStatus = client.getTaskStatus("29576") as TaskResult.Failed
        assertThat(taskStatus.error).isNotNull()
    }
}
