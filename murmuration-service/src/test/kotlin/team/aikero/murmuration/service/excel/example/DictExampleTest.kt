package team.aikero.murmuration.service.excel.example

import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test

/**
 * 字典示例测试
 */
class DictExampleTest {
    
    @Test
    fun `test gender dict provider`() {
        val provider = GenderDictProvider()
        
        val mapping = provider.getDictMapping("gender")
        assertEquals(3, mapping.size)
        assertEquals(1, mapping["男"])
        assertEquals(2, mapping["女"])
        assertEquals(0, mapping["未知"])
        
        val reverseMapping = provider.getReverseMapping("gender")
        assertEquals(3, reverseMapping.size)
        assertEquals("男", reverseMapping[1])
        assertEquals("女", reverseMapping[2])
        assertEquals("未知", reverseMapping[0])
        
        val options = provider.getDropDownOptions("gender")
        assertEquals(3, options.size)
        assertTrue(options.contains("男"))
        assertTrue(options.contains("女"))
        assertTrue(options.contains("未知"))
    }
    
    @Test
    fun `test status dict provider`() {
        val provider = StatusDictProvider()
        
        val mapping = provider.getDictMapping("status")
        assertEquals(2, mapping.size)
        assertEquals(true, mapping["启用"])
        assertEquals(false, mapping["禁用"])
        
        val reverseMapping = provider.getReverseMapping("status")
        assertEquals(2, reverseMapping.size)
        assertEquals("启用", reverseMapping[true])
        assertEquals("禁用", reverseMapping[false])
        
        val options = provider.getDropDownOptions("status")
        assertEquals(2, options.size)
        assertTrue(options.contains("启用"))
        assertTrue(options.contains("禁用"))
    }
    
    @Test
    fun `test priority dict provider`() {
        val provider = PriorityDictProvider()
        
        val mapping = provider.getDictMapping("priority")
        assertEquals(3, mapping.size)
        assertEquals("HIGH", mapping["高"])
        assertEquals("MEDIUM", mapping["中"])
        assertEquals("LOW", mapping["低"])
        
        val reverseMapping = provider.getReverseMapping("priority")
        assertEquals(3, reverseMapping.size)
        assertEquals("高", reverseMapping["HIGH"])
        assertEquals("中", reverseMapping["MEDIUM"])
        assertEquals("低", reverseMapping["LOW"])
        
        val options = provider.getDropDownOptions("priority")
        assertEquals(3, options.size)
        assertTrue(options.contains("高"))
        assertTrue(options.contains("中"))
        assertTrue(options.contains("低"))
    }
    
    @Test
    fun `test unknown dict type`() {
        val provider = GenderDictProvider()
        
        val mapping = provider.getDictMapping("unknown")
        assertEquals(0, mapping.size)
        
        val reverseMapping = provider.getReverseMapping("unknown")
        assertEquals(0, reverseMapping.size)
        
        val options = provider.getDropDownOptions("unknown")
        assertEquals(0, options.size)
    }
    
    @Test
    fun `test user example data class`() {
        val user = UserExample(
            name = "张三",
            gender = 1,
            status = true,
            priority = "HIGH"
        )
        
        assertEquals("张三", user.name)
        assertEquals(1, user.gender)
        assertEquals(true, user.status)
        assertEquals("HIGH", user.priority)
    }
}