package team.aikero.murmuration.service.excel.converter

import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import team.aikero.murmuration.service.excel.example.GenderDictProvider
import team.aikero.murmuration.service.excel.example.StatusDictProvider

/**
 * 字典值提供者注册中心测试
 */
class DictValueProviderRegistryTest {
    
    @BeforeEach
    fun setUp() {
        // 清空注册中心 - 通过反射获取并清空 providers
        val field = DictValueProviderRegistry::class.java.getDeclaredField("providers")
        field.isAccessible = true
        val providers = field.get(null) as MutableMap<String, Any>
        providers.clear()
    }
    
    @Test
    fun `test register and get provider`() {
        val provider = GenderDictProvider()
        DictValueProviderRegistry.registerProvider("gender", provider)
        
        val retrieved = DictValueProviderRegistry.getProvider("gender")
        assertNotNull(retrieved)
        assertEquals(provider, retrieved)
    }
    
    @Test
    fun `test get non-existent provider`() {
        val retrieved = DictValueProviderRegistry.getProvider("non_existent")
        assertEquals(null, retrieved)
    }
    
    @Test
    fun `test get all dict types`() {
        DictValueProviderRegistry.registerProvider("gender", GenderDictProvider())
        DictValueProviderRegistry.registerProvider("status", StatusDictProvider())
        
        val types = DictValueProviderRegistry.getAllDictTypes()
        
        assertEquals(2, types.size)
        assertTrue(types.contains("gender"))
        assertTrue(types.contains("status"))
    }
    
    @Test
    fun `test register duplicate provider`() {
        val provider1 = GenderDictProvider()
        val provider2 = StatusDictProvider()
        
        DictValueProviderRegistry.registerProvider("test", provider1)
        DictValueProviderRegistry.registerProvider("test", provider2)
        
        val retrieved = DictValueProviderRegistry.getProvider("test")
        assertEquals(provider2, retrieved) // 应该覆盖之前的
    }
}