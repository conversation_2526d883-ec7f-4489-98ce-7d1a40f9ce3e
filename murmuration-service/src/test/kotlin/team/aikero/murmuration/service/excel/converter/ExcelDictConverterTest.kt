package team.aikero.murmuration.service.excel.converter

import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import team.aikero.murmuration.service.excel.example.GenderDictProvider
import team.aikero.murmuration.service.excel.example.StatusDictProvider
import team.aikero.murmuration.service.excel.example.PriorityDictProvider
import team.aikero.murmuration.service.excel.example.UserExample

/**
 * Excel 字典转换器测试
 */
class ExcelDictConverterTest {
    
    private lateinit var converter: ExcelDictConverter
    
    @BeforeEach
    fun setUp() {
        converter = ExcelDictConverter()
        
        // 初始化字典提供者
        DictValueProviderRegistry.registerProvider("gender", GenderDictProvider())
        DictValueProviderRegistry.registerProvider("status", StatusDictProvider())
        DictValueProviderRegistry.registerProvider("priority", PriorityDictProvider())
    }
    
    @Test
    fun `test convert single object dict values`() {
        // 创建一个模拟从 Excel 读取的数据映射
        val excelData = mapOf(
            "name" to "张三",
            "gender" to "男",    // Excel 中的显示值
            "status" to "启用",  // Excel 中的显示值
            "priority" to "高"   // Excel 中的显示值
        )
        
        // 使用新的转换方法
        val converted = converter.convertFromStringValues(excelData, UserExample::class.java)
        
        // 验证转换结果
        assertEquals("张三", converted.name)
        assertEquals(1, converted.gender)
        assertEquals(true, converted.status)
        assertEquals("HIGH", converted.priority)
    }
    
    @Test
    fun `test convert to display values`() {
        val user = UserExample(
            name = "张三",
            gender = 1,
            status = true,
            priority = "HIGH"
        )
        
        val converted = converter.convertToDisplayValues(user)
        
        assertEquals("男", converted.gender)
        assertEquals("启用", converted.status)
        assertEquals("高", converted.priority)
    }
    
    @Test
    fun `test convert list to display values`() {
        val user1 = UserExample(
            name = "张三",
            gender = 1,
            status = true,
            priority = "HIGH"
        )
        
        val user2 = UserExample(
            name = "李四",
            gender = 2,
            status = false,
            priority = "LOW"
        )
        
        val users = listOf(user1, user2)
        val converted = converter.convertToDisplayValues(users)
        
        assertEquals("男", converted[0].gender)
        assertEquals("启用", converted[0].status)
        assertEquals("高", converted[0].priority)
        assertEquals("女", converted[1].gender)
        assertEquals("禁用", converted[1].status)
        assertEquals("低", converted[1].priority)
    }
    
    @Test
    fun `test get dict mapping`() {
        val field = UserExample::class.java.getDeclaredField("gender")
        val annotation = field.getAnnotation(team.aikero.murmuration.service.excel.annotation.DictConvert::class.java)
        
        val mapping = converter.getDictMapping(field, annotation)
        
        assertEquals(3, mapping.size)
        assertEquals(1, mapping["男"])
        assertEquals(2, mapping["女"])
        assertEquals(0, mapping["未知"])
    }
    
    @Test
    fun `test get drop down options`() {
        val field = UserExample::class.java.getDeclaredField("gender")
        val annotation = field.getAnnotation(team.aikero.murmuration.service.excel.annotation.DictConvert::class.java)
        
        val options = converter.getDropDownOptions(field, annotation)
        
        assertEquals(3, options.size)
        assertTrue(options.contains("男"))
        assertTrue(options.contains("女"))
        assertTrue(options.contains("未知"))
    }
    
    @Test
    fun `test convert null object`() {
        val converted = converter.convertDictValues(null as UserExample?)
        assertNull(converted)
    }
    
    @Test
    fun `test convert null list`() {
        val converted = converter.convertDictValues(null as List<UserExample>?)
        assertNull(converted)
    }
    
    @Test
    fun `test convert with unknown dict type`() {
        // 创建一个临时类来测试未知字典类型
        data class TestUser(
            @team.aikero.murmuration.service.excel.annotation.DictConvert(
                dictType = "unknown_type",
                targetClass = Int::class
            )
            var testField: Int? = null
        )
        
        val user = TestUser(testField = 1)
        
        val exception = assertThrows(IllegalArgumentException::class.java) {
            converter.convertDictValues(user)
        }
        
        assertTrue(exception.message!!.contains("No provider found for dict type"))
    }
    
    @Test
    fun `test convert with invalid value`() {
        // 创建一个模拟从 Excel 读取的数据映射
        val excelData = mapOf(
            "name" to "张三",
            "gender" to "无效值",  // 无效的字典值
            "status" to "启用",
            "priority" to "高"
        )
        
        // 应该不会抛出异常，无效值会保持原样
        val converted = converter.convertFromStringValues(excelData, UserExample::class.java)
        
        // 验证结果
        assertEquals("张三", converted.name)
        assertEquals("无效值", converted.gender)  // 无效值保持原样
        assertEquals(true, converted.status)
        assertEquals("HIGH", converted.priority)
    }
}