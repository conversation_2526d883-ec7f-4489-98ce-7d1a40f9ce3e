package team.aikero.murmuration.service.excel.converter

import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import team.aikero.murmuration.service.excel.example.GenderDictProvider
import team.aikero.murmuration.service.excel.example.StatusDictProvider
import team.aikero.murmuration.service.excel.example.UserExample

/**
 * Excel 下拉选项提供者测试
 */
class ExcelDropDownProviderTest {
    
    private lateinit var dropDownProvider: ExcelDropDownProvider
    
    @BeforeEach
    fun setUp() {
        dropDownProvider = ExcelDropDownProvider()
        
        // 注册字典提供者
        DictValueProviderRegistry.registerProvider("gender", GenderDictProvider())
        DictValueProviderRegistry.registerProvider("status", StatusDictProvider())
        DictValueProviderRegistry.registerProvider("priority", team.aikero.murmuration.service.excel.example.PriorityDictProvider())
    }
    
    @Test
    fun `test generate excel with drop down options`() {
        val data = listOf(
            UserExample(name = "张三", gender = 1, status = true),
            UserExample(name = "李四", gender = 2, status = false)
        )
        
        val excelBytes = dropDownProvider.generateExcelWithDropDownOptions(UserExample::class.java, data)
        
        assertNotNull(excelBytes)
        assertTrue(excelBytes.isNotEmpty())
        
        // 验证字节数组长度合理
        assertTrue(excelBytes.size > 100) // Excel 文件应该有一定大小
    }
    
    @Test
    fun `test get drop down options for field`() {
        val field = UserExample::class.java.getDeclaredField("gender")
        val options = dropDownProvider.getDropDownOptionsForField(field)
        
        assertEquals(3, options.size)
        assertTrue(options.contains("男"))
        assertTrue(options.contains("女"))
        assertTrue(options.contains("未知"))
    }
    
    @Test
    fun `test get drop down options for field without annotation`() {
        val field = UserExample::class.java.getDeclaredField("name")
        val options = dropDownProvider.getDropDownOptionsForField(field)
        
        assertEquals(0, options.size)
    }
    
    @Test
    fun `test generate drop down options info`() {
        val optionsInfo = dropDownProvider.generateDropDownOptionsInfo(UserExample::class.java)
        
        assertEquals(3, optionsInfo.size)
        assertTrue(optionsInfo.containsKey("gender"))
        assertTrue(optionsInfo.containsKey("status"))
        assertTrue(optionsInfo.containsKey("priority"))
        
        assertEquals(3, optionsInfo["gender"]?.size)
        assertEquals(2, optionsInfo["status"]?.size)
        assertEquals(3, optionsInfo["priority"]?.size)
    }
    
    @Test
    fun `test generate excel with empty data`() {
        val data = emptyList<UserExample>()
        
        val excelBytes = dropDownProvider.generateExcelWithDropDownOptions(UserExample::class.java, data)
        
        assertNotNull(excelBytes)
        assertTrue(excelBytes.isNotEmpty())
    }
}