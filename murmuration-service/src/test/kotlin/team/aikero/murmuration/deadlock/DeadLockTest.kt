package team.aikero.murmuration.deadlock

import org.babyfish.jimmer.sql.ast.mutation.AssociatedSaveMode
import org.babyfish.jimmer.sql.ast.mutation.SaveMode
import org.babyfish.jimmer.sql.kt.KSqlClient
import org.babyfish.jimmer.sql.kt.ast.expression.eq
import org.babyfish.jimmer.sql.kt.fetcher.newFetcher
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import team.aikero.blade.auth.withSystemUser
import team.aikero.murmuration.MurmurationApplication
import team.aikero.murmuration.core.workflow.entity.NodeInstance
import team.aikero.murmuration.core.workflow.entity.NodeInstanceDetail
import team.aikero.murmuration.core.workflow.entity.NodeStatus
import team.aikero.murmuration.core.workflow.entity.by
import team.aikero.murmuration.core.workflow.entity.nodeInstanceId
import team.aikero.murmuration.core.workflow.entity.storage

@SpringBootTest(classes = [MurmurationApplication::class])
class DeadLockTest(@Autowired val sql: KSqlClient) {

    @Test
    @Disabled
    fun test() = withSystemUser {
        val nodeInstanceId = 7345381776128679943
        val thread1 = Thread {
            sql.transaction {
                // 查询主表+子表
                sql.findOneById(
                    newFetcher(NodeInstance::class).by {
                        allScalarFields()
                        nodeDefinition {
                            allScalarFields()
                        }
                        workflowInstance {
                            allScalarFields()
                            workflowDefinition {
                                allScalarFields()
                            }
                            detail {
                                allScalarFields()
                            }
                            nodeInstances {
                                allScalarFields()
                                nodeDefinition {
                                    allScalarFields()
                                }
                                detail {
                                    allScalarFields()
                                }
                            }
                        }
                        detail {
                            allScalarFields()
                        }
                    },
                    nodeInstanceId
                )
                // 更新子表storage
                sql.executeUpdate(NodeInstanceDetail::class) {
                    where(table.nodeInstanceId eq nodeInstanceId)
                    set(table.storage, mutableMapOf())
                }
                // 更新主表状态为REVIEWING
                sql.save(NodeInstance {
                    this.id = nodeInstanceId
                    this.status = NodeStatus.REVIEWING
                }, SaveMode.UPDATE_ONLY, AssociatedSaveMode.UPDATE)
            }
        }

        val thread2 = Thread {
            sql.transaction {
                // 查询主表+子表
                sql.findOneById(
                    newFetcher(NodeInstance::class).by {
                        allScalarFields()
                        nodeDefinition {
                            allScalarFields()
                        }
                        workflowInstance {
                            allScalarFields()
                            workflowDefinition {
                                allScalarFields()
                            }
                            detail {
                                allScalarFields()
                            }
                            nodeInstances {
                                allScalarFields()
                                nodeDefinition {
                                    allScalarFields()
                                }
                                detail {
                                    allScalarFields()
                                }
                            }
                        }
                        detail {
                            allScalarFields()
                        }
                    },
                    nodeInstanceId
                )
                // 更新子表storage
                sql.executeUpdate(NodeInstanceDetail::class) {
                    where(table.nodeInstanceId eq nodeInstanceId)
                    set(table.storage, mutableMapOf())
                }
                // 更新主表状态为COMPLETED
                sql.save(NodeInstance {
                    this.id = nodeInstanceId
                    this.status = NodeStatus.COMPLETED
                }, SaveMode.UPDATE_ONLY, AssociatedSaveMode.UPDATE)
            }
        }

        thread1.start()
        thread2.start()

        thread1.join()
        thread2.join()
    }
}
