import team.aikero.gradle.plugin.version.catalog.versionCatalogConf

plugins {
    id("team.aikero.gradle.plugin.version-catalog")
    id("org.gradle.toolchains.foojay-resolver-convention")
}

val frameworkVersion: String by settings

rootProject.name = "murmuration"
include(
    "murmuration-common",
    "murmuration-sdk",
    "murmuration-runtime",
    "murmuration-service",
    // 避免 ksp 产物与服务产物重名
    "murmuration-ksp:murmuration-ksp-service",
    "murmuration-ksp:murmuration-ksp-sdk",
    "murmuration-ksp:murmuration-ksp-enum",
)

versionCatalogConf {
    artifactVersion = frameworkVersion
}
