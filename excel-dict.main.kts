#!/usr/bin/env kotlin
@file:DependsOn("cn.idev.excel:fastexcel:1.2.0")

import cn.idev.excel.FastExcel
import cn.idev.excel.annotation.ExcelProperty
import cn.idev.excel.converters.Converter
import cn.idev.excel.enums.CellDataTypeEnum
import cn.idev.excel.metadata.AbstractParameterBuilder
import cn.idev.excel.metadata.GlobalConfiguration
import cn.idev.excel.metadata.data.ReadCellData
import cn.idev.excel.metadata.data.WriteCellData
import cn.idev.excel.metadata.property.ExcelContentProperty
import cn.idev.excel.write.handler.SheetWriteHandler
import cn.idev.excel.write.handler.context.SheetWriteHandlerContext
import org.apache.poi.ss.usermodel.Sheet
import org.apache.poi.ss.util.CellRangeAddressList
import java.lang.reflect.Field
import kotlin.io.path.createTempFile

/**
 * 字典提供者注册器
 */
object DictProviderRegistry {

    // 存储手动注册的 Provider
    private val providers = linkedSetOf<DictProvider>()

    /**
     * 注册一个自定义的 DictProvider
     *
     * @param provider 字典提供者实例
     */
    fun registerProvider(provider: DictProvider): DictProviderRegistry {
        providers.addFirst(provider)
        return this
    }

    /**
     * 注册一个自定义的 DictProvider
     *
     * @param provider 字典提供者实例
     */
    fun registerProviderLast(provider: DictProvider): DictProviderRegistry {
        providers.addLast(provider)
        return this
    }

    /**
     * 获取字典提供者 (核心查找逻辑)
     *
     * @param dictCode 字典编码
     * @return DictProvider 实例，如果找不到则返回 null
     */
    fun getMapping(dictCode: String): Map<String, String> {

        for (it in providers) {
            if (it.support(dictCode)) {
                return it.mapping(dictCode)
            }
        }

        return emptyMap()
    }
}

/**
 * 字典提供者
 *
 * <AUTHOR>
 */
interface DictProvider {

    /**
     * 是否支持指定的字典编码
     *
     * @param code 字典编码
     * @return 是否支持
     */
    fun support(code: String): Boolean

    /**
     * 根据字典编码获取字典映射关系
     *
     * @param code 字典编码
     * @return 字典映射关系
     */
    fun mapping(code: String): Map<String, String> {
        return mapping()
    }

    /**
     * 获取字典映射关系
     *
     * @return 字典映射关系
     */
    fun mapping(): Map<String, String>
}


/**
 * 枚举字典提供者
 *
 * @param enum 枚举类的 Class 对象
 * <AUTHOR>
 */
class EnumDictProvider(val code: String = enum.simpleName.lowercase(), val enum: Class<out Enum<*>>) : DictProvider {

    override fun support(code: String): Boolean {
        return this.code == code
    }

    /**
     * 获取字典映射关系
     *
     * @param code 字典编码（对于枚举字典提供者，此参数被忽略）
     * @return 枚举常量名称到显示值的映射
     * @throws IllegalArgumentException 当枚举类无效时
     */
    override fun mapping(): Map<String, String> {
        return try {
            extractEnumMapping()
        } catch (e: Exception) {
            throw IllegalArgumentException("无法从枚举类 [${enum.simpleName}] 提取字典映射: ${e.message}", e)
        }
    }

    /**
     * 提取枚举映射关系
     *
     * @return 枚举常量名称到显示值的映射
     */
    private fun extractEnumMapping(): Map<String, String> {
        val enumConstants = enum.enumConstants
        if (enumConstants.isNullOrEmpty()) {
            return emptyMap()
        }

        return enumConstants.associate { enumConstant ->
            val enumValue = enumConstant as Enum<*>
            val name = enumValue.name
            val displayValue = extractDisplayValue(enumValue)
            name to displayValue
        }
    }

    /**
     * 提取枚举常量的显示值
     *
     * 优先级：
     * 1. KDoc 注释内容（去除前后空白）
     * 2. 枚举常量名称
     *
     * @param enumConstant 枚举常量
     * @return 显示值
     */
    private fun extractDisplayValue(enumConstant: Enum<*>): String {
        return try {
            // 尝试获取 KDoc 注释
            val field = enum.getField(enumConstant.name)
            val kdocComment = extractKDocComment(field)

            kdocComment.ifBlank {
                // 回退到枚举名称
                enumConstant.name
            }
        } catch (e: Exception) {
            // 如果获取注释失败，使用枚举名称
            enumConstant.name
        }
    }

    /**
     * 从字段中提取显示值
     *
     * @param field 枚举字段
     * @return 显示值，如果不可用则返回空字符串
     */
    private fun extractKDocComment(field: Field): String {
        // 1. 检查 @JsonProperty 注解
        try {
            val jsonPropertyClass = Class.forName("com.fasterxml.jackson.annotation.JsonProperty")
            val jsonProperty = field.getAnnotation(jsonPropertyClass as Class<Annotation>)
            if (jsonProperty != null) {
                val valueMethod = jsonPropertyClass.getMethod("value")
                val value = valueMethod.invoke(jsonProperty) as String
                if (value.isNotBlank()) {
                    return value
                }
            }
        } catch (e: Exception) {
            // JsonProperty 注解不可用，继续尝试其他方式
        }

        // 2. 可以在这里添加对其他注解的支持
        // 例如：@EnumName、@Description、@Label 等

        // 3. 检查其他可能的显示值注解
        field.annotations.forEach { annotation ->
            when (annotation.annotationClass.simpleName) {
                "EnumName" -> {
                    try {
                        val valueMethod = annotation.annotationClass.java.getMethod("value")
                        val value = valueMethod.invoke(annotation) as String
                        if (value.isNotBlank()) return value
                    } catch (e: Exception) { /* 忽略 */
                    }
                }

                "Description" -> {
                    try {
                        val valueMethod = annotation.annotationClass.java.getMethod("value")
                        val value = valueMethod.invoke(annotation) as String
                        if (value.isNotBlank()) return value
                    } catch (e: Exception) { /* 忽略 */
                    }
                }
            }
        }

        // 4. 在标准的 JVM 运行时环境中，KDoc 注释信息通常不可用
        // 这里为未来扩展预留

        return ""
    }

}

/**
 * Map 字典提供者
 *
 * <AUTHOR>
 */
class MapDictProvider(val code: String, val dict: Map<String, String>) : DictProvider {

    override fun support(code: String): Boolean {
        return this.code == code
    }

    override fun mapping(): Map<String, String> {
        return dict
    }
}


/**
 * 字典格式化
 *
 * <AUTHOR>
 */
@Target(AnnotationTarget.FIELD)
@Retention(AnnotationRetention.RUNTIME)
annotation class DictFormat(
    /**
     * 字典编码
     */
    val value: String,
    /**
     * 下拉配置
     */
    val range: Range = Range()
)

/**
 * 字典下拉行范围
 */
annotation class Range(
    /**
     * 起始行
     */
    val start: Int = 1,
    /**
     * 结束行
     */
    val end: Int = 10
)

/**
 * 自定义显示名称注解
 */
@Target(AnnotationTarget.FIELD)
@Retention(AnnotationRetention.RUNTIME)
annotation class EnumName(
    val value: String
)


/**
 * 示例枚举 - 用户状态 (使用 @EnumName 注解)
 */
enum class UserStatus {
    /**
     * 活跃用户
     */
    @EnumName("活跃")
    ACTIVE,

    /**
     * 非活跃用户
     */
    @EnumName("非活跃")
    INACTIVE,

    /**
     * 已禁用
     */
    @EnumName("已禁用")
    DISABLED
}

/**
 * 示例枚举 - 优先级 (使用字典映射)
 */
enum class Priority {
    /**
     * 高优先级
     */
    HIGH,

    /**
     * 中等优先级
     */
    MEDIUM,

    /**
     * 低优先级
     */
    LOW
}

/**
 * 抽象字典转换器基类
 *
 * 提供通用的字典转换逻辑，支持多种Java类型的字典转换
 *
 * @param T 目标Java类型
 * <AUTHOR>
 */
abstract class AbstractDictConverter<T> : Converter<T> {

    /**
     * 获取支持的Excel单元格数据类型
     * 子类可以重写此方法以支持不同的Excel数据类型
     */
    override fun supportExcelTypeKey(): CellDataTypeEnum {
        return CellDataTypeEnum.STRING
    }

    /**
     * 从Excel单元格数据转换为Java对象
     */
    override fun convertToJavaData(
        cellData: ReadCellData<*>,
        contentProperty: ExcelContentProperty?,
        globalConfiguration: GlobalConfiguration
    ): T? {
        try {
            // 从Excel单元格中获取原始字符串值
            val cellValue = extractCellValue(cellData)

            if (cellValue.isNullOrBlank()) {
                return null
            }

            // 从content属性获取字段信息
            val field = contentProperty?.field
            if (field != null) {
                val dictFormat = field.getAnnotation(DictFormat::class.java)
                if (dictFormat != null) {
                    // 使用字典映射将显示值转换为实际值
                    return convertFromDisplayValue(field, cellValue, dictFormat)
                }
            }

            // 如果不需要字典转换，则转换为目标类型
            return convertStringToTargetType(field,cellValue)

        } catch (e: Exception) {
            throw RuntimeException("Failed to convert Excel cell data to Java value: ${e.message}", e)
        }
    }

    /**
     * 从Java对象转换为Excel单元格数据
     */
    override fun convertToExcelData(
        value: T?,
        contentProperty: ExcelContentProperty?,
        globalConfiguration: GlobalConfiguration
    ): WriteCellData<*> {
        try {
            if (value == null) {
                return WriteCellData<String>()
            }

            // 获取字段信息
            val field = contentProperty?.field
            if (field != null) {
                val dictFormat = field.getAnnotation(DictFormat::class.java)
                if (dictFormat != null) {
                    // 将实际值转换为显示值
                    val displayValue = convertToDisplayValue(field, value, dictFormat)
                    return WriteCellData<String>(displayValue)
                }
            }

            // 如果不需要字典转换，则直接返回字符串表示
            return WriteCellData<String>(convertTargetTypeToString(field, value))

        } catch (e: Exception) {
            throw RuntimeException("Failed to convert Java value to Excel cell data: ${e.message}", e)
        }
    }

    /**
     * 从Excel单元格中提取字符串值
     */
    protected fun extractCellValue(cellData: ReadCellData<*>): String? {
        return when (cellData.type) {
            CellDataTypeEnum.STRING -> cellData.stringValue
            CellDataTypeEnum.NUMBER -> cellData.numberValue?.toString()
            CellDataTypeEnum.BOOLEAN -> cellData.booleanValue?.toString()
            else -> cellData.stringValue
        }
    }

    /**
     * 将显示值转换为实际值
     */
    protected open fun convertFromDisplayValue(field: Field, displayValue: String, dictFormat: DictFormat): T? {
        try {
            val mapping = DictProviderRegistry.getMapping(dictFormat.value)
            val reverseMapping = mapping.map { it.value to it.key }.toMap()
            val actualValue = reverseMapping[displayValue]

            // 如果没有找到映射，检查是否已经是实际值
            if (actualValue == null) {
                if (reverseMapping.values.contains(displayValue)) {
                    // 输入已经是实际值，直接转换类型
                    return convertStringToTargetType(field,displayValue)
                }
                // 记录警告但不失败
                println("Warning: No mapping found for display value '$displayValue' in dict type '${dictFormat.value}', using original value")
                return convertStringToTargetType(field,displayValue)
            }

            return convertStringToTargetType(field,actualValue)
        } catch (e: Exception) {
            println("Error converting display value '$displayValue' for dict type '${dictFormat.value}': ${e.message}")
            return convertStringToTargetType(field,displayValue)
        }
    }

    /**
     * 将实际值转换为显示值
     */
    protected open fun convertToDisplayValue(field: Field, actualValue: T, dictFormat: DictFormat): String {
        try {
            val mapping = DictProviderRegistry.getMapping(dictFormat.value)
            val actualValueStr = convertTargetTypeToString(field,actualValue)

            // 尝试查找映射
            val displayValue = mapping[actualValueStr]

            // 如果没有找到映射，检查是否已经是显示值
            if (displayValue == null) {
                val reverseMapping = mapping.map { it.value to it.key }.toMap()
                if (reverseMapping.containsKey(actualValueStr)) {
                    // 输入已经是显示值，直接返回
                    return actualValueStr
                }
                // 记录警告但不失败
                println("Warning: No reverse mapping found for actual value '$actualValueStr' in dict type '${dictFormat.value}', using original value")
                return actualValueStr
            }

            return displayValue
        } catch (e: Exception) {
            println("Error converting actual value '$actualValue' for dict type '${dictFormat.value}': ${e.message}")
            return convertTargetTypeToString(field,actualValue)
        }
    }

    /**
     * 将字符串转换为目标类型
     * 子类必须实现此方法以支持特定的类型转换
     */
    protected abstract fun convertStringToTargetType(field: Field?, value: String): T?

    /**
     * 将目标类型转换为字符串
     * 子类可以重写此方法以自定义字符串表示
     */
    protected open fun convertTargetTypeToString(field: Field?, value: T): String {
        return value.toString()
    }
}

/**
 * 字符串字典转换器 (重构后的DictConverter)
 *
 * <AUTHOR>
 */
class StringDictConverter : AbstractDictConverter<String>() {

    override fun supportJavaTypeKey(): Class<String> {
        return String::class.java
    }

    override fun convertStringToTargetType(field: Field?, value: String): String {
        return value
    }
}


/**
 * 布尔字典转换器
 *
 * 支持Boolean类型的字典转换，可以处理各种布尔值表示到Boolean的转换
 *
 * <AUTHOR>
 */
class BooleanDictConverter : AbstractDictConverter<Boolean>() {

    override fun supportJavaTypeKey(): Class<Boolean> {
        return Boolean::class.java
    }

    override fun supportExcelTypeKey(): CellDataTypeEnum {
        // Boolean类型可以支持布尔和字符串类型的Excel单元格
        return CellDataTypeEnum.BOOLEAN
    }

    override fun convertStringToTargetType(field: Field?, value: String): Boolean? {
        return try {
            when (value.lowercase()) {
                "true", "1", "yes", "y", "on", "enabled", "启用" -> true
                "false", "0", "no", "n", "off", "disabled", "禁用" -> false
                else -> {
                    // 尝试标准布尔转换
                    value.toBoolean()
                }
            }
        } catch (e: Exception) {
            println("Warning: Cannot convert '$value' to Boolean, returning null")
            null
        }
    }

    override fun convertTargetTypeToString(field: Field?, value: Boolean): String {
        return value.toString()
    }
}

/**
 * 整数字典转换器
 *
 * 支持Integer类型的字典转换，可以处理数字字符串到Integer的转换
 *
 * <AUTHOR>
 */
class IntegerDictConverter : AbstractDictConverter<Int>() {

    override fun supportJavaTypeKey(): Class<Int> {
        return Int::class.java
    }

    override fun supportExcelTypeKey(): CellDataTypeEnum {
        // Integer类型可以支持数字和字符串类型的Excel单元格
        return CellDataTypeEnum.NUMBER
    }

    override fun convertStringToTargetType(field: Field?, value: String): Int? {
        return try {
            value.toInt()
        } catch (e: NumberFormatException) {
            println("Warning: Cannot convert '$value' to Integer, returning null")
            null
        }
    }

    override fun convertTargetTypeToString(field: Field?, value: Int): String {
        return value.toString()
    }
}

/**
 * 长整数字典转换器
 *
 * 支持Long类型的字典转换，可以处理长整数字符串到Long的转换
 *
 * <AUTHOR>
 */
class LongDictConverter : AbstractDictConverter<Long>() {

    override fun supportJavaTypeKey(): Class<Long> {
        return Long::class.java
    }

    override fun supportExcelTypeKey(): CellDataTypeEnum {
        return CellDataTypeEnum.NUMBER
    }

    override fun convertStringToTargetType(field: Field?, value: String): Long? {
        return try {
            value.toLong()
        } catch (e: NumberFormatException) {
            println("Warning: Cannot convert '$value' to Long, returning null")
            null
        }
    }

    override fun convertTargetTypeToString(field: Field?, value: Long): String {
        return value.toString()
    }
}

/**
 * 双精度浮点数字典转换器
 *
 * 支持Double类型的字典转换，可以处理浮点数字符串到Double的转换
 *
 * <AUTHOR>
 */
class DoubleDictConverter : AbstractDictConverter<Double>() {

    override fun supportJavaTypeKey(): Class<Double> {
        return Double::class.java
    }

    override fun supportExcelTypeKey(): CellDataTypeEnum {
        return CellDataTypeEnum.NUMBER
    }

    override fun convertStringToTargetType(field: Field?, value: String): Double? {
        return try {
            value.toDouble()
        } catch (e: NumberFormatException) {
            println("Warning: Cannot convert '$value' to Double, returning null")
            null
        }
    }

    override fun convertTargetTypeToString(field: Field?, value: Double): String {
        return value.toString()
    }
}


/**
 * 自动列宽处理器
 *
 * <AUTHOR>
 */
class AutoWidthWriteHandler : SheetWriteHandler {

    override fun afterSheetCreate(context: SheetWriteHandlerContext) {
        val sheet = context.writeSheetHolder.sheet
        val dataClass = context.writeSheetHolder.clazz

        dataClass?.declaredFields?.forEachIndexed { index, field ->
            val excelProperty = field.getAnnotation(ExcelProperty::class.java)
            val columnWidth = calculateOptimalWidth(field.name, excelProperty?.value?.firstOrNull())
            sheet.setColumnWidth(index, columnWidth)
        }
    }

    private fun calculateOptimalWidth(fieldName: String, headerName: String?): Int {
        val displayName = headerName ?: fieldName

        val baseWidth = maxOf(2, minOf(20, displayName.length)) + 5
        // OOXML 规范一个字符 255个单位，最大 256字符，此处简化使用，默认一个文字占用两个字符
        return baseWidth * 512
    }
}

/**
 * 字典下拉处理器
 *
 * 自动解析字典注解，生成下拉选项
 *
 * <AUTHOR>
 */
class DictDropdownWriteHandler : SheetWriteHandler {

    override fun afterSheetCreate(context: SheetWriteHandlerContext) {
        val sheet = context.writeSheetHolder.sheet
        val dataClass = context.writeSheetHolder.clazz

        if (dataClass != null) {
            createDropdownValidations(sheet, dataClass)
        }
    }

    private fun createDropdownValidations(sheet: Sheet, dataClass: Class<*>) {
        dataClass.declaredFields.forEachIndexed { index, field ->
            val dictFormat = field.getAnnotation(DictFormat::class.java)
            if (dictFormat != null) {
                createDropdownForField(sheet, index, dictFormat)
            }
        }
    }

    private fun createDropdownForField(sheet: Sheet, columnIndex: Int, dictFormat: DictFormat) {
        val mapping = DictProviderRegistry.getMapping(dictFormat.value)
        if (mapping.isNotEmpty()) {
            val config = dictFormat.range

            // Create cell range for validation
            val range = CellRangeAddressList(config.start, config.end, columnIndex, columnIndex)

            // Create validation constraint
            val helper = sheet.dataValidationHelper
            val constraint = helper.createExplicitListConstraint(mapping.values.toTypedArray())
            val validation = helper.createValidation(constraint, range)

            // Configure validation
            validation.showErrorBox = true
            validation.emptyCellAllowed = false
            validation.createErrorBox("输入错误", "请从下拉列表中选择有效值")
            validation.showPromptBox = true
            validation.createPromptBox("提示", "请选择: ${mapping.values.joinToString(", ")}")

            // Add validation to sheet
            sheet.addValidationData(validation)
        }
    }
}

/**
 * Enhanced Demo Data Class with Dictionary Annotations (String types - backward compatibility)
 */
data class DemoData(
    @ExcelProperty("姓名")
    var name: String,

    @ExcelProperty("年龄")
    var age: Int,

    @DictFormat(value = "gender")
    @ExcelProperty("性别")
    var gender: String,

    @DictFormat(value = "status")
    @ExcelProperty("状态")
    var status: String,

    @ExcelProperty("部门")
    var department: String,

    @ExcelProperty("备注")
    var remark: String,

    @DictFormat(value = "userStatus")
    @ExcelProperty("用户状态")
    var userStatus: String  // 使用字符串存储枚举值，通过字典转换显示
)

/**
 * 多类型字典转换示例数据类
 *
 * 展示如何使用不同类型的字典转换器
 */
data class MultiTypeDemoData(
    @ExcelProperty("姓名")
    var name: String,

    @DictFormat(value = "genderInt")
    @ExcelProperty("性别编码")
    var genderCode: Int,  // 使用Integer类型存储性别编码

    @DictFormat(value = "statusBool")
    @ExcelProperty("启用状态")
    var enabled: Boolean,  // 使用Boolean类型存储状态

    @DictFormat(value = "priority")
    @ExcelProperty("优先级")
    var priority: String,  // 字符串类型的优先级

    @ExcelProperty("薪资")
    var salary: Double,

    @DictFormat(value = "departmentId")
    @ExcelProperty("部门ID")
    var departmentId: Long,  // 使用Long类型存储部门ID

    @DictFormat(value = "userStatus")
    @ExcelProperty("用户状态")
    var userStatus: UserStatus  // 使用枚举类型存储用户状态
)

/**
 * 枚举转换示例数据类
 *
 * 专门展示枚举类型的字典转换
 */
data class EnumDemoData(
    @ExcelProperty("姓名")
    var name: String,

    @DictFormat(value = "userStatus")
    @ExcelProperty("用户状态")
    var status: UserStatus,  // 枚举类型，使用 @EnumName 注解显示名称

    @DictFormat(value = "priority")
    @ExcelProperty("优先级")
    var priority: Priority,  // 枚举类型，使用字典映射显示名称

    @ExcelProperty("创建时间")
    var createTime: String
)

val fileName = "simpleWrite" + System.currentTimeMillis() + ".xlsx"
val createTempFile = createTempFile(suffix = fileName)

// Create test data with actual dictionary values (these will be converted to display values in Excel)
val data = (1..10).map { index ->
    val genderValues = listOf("M", "F", "U")
    val statusValues = listOf("1", "0", "2")  // String representation of status values
    val userStatusValues = listOf("ACTIVE", "INACTIVE", "DISABLED")  // 枚举名称

    DemoData(
        name = "用户$index",
        age = 20 + index,
        gender = genderValues[index % 3],  // Will be converted to "男", "女", "未知"
        status = statusValues[index % 3],  // Will be converted to "启用", "禁用", "待审核"
        department = "技术部",
        remark = "备注信息$index",
        userStatus = userStatusValues[index % 3]  // Will be converted using EnumDictProvider
    )
}


// ========== 字典提供者注册 ==========

// 字符串类型字典 (向后兼容)
DictProviderRegistry.registerProvider(MapDictProvider("gender", mapOf("M" to "男", "F" to "女", "U" to "未知")))
DictProviderRegistry.registerProvider(MapDictProvider("status", mapOf("1" to "启用", "0" to "禁用", "2" to "待审核")))

// 整数类型字典 (性别编码)
DictProviderRegistry.registerProvider(MapDictProvider("genderInt", mapOf("1" to "男", "2" to "女", "0" to "未知")))

// 布尔类型字典 (启用状态)
DictProviderRegistry.registerProvider(MapDictProvider("statusBool", mapOf("true" to "启用", "false" to "禁用")))

// 字符串类型字典 (优先级)
DictProviderRegistry.registerProvider(
    MapDictProvider(
        "priority",
        mapOf("HIGH" to "高", "MEDIUM" to "中", "LOW" to "低")
    )
)

// 长整数类型字典 (部门ID)
DictProviderRegistry.registerProvider(
    MapDictProvider(
        "departmentId",
        mapOf("1001" to "技术部", "1002" to "产品部", "1003" to "运营部")
    )
)

// 注册枚举字典提供者 (使用现有的EnumDictProvider)
DictProviderRegistry.registerProvider(EnumDictProvider("userStatus", UserStatus::class.java))

// 注册Priority枚举的字典映射 (演示枚举使用字典映射的方式)
DictProviderRegistry.registerProvider(
    MapDictProvider(
        "priorityEnum",
        mapOf("HIGH" to "高优先级", "MEDIUM" to "中等优先级", "LOW" to "低优先级")
    )
)

// ========== 示例1: 向后兼容的字符串类型转换 ==========
println("=== 示例1: 向后兼容的字符串类型转换 ===")

FastExcel.write(createTempFile.toFile(), DemoData::class.java)
    .sheet("String类型示例")
    .registerWriteHandler(AutoWidthWriteHandler())
    .registerWriteHandler(DictDropdownWriteHandler())
    .registerConverter(StringDictConverter())  // 使用原有的DictConverter (向后兼容)
    .doWrite(data)

// ========== 示例2: 多类型字典转换 ==========
println("=== 示例2: 多类型字典转换 ===")

// 创建多类型示例数据
val multiTypeData = (1..5).map { index ->
    MultiTypeDemoData(
        name = "员工$index",
        genderCode = listOf(1, 2, 0)[index % 3],  // Integer类型，将转换为 "男", "女", "未知"
        enabled = index % 2 == 1,  // Boolean类型，将转换为 "启用", "禁用"
        priority = listOf("HIGH", "MEDIUM", "LOW")[index % 3],  // String类型，将转换为 "高", "中", "低"
        salary = 5000.0 + index * 1000,
        departmentId = listOf(1001L, 1002L, 1003L)[index % 3],  // Long类型，将转换为部门名称
        userStatus = listOf(UserStatus.ACTIVE, UserStatus.INACTIVE, UserStatus.DISABLED)[index % 3]  // 枚举类型
    )
}

// 创建枚举示例数据
val enumData = (1..5).map { index ->
    EnumDemoData(
        name = "用户$index",
        status = listOf(UserStatus.ACTIVE, UserStatus.INACTIVE, UserStatus.DISABLED)[index % 3],  // 使用@EnumName注解显示
        priority = listOf(Priority.HIGH, Priority.MEDIUM, Priority.LOW)[index % 3],  // 使用字典映射显示
        createTime = "2024-01-${10 + index}"
    )
}

val multiTypeFile = createTempFile(suffix = "multiType_${System.currentTimeMillis()}.xlsx")


FastExcel.write(multiTypeFile.toFile())
    .init(MultiTypeDemoData::class.java)
    .sheet("多类型示例")
    .registerWriteHandler(AutoWidthWriteHandler())
    .registerWriteHandler(DictDropdownWriteHandler())
    .doWrite(multiTypeData)


@Suppress("UNCHECKED_CAST")
fun <T : AbstractParameterBuilder<T, *>> AbstractParameterBuilder<T, *>.init(clazz: Class<*>): T {

    return head(clazz)
        .registerConverter(StringDictConverter())
        .registerConverter(IntegerDictConverter())
        .registerConverter(BooleanDictConverter())
        .registerConverter(LongDictConverter())
}

/**
 * 批量注册转换器
 *
 * @param converters 转换器列表
 * @return 当前对象
 */
@Suppress("UNCHECKED_CAST")
fun <T : AbstractParameterBuilder<T, *>> AbstractParameterBuilder<T, *>.registerConverters(converters: List<Converter<*>>): T {
    converters.forEach {
        registerConverter(it)
    }
    return this as T
}

// ========== 示例3: 枚举类型字典转换 ==========
println("=== 示例3: 枚举类型字典转换 ===")

val enumFile = createTempFile(suffix = "enum_${System.currentTimeMillis()}.xlsx")

FastExcel.write(enumFile.toFile(), EnumDemoData::class.java)
    .sheet("枚举示例")
    .registerWriteHandler(AutoWidthWriteHandler())
    .registerWriteHandler(DictDropdownWriteHandler())
    .doWrite(enumData)
