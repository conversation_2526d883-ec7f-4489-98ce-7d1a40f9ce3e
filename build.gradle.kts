// 解决多项目版本重复加载问题 见 https://docs.gradle.org/current/userguide/plugins.html#sec:subprojects_plugins_dsl
plugins {
    alias(commonLibs.plugins.kotlin.jvm) apply false
    alias(commonLibs.plugins.kapt) apply false
}

subprojects {

    configurations.all {
        // 禁用缓存
        resolutionStrategy.cacheChangingModulesFor(0, TimeUnit.SECONDS)
        // 每次都强制检查更新
        resolutionStrategy.cacheDynamicVersionsFor(0, TimeUnit.SECONDS)
    }
}
