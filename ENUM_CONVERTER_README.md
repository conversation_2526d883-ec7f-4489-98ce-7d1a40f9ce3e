# 枚举字典转换器 (EnumDictConverter)

## 概述

`EnumDictConverter<E>` 是基于 `AbstractDictConverter<T>` 架构的枚举类型字典转换器，专门用于处理枚举类型与Excel显示值之间的转换。

## 特性

### ✅ **双重显示名称支持**
1. **@EnumName 注解方式**: 直接在枚举常量上使用注解定义显示名称
2. **字典映射方式**: 通过 `DictProviderRegistry` 注册字典映射

### ✅ **智能转换逻辑**
- 优先使用字典映射（如果存在）
- 回退到 @EnumName 注解显示名称
- 最终回退到枚举名称本身

### ✅ **多种匹配方式**
- 精确枚举名称匹配
- @EnumName 注解显示名称匹配
- 大小写不敏感的枚举名称匹配

### ✅ **错误容错**
- 转换失败时打印警告而不是抛出异常
- 无效输入返回 null 而不是崩溃

## 使用方法

### 1. 定义枚举类型

#### 方式一：使用 @EnumName 注解
```kotlin
enum class UserStatus {
    @EnumName("活跃状态")
    ACTIVE,
    
    @EnumName("非活跃状态")
    INACTIVE,
    
    @EnumName("已禁用状态")
    DISABLED
}
```

#### 方式二：使用字典映射
```kotlin
enum class Priority {
    HIGH,    // 将通过字典映射显示为 "高优先级"
    MEDIUM,  // 将通过字典映射显示为 "中等优先级"
    LOW      // 将通过字典映射显示为 "低优先级"
}

// 注册字典映射
DictProviderRegistry.registerProvider(
    MapDictProvider("priority", mapOf(
        "HIGH" to "高优先级",
        "MEDIUM" to "中等优先级", 
        "LOW" to "低优先级"
    ))
)
```

### 2. 在数据类中使用

```kotlin
data class UserData(
    @ExcelProperty("姓名")
    var name: String,
    
    @DictFormat(value = "userStatus")  // 对应字典编码
    @ExcelProperty("用户状态")
    var status: UserStatus,  // 使用 @EnumName 注解显示
    
    @DictFormat(value = "priority")    // 对应字典编码
    @ExcelProperty("优先级")
    var priority: Priority   // 使用字典映射显示
)
```

### 3. 注册转换器

```kotlin
FastExcel.write(file, UserData::class.java)
    .sheet("用户数据")
    .registerConverter(EnumDictConverter(UserStatus::class.java))
    .registerConverter(EnumDictConverter(Priority::class.java))
    .doWrite(data)
```

## 转换示例

### @EnumName 注解方式

| 枚举值 | Excel显示值 | 说明 |
|--------|-------------|------|
| `UserStatus.ACTIVE` | "活跃状态" | 使用 @EnumName 注解值 |
| `UserStatus.INACTIVE` | "非活跃状态" | 使用 @EnumName 注解值 |
| `UserStatus.DISABLED` | "已禁用状态" | 使用 @EnumName 注解值 |

### 字典映射方式

| 枚举值 | Excel显示值 | 说明 |
|--------|-------------|------|
| `Priority.HIGH` | "高优先级" | 通过字典映射 |
| `Priority.MEDIUM` | "中等优先级" | 通过字典映射 |
| `Priority.LOW` | "低优先级" | 通过字典映射 |

## 转换优先级

转换器按以下优先级处理显示名称：

1. **字典映射** (最高优先级)
   - 如果 `DictProviderRegistry` 中存在对应的字典映射，优先使用

2. **@EnumName 注解**
   - 如果枚举常量有 @EnumName 注解，使用注解值

3. **枚举名称** (最低优先级)
   - 直接使用枚举常量的名称

## 反向转换支持

转换器支持从Excel显示值反向转换为枚举值：

```kotlin
// 输入: "活跃状态" -> 输出: UserStatus.ACTIVE
// 输入: "高优先级" -> 输出: Priority.HIGH
// 输入: "ACTIVE" -> 输出: UserStatus.ACTIVE (枚举名称)
// 输入: "active" -> 输出: UserStatus.ACTIVE (大小写不敏感)
```

## 错误处理

- **无效输入**: 打印警告信息，返回 null
- **类型转换失败**: 打印错误信息，返回 null
- **字典映射缺失**: 回退到其他显示方式

## 测试验证

运行 `enum-converter-test.main.kts` 可以验证枚举转换器的功能：

```bash
kotlin enum-converter-test.main.kts
```

测试覆盖：
- ✅ @EnumName 注解方式转换
- ✅ 字典映射方式转换
- ✅ 大小写不敏感匹配
- ✅ 所有枚举值的双向转换
- ✅ 错误处理和容错机制

## 与其他转换器的集成

`EnumDictConverter` 可以与其他类型转换器一起使用：

```kotlin
FastExcel.write(file, ComplexData::class.java)
    .registerConverter(StringDictConverter())           // 字符串
    .registerConverter(IntegerDictConverter())          // 整数
    .registerConverter(BooleanDictConverter())          // 布尔
    .registerConverter(EnumDictConverter(Status::class.java))     // 枚举1
    .registerConverter(EnumDictConverter(Priority::class.java))   // 枚举2
    .doWrite(data)
```

## 最佳实践

1. **选择合适的显示方式**:
   - 简单的显示名称使用 @EnumName 注解
   - 复杂的或需要动态配置的使用字典映射

2. **保持一致性**:
   - 同一个枚举类型在整个项目中使用统一的显示方式

3. **错误处理**:
   - 监控转换警告日志，及时处理数据质量问题

4. **性能考虑**:
   - 字典映射查找比反射获取注解更高效
   - 对于高频转换的枚举，推荐使用字典映射方式
