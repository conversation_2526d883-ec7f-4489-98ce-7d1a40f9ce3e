# Murmuration 工作流引擎详细设计文档

## 1. 核心实体与关系

工作流引擎的核心实体遵循定义与实例分离的设计模式，确保工作流模板的可复用性和运行时状态的独立管理。

```mermaid
classDiagram
    %% 工作流层次
    class WorkflowDefinition {
        +Long id
        +String name
        +String category
        +List<NodeDefinition> nodeDefinitions
        +List<Edge> edges
    }

    class WorkflowInstance {
        +Long id
        +String serialNumber
        +WorkflowMode mode
        +WorkflowStatus status
        +WorkflowInstanceDetail detail
        +List<NodeInstance> nodeInstances
    }

    class WorkflowInstanceDetail {
        +Long workflowInstanceId
        +Map<String, String> storage
        +String input
        +String parameter
    }

    %% 节点层次
    class NodeDefinition {
        +Long id
        +String nodeKey
        +Boolean review
        +Boolean skip
        +Map<String, String> input
        +Map<String, String> parameter
        +NodeMetadata nodeMetadata
    }

    class NodeInstance {
        +Long id
        +Supplier supplier
        +Ability ability
        +NodeStatus status
        +NodeInstanceDetail detail
        +List<TaskInstance> taskInstances
    }

    class NodeInstanceDetail {
        +Long nodeInstanceId
        +Map<String, String> storage
        +Object input
        +Object parameter
        +Object output
        +String error
    }

    %% 工作流节点抽象
    class WorkflowNode~I, P, O~ {
        <<interface>>
        +NodeType type
        +execute(context, input, parameter) NodeResult<O>
    }

    class TaskNode~I, P, O~ {
        <<abstract>>
        +NodeType type = TASK
        +TaskManager taskManager
        +execute(context, input, parameter) NodeResult<O>
        +createTask(context, input, parameter)*
        +assembleOutput(context, taskResults)* O
    }

    %% 上下文管理
    class WorkflowContext {
        +WorkflowInstance workflowInstance
        +MutableMap<String, String> storage
        +setStorage(key, value)
        +getStorage<T>(key) T
    }

    class WorkflowNodeContext {
        +WorkflowContext workflowContext
        +NodeInstance node
        +MutableMap<String, String> storage
    }

    %% 关系定义
    WorkflowDefinition -- NodeDefinition : "contains"
    WorkflowInstance -- WorkflowDefinition : "instantiates"
    WorkflowInstance -- NodeInstance : "contains"
    WorkflowInstance -- WorkflowInstanceDetail : "has"
    
    NodeInstance -- NodeDefinition : "instantiates"
    NodeInstance -- NodeInstanceDetail : "has"
    
    WorkflowNode <|.. TaskNode : "implements"
    
    WorkflowContext *-- WorkflowInstance : "contains"
    WorkflowNodeContext *-- WorkflowContext : "contains"
    WorkflowNodeContext *-- NodeInstance : "contains"

    %% 枚举类型
    class WorkflowStatus {
        <<enumeration>>
        START
        RUNNING
        PAUSED
        COMPLETED
        CANCELLED
        FAILED
    }

    class NodeStatus {
        <<enumeration>>
        PREPARE
        READY
        RUNNING
        REVIEWING
        REVIEWED
        COMPLETED
        FAILED
    }

    WorkflowInstance *-- WorkflowStatus : "has"
    NodeInstance *-- NodeStatus : "has"
```

### 关系说明

**定义与实例分离**：
- `WorkflowDefinition` ↔ `WorkflowInstance`：一个工作流定义可以创建多个实例
- `NodeDefinition` ↔ `NodeInstance`：一个节点定义在每个工作流实例中对应一个节点实例

**组合关系**：
- 工作流实例包含多个节点实例，形成 DAG 结构
- 每个实例都有对应的详情实体，存储运行时数据

**继承关系**：
- `TaskNode` 继承自 `WorkflowNode` 接口，提供任务节点的通用实现

## 2. 数据模型

### 2.1 WorkflowInstance 表结构

| 字段名 | 数据类型 | 约束 | 注释 |
|--------|---------|------|------|
| id | BIGINT | PRIMARY KEY | 工作流实例ID |
| workflow_definition_id | BIGINT | NOT NULL, FK | 工作流定义ID |
| serial_number | VARCHAR(50) | NOT NULL, UNIQUE | 工作流流水号 |
| mode | VARCHAR(20) | NOT NULL | 工作流模式(NODE/WORKFLOW) |
| status | VARCHAR(20) | NOT NULL | 工作流状态 |
| tenant_id | BIGINT | NOT NULL | 租户ID |
| organization_id | BIGINT | NOT NULL | 组织ID |
| creator_id | BIGINT | NOT NULL | 创建者ID |
| creator_name | VARCHAR(100) | NOT NULL | 创建者姓名 |
| create_time | TIMESTAMP | NOT NULL | 创建时间 |
| update_time | TIMESTAMP | NOT NULL | 更新时间 |

**索引设计**：
- PRIMARY KEY: `id`
- INDEX: `idx_workflow_definition_id` ON `workflow_definition_id`
- INDEX: `idx_serial_number` ON `serial_number`
- INDEX: `idx_status_create_time` ON `status, create_time`
- INDEX: `idx_tenant_org` ON `tenant_id, organization_id`

### 2.2 NodeInstance 表结构

| 字段名 | 数据类型 | 约束 | 注释 |
|--------|---------|------|------|
| id | BIGINT | PRIMARY KEY | 节点实例ID |
| workflow_instance_id | BIGINT | NOT NULL, FK | 工作流实例ID |
| node_definition_id | BIGINT | NOT NULL, FK | 节点定义ID |
| supplier | VARCHAR(50) | NOT NULL | AI服务供应商 |
| ability | VARCHAR(50) | NOT NULL | AI能力标识 |
| status | VARCHAR(20) | NOT NULL | 节点状态 |
| create_time | TIMESTAMP | NOT NULL | 创建时间 |
| update_time | TIMESTAMP | NOT NULL | 更新时间 |

**索引设计**：
- PRIMARY KEY: `id`
- INDEX: `idx_workflow_instance_id` ON `workflow_instance_id`
- INDEX: `idx_node_definition_id` ON `node_definition_id`
- INDEX: `idx_supplier_ability` ON `supplier, ability`
- INDEX: `idx_status_update_time` ON `status, update_time`
- UNIQUE INDEX: `uk_workflow_node_definition` ON `workflow_instance_id, node_definition_id`

### 2.3 相关详情表

**WorkflowInstanceDetail**：存储工作流实例的运行时数据
- `storage`: JSON 格式的全局存储
- `input`/`parameter`: 工作流级别的输入参数

**NodeInstanceDetail**：存储节点实例的运行时数据  
- `storage`: JSON 格式的节点级别存储
- `input`/`parameter`/`output`: 节点的输入、参数和输出数据
- `error`/`error_stack_trace`: 错误信息和堆栈跟踪

## 3. 核心执行流程

工作流从启动到第一个节点开始执行的完整序列如下：

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Controller as 工作流控制器
    participant GraphEngine as 图执行引擎
    participant NodeExecutor as 节点执行器
    participant TaskManager as 任务管理器
    participant Scheduler as 调度器

    %% 工作流启动阶段
    Client->>Controller: POST /workflow/start
    Note over Client,Controller: 包含 workflowDefinitionId,<br/>input, parameter, nodeArgs

    Controller->>GraphEngine: startWorkflow(definitionId, input, parameter, nodeArgs)
    
    GraphEngine->>GraphEngine: 1. 加载工作流定义
    GraphEngine->>GraphEngine: 2. 创建工作流实例
    GraphEngine->>GraphEngine: 3. 创建所有节点实例
    GraphEngine->>GraphEngine: 4. 初始化工作流和节点详情
    
    %% 启动节点识别与调度
    GraphEngine->>GraphEngine: 5. 识别启动节点(入度为0)
    loop 每个启动节点
        GraphEngine->>Scheduler: 调度节点执行
        Note over GraphEngine,Scheduler: NodeWork(nodeInstanceId,<br/>supplier, ability)
    end
    
    GraphEngine-->>Controller: 返回 workflowInstanceId
    Controller-->>Client: 200 OK { workflowInstanceId }

    %% 第一个节点执行阶段
    Scheduler->>NodeExecutor: execute(NodeWork)
    
    NodeExecutor->>NodeExecutor: 1. 加载节点实例及相关信息
    Note over NodeExecutor: 包含工作流实例、节点定义、<br/>历史状态等完整信息

    NodeExecutor->>NodeExecutor: 2. 检查工作流和节点状态
    Note over NodeExecutor: 确保工作流RUNNING状态<br/>且节点状态为READY

    NodeExecutor->>NodeExecutor: 3. 构建执行上下文
    Note over NodeExecutor: WorkflowContext + <br/>WorkflowNodeContext

    NodeExecutor->>NodeExecutor: 4. 获取节点实现
    Note over NodeExecutor: 从NodeRegistry获取<br/>对应的WorkflowNode实例

    NodeExecutor->>NodeExecutor: 5. 解析输入参数
    Note over NodeExecutor: 表达式计算 + 类型转换

    %% 节点具体执行
    NodeExecutor->>TaskManager: node.execute(context, input, parameter)
    Note over NodeExecutor,TaskManager: 实际是TaskNode.execute()

    alt 节点状态为 READY
        TaskManager->>TaskManager: createTask()
        TaskManager->>TaskManager: 持久化任务实例
        TaskManager->>Scheduler: 发布TaskPrepareEvent
        TaskManager-->>NodeExecutor: NodeResult.Running
        
        NodeExecutor->>NodeExecutor: 更新节点状态为RUNNING
        NodeExecutor->>Scheduler: 发布NodeRunningEvent
        NodeExecutor-->>Scheduler: WorkStatus.REDO
        Note over NodeExecutor,Scheduler: 需要重新调度检查任务状态
        
    else 节点状态为 RUNNING
        TaskManager->>TaskManager: queryTaskStatus()
        alt 任务仍在运行
            TaskManager-->>NodeExecutor: TaskResult.Running
            NodeExecutor-->>Scheduler: WorkStatus.REDO
        else 任务已完成
            TaskManager-->>NodeExecutor: TaskResult.Completed
            NodeExecutor->>NodeExecutor: 组装节点输出
            NodeExecutor->>NodeExecutor: 更新节点状态为COMPLETED
            NodeExecutor->>Scheduler: 发布NodeCompletedEvent
            NodeExecutor-->>Scheduler: WorkStatus.FINISHED
        end
    end
```

### 执行流程关键点

1. **状态驱动**：整个执行过程基于状态机模式，每个阶段都有明确的状态转换
2. **异步调度**：通过调度器实现异步执行，避免长时间阻塞
3. **事件驱动**：关键状态变更通过事件发布，支持监听和扩展
4. **容错处理**：每个阶段都有异常处理和状态回滚机制

## 4. 上下文管理 (Context Management)

上下文管理是工作流引擎的核心机制，负责在节点间传递数据和维护执行状态。

### 4.1 WorkflowContext - 工作流级别上下文

**设计目的**：
- 维护整个工作流实例的全局状态
- 提供跨节点的数据共享机制
- 管理工作流级别的配置和元数据

**核心功能**：
```kotlin
data class WorkflowContext(
    val workflowInstance: WorkflowInstance,  // 工作流实例信息
    val storage: MutableMap<String, String>  // 全局存储
) {
    fun setStorage(key: String, value: Any)       // 存储数据
    inline fun <reified T> getStorage(key: String): T  // 获取数据
}
```

**数据持久化**：
- 存储在 `WorkflowInstanceDetail.storage` 字段中
- JSON 格式序列化，支持复杂对象存储
- 实时持久化，确保系统重启后状态不丢失

### 4.2 WorkflowNodeContext - 节点级别上下文

**设计目的**：
- 为单个节点提供隔离的执行环境
- 管理节点特定的临时数据和状态
- 提供对工作流全局上下文的访问

**核心结构**：
```kotlin
data class WorkflowNodeContext(
    val workflowContext: WorkflowContext,    // 工作流全局上下文
    val node: NodeInstance,                  // 当前节点实例
    val storage: MutableMap<String, String>  // 节点私有存储
)
```

**访问层次**：
- **节点私有数据**：仅当前节点可访问，用于临时计算和状态保存
- **工作流共享数据**：所有节点可访问，用于跨节点数据传递
- **节点实例信息**：包含节点定义、状态、配置等元数据

### 4.3 数据传递机制

**4.3.1 表达式驱动的参数传递**

节点的输入参数支持表达式语法，实现动态数据获取：

```kotlin
// 节点定义中的表达式示例
val nodeInput = mapOf(
    "imageUrl" to "\${workflow.storage.sourceImage}",      // 从工作流存储获取
    "styleId" to "\${node.output.styleId}",               // 从上游节点输出获取
    "tenantId" to "\${workflow.instance.tenantId}"        // 从工作流实例获取
)
```

**4.3.2 数据隔离与共享策略**

| 数据层次 | 访问范围 | 生命周期 | 用途 |
|----------|----------|----------|------|
| **节点私有存储** | 当前节点 | 节点执行期间 | 临时计算、中间状态 |
| **工作流共享存储** | 所有节点 | 工作流实例生命周期 | 跨节点数据传递 |
| **节点输出** | 下游节点 | 节点完成后持久化 | 节点间数据流转 |

**4.3.3 类型安全的数据转换**

上下文管理支持强类型的数据存储和获取：

```kotlin
// 存储复杂对象
context.setStorage("processedImages", listOf(
    ProcessedImage("url1", "style1"),
    ProcessedImage("url2", "style2")
))

// 类型安全获取
val images: List<ProcessedImage> = context.getStorage("processedImages")
```

### 4.4 上下文生命周期管理

**创建阶段**：
1. 工作流启动时创建 `WorkflowContext`
2. 节点执行前创建对应的 `WorkflowNodeContext`
3. 从数据库加载已有的存储数据

**执行阶段**：
1. 节点通过上下文访问输入数据和全局状态
2. 执行过程中可以更新节点私有存储
3. 节点完成时输出数据写入全局存储

**持久化阶段**：
1. 节点执行完成后，自动持久化存储变更
2. 工作流级别存储写入 `WorkflowInstanceDetail`
3. 节点级别存储写入 `NodeInstanceDetail`

这种设计确保了数据的一致性、隔离性和可恢复性，为复杂工作流的可靠执行提供了坚实的基础。