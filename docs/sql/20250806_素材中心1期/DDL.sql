CREATE TABLE infringement_image
(
    id           BIGINT        NOT NULL COMMENT '主键' PRIMARY KEY,
    url          VARCHAR(2048) NOT NULL COMMENT '图片URL',
    enabled      BOOLEAN       NOT NULL COMMENT '是否启用',
    product_id   VARCHAR(64)   NOT NULL COMMENT '商品ID',
    type         VARCHAR(32)   NOT NULL COMMENT '侵权类型',
    brand        VARCHAR(32)   NOT NULL COMMENT '品牌名称',
    product_link VARCHAR(2048) NOT NULL COMMENT '单品链接',
    category     VARCHAR(255)  NOT NULL COMMENT '服装类目',
    remark       VARCHAR(512)  NULL COMMENT '备注信息',
    created_time DATETIME      NOT NULL COMMENT '创建时间',
    revised_time DATETIME      NOT NULL COMMENT '更新时间',
    creator_id   VARCHAR(64)   NOT NULL COMMENT '创建人',
    creator_name VARCHAR(64)   NOT NULL COMMENT '创建人名称',
    reviser_id   VARCHAR(64)   NOT NULL COMMENT '更新人',
    reviser_name VARCHAR(64)   NOT NULL COMMENT '更新人名称',
    CONSTRAINT uniq_product_id unique (product_id)
) comment '侵权图库';
CREATE INDEX idx_created_time on infringement_image (created_time DESC);

CREATE TABLE muse_same_style_image
(
    id            BIGINT        NOT NULL COMMENT '主键' PRIMARY KEY,
    url           VARCHAR(2048) NOT NULL COMMENT '图片URL',
    enabled       BOOLEAN       NOT NULL COMMENT '是否启用',
    spu_code      VARCHAR(250)  NOT NULL COMMENT 'SPU款号',
    supply_mode   VARCHAR(32)   NOT NULL COMMENT '供给方式',
    category_code VARCHAR(32)   NOT NULL COMMENT '品类',
    created_time  DATETIME      NOT NULL COMMENT '创建时间',
    revised_time  DATETIME      NOT NULL COMMENT '更新时间',
    creator_id    VARCHAR(64)   NOT NULL COMMENT '创建人',
    creator_name  VARCHAR(64)   NOT NULL COMMENT '创建人名称',
    reviser_id    VARCHAR(64)   NOT NULL COMMENT '更新人',
    reviser_name  VARCHAR(64)   NOT NULL COMMENT '更新人名称',
    CONSTRAINT uniq_spu_code unique (spu_code)
) comment 'MUSE同款图库';
CREATE INDEX idx_created_time on muse_same_style_image (created_time DESC);

CREATE TABLE excel_import_log
(
    id           BIGINT        NOT NULL COMMENT '主键' PRIMARY KEY,
    url          VARCHAR(2048) NOT NULL COMMENT '导入文件URL',
    scene        VARCHAR(32)   NOT NULL COMMENT '导入场景',
    status       VARCHAR(32)   NOT NULL COMMENT '导入状态',
    created_time DATETIME      NOT NULL COMMENT '创建时间',
    creator_id   VARCHAR(64)   NOT NULL COMMENT '创建人',
    creator_name VARCHAR(64) NOT NULL COMMENT '创建人名称',
    revised_time DATETIME    NOT NULL COMMENT '更新时间'
) comment 'Excel导入日志';

CREATE TABLE excel_row_error
(
    id           BIGINT        NOT NULL COMMENT '主键' PRIMARY KEY,
    log_id       BIGINT        NOT NULL COMMENT '导入日志ID',
    row_num      INT           NOT NULL COMMENT '所在行号',
    reason       VARCHAR(2048) NOT NULL COMMENT '错误原因',
    created_time DATETIME NOT NULL COMMENT '创建时间',
    CONSTRAINT uniq_row_num unique (log_id, row_num)
) comment 'Excel行错误';
ALTER TABLE excel_row_error ADD FOREIGN KEY (log_id) references excel_import_log (id);
