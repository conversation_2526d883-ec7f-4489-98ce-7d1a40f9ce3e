alter table workflow_instance
    add deleted tinyint not null comment '逻辑删除';


alter table workflow_definition
add variables text not null comment '变量定义' after category;

drop index idx_created_time on workflow_instance;

create index idx_created_time
    on workflow_instance (deleted, tenant_id, created_time);

alter table workflow_instance
    drop key workflow_instance_pk;

alter table workflow_instance
    add constraint workflow_instance_pk
        unique (deleted, tenant_id, serial_number);


update workflow_definition set variables = '[{"group":"INPUT","name":"素材库","property":"images","type":"GRAPHIC_STOREHOUSE","required":true,"description":"请选择图案素材"}]';
