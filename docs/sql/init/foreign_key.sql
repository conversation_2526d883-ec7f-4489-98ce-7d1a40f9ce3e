alter table edge add foreign key (workflow_definition_id) references workflow_definition (id);

alter table node_definition add foreign key (node_metadata_id) references node_metadata (id);
alter table node_definition add foreign key (workflow_definition_id) references workflow_definition (id);

alter table workflow_instance add foreign key (workflow_definition_id) references workflow_definition (id);

alter table node_instance add foreign key (node_definition_id) references node_definition (id);
alter table node_instance add foreign key (workflow_instance_id) references workflow_instance (id);

alter table node_instance_detail add foreign key (node_instance_id) references node_instance (id);
alter table node_instance_event add foreign key (node_instance_id) references node_instance (id);
alter table node_instance_history add foreign key (node_instance_id) references node_instance (id);

alter table task_instance add foreign key (node_instance_id) references node_instance (id);
alter table task_instance_detail add foreign key (task_id) references task_instance (id) on delete cascade;
alter table task_instance_result add foreign key (task_id) references task_instance (id) on delete cascade;

alter table workflow_instance_detail add foreign key (workflow_instance_id) references workflow_instance (id);
