-- 节点元数据表
create table node_metadata
(
    id           bigint       not null comment '主键ID' primary key,
    name         varchar(255) not null comment '节点名称',
    type         varchar(32)  not null comment '节点类型(START/LOGIC/NORMAL/TASK/END)',
    supplier     varchar(64)  not null comment '供应商(MIDJOURNEY/REALESRGAN/COMFY_UI等)',
    ability      varchar(64)  not null comment '能力(IMAGE_DERIVATION/UPSCALE/TEXTURE等)',
    input        text         not null comment '节点输入参数(JSON格式)',
    parameter    text         not null comment '节点参数(JSON格式)',
    output       text         not null comment '节点输出参数(JSON格式)',
    created_time datetime     not null comment '创建时间',
    revised_time datetime     not null comment '更新时间',
    constraint uniq_supplier_ability unique (supplier, ability)
) comment '节点元数据表';

-- 风格管理表
create table style_manage
(
    id                   bigint          not null comment '主键ID' primary key,
    style_code           varchar(64)     not null comment '风格编号',
    style_name           varchar(64)     not null comment '风格名称',
    application_business varchar(32)     not null comment '应用业务',
    image_material       text            not null comment '图片素材',
    created_time         datetime        not null comment '创建时间',
    revised_time         datetime        not null comment '更新时间',
    creator_id           bigint unsigned not null comment '创建人',
    creator_name         varchar(64)     not null comment '创建人名称',
    reviser_id           bigint unsigned not null comment '更新人',
    reviser_name         varchar(64)     not null comment '更新人名称',
    organization_id      bigint          not null comment '组织ID',
    tenant_id            bigint          not null comment '租户ID',
    constraint uniq_style_code unique (tenant_id, style_code)
) comment '风格管理表';

-- 工作流定义表
create table workflow_definition
(
    id              bigint       not null comment '主键ID' primary key,
    name            varchar(255) not null comment '工作流名称',
    category        varchar(32)  not null comment '工作流分类',
    created_time    datetime     not null comment '创建时间',
    revised_time    datetime     not null comment '更新时间',
    creator_id      varchar(64)  not null comment '创建人',
    creator_name    varchar(64)  not null comment '创建人名称',
    reviser_id      varchar(64)  not null comment '更新人',
    reviser_name    varchar(64)  not null comment '更新人名称',
    organization_id bigint       not null comment '组织ID',
    tenant_id       bigint       not null comment '租户ID',
    constraint uniq_name unique (tenant_id, name)
) comment '工作流定义表';

-- 工作流连接表
create table edge
(
    id                     bigint       not null comment '主键ID' primary key,
    workflow_definition_id bigint       null comment '工作流定义ID',
    source_node_key        varchar(128) not null comment '源节点key',
    target_node_key        varchar(128) not null comment '目标节点key',
    constraint uniq_key unique (workflow_definition_id, source_node_key, target_node_key)
) comment '工作流连接表';
create index idx_workflow_definition_id on edge (workflow_definition_id);

-- 节点定义表
create table node_definition
(
    id                     bigint       not null comment '主键ID' primary key,
    workflow_definition_id bigint       null comment '工作流定义ID',
    node_metadata_id       bigint       not null comment '节点元数据ID',
    node_key               varchar(128) not null comment '节点唯一标识',
    review                 tinyint(1)   not null comment '是否需要验收',
    skip                   tinyint(1)   not null comment '是否允许跳过',
    input                  text         not null comment '节点输入参数(JSON格式)',
    parameter              text         not null comment '节点参数(JSON格式)',
    constraint uniq_workflow_definition_key unique (workflow_definition_id, node_key)
) comment '节点定义表';
create index idx_node_metadata_id on node_definition (node_metadata_id);

-- 工作流实例表
create table workflow_instance
(
    id                     bigint       not null comment '主键ID' primary key,
    workflow_definition_id bigint       not null comment '工作流定义ID',
    serial_number          varchar(255) not null comment '工作流编码(流水号)',
    mode                   varchar(32)  not null comment '工作流模式(NODE/WORKFLOW)',
    status                 varchar(32)  not null comment '工作流运行状态(START/RUNNING/WAITING/COMPLETED/CANCELLED/FAILED)',
    created_time           datetime     not null comment '创建时间',
    revised_time           datetime     not null comment '更新时间',
    creator_id             varchar(64)  not null comment '创建人',
    creator_name           varchar(64)  not null comment '创建人名称',
    reviser_id             varchar(64)  not null comment '更新人',
    reviser_name           varchar(64)  not null comment '更新人名称',
    organization_id        bigint       not null comment '组织ID',
    tenant_id              bigint       not null comment '租户ID',
    constraint uniq_serial_number unique (tenant_id, serial_number)
) comment '工作流实例表';
create index idx_created_time on workflow_instance (tenant_id, created_time);

-- 节点实例表
create table node_instance
(
    id                   bigint      not null comment '主键ID' primary key,
    node_definition_id   bigint      not null comment '节点定义ID',
    workflow_instance_id bigint      not null comment '工作流实例ID',
    supplier             varchar(64) not null comment '供应商',
    ability              varchar(64) not null comment '能力',
    status               varchar(32) not null comment '节点状态(PREPARE/READY/RUNNING/REVIEWING/COMPLETED/FAILED)',
    created_time         datetime    not null comment '创建时间',
    revised_time         datetime    not null comment '更新时间',
    creator_id           varchar(64) not null comment '创建人',
    creator_name         varchar(64) not null comment '创建人名称',
    reviser_id           varchar(64) not null comment '更新人',
    reviser_name         varchar(64) not null comment '更新人名称'
) comment '节点实例表';
create index idx_node_definition_id on node_instance (node_definition_id);
create index idx_workflow_instance_id on node_instance (workflow_instance_id);

-- 节点实例详情表
create table node_instance_detail
(
    id                bigint not null comment '主键ID' primary key,
    node_instance_id  bigint not null comment '节点实例ID',
    storage           text   not null comment '工作流存储(JSON格式)',
    input             text   not null comment '节点输入参数(JSON格式)',
    parameter         text   not null comment '节点参数(JSON格式)',
    output            text   null comment '节点输出参数(JSON格式)',
    error             text   null comment '节点执行错误信息',
    error_stack_trace text   null comment '错误堆栈',
    constraint uniq_node_instance_id unique (node_instance_id)
) comment '节点实例详情表';

-- 节点实例事件
create table node_instance_event
(
    id               bigint      not null comment '主键ID' primary key,
    node_instance_id bigint      not null comment '节点实例ID',
    status           varchar(32) not null comment '节点状态(PREPARE/READY/RUNNING/REVIEWING/COMPLETED/FAILED)',
    created_time     datetime    not null comment '创建时间'
) comment '节点实例事件';
create index idx_node_instance_id on node_instance_event (node_instance_id);

-- 节点实例历史
create table node_instance_history
(
    id               bigint   not null comment '主键ID' primary key,
    node_instance_id bigint   not null comment '节点实例ID',
    created_time     datetime not null comment '创建时间',
    data             text     not null comment '历史数据'
) comment '节点实例历史';
create index idx_node_instance_id on node_instance_history (node_instance_id);

-- 任务实例表
create table task_instance
(
    id               bigint      not null comment '主键ID' primary key,
    node_instance_id bigint      null comment '节点实例ID',
    supplier         varchar(64) not null comment 'AI模型供应商',
    ability          varchar(64) not null comment 'AI能力',
    status           varchar(32) not null comment '任务状态(PREPARED/RUNNING/COMPLETED/FAILED/CANCELLED/TIMEOUT)',
    group_index      bigint      not null comment '任务分组',
    created_time     datetime    not null comment '创建时间',
    revised_time     datetime    not null comment '更新时间',
    creator_id       varchar(64) not null comment '创建人',
    creator_name     varchar(64) not null comment '创建人名称',
    reviser_id       varchar(64) not null comment '更新人',
    reviser_name     varchar(64) not null comment '更新人名称',
    biz_id           varchar(64) null comment '业务ID',
    biz_type         varchar(64) null comment '业务类型',
    trigger_source   varchar(10) not null comment '触发来源'
) comment '任务实例表';
create index idx_node_instance_id on task_instance (node_instance_id);
-- 查询场景1：根据 biz_id 精确查询（业务维度）
create index idx_biz_type_biz_id on task_instance (trigger_source, created_time, biz_type, biz_id);
-- 查询场景2：根据能力+供应商过滤（统计维度）
create index idx_ability_supplier on task_instance (trigger_source, created_time, ability, supplier);
-- 查询场景3：根据任务状态过滤（统计维度）
create index idx_status on task_instance (trigger_source, created_time, status);

-- 任务详情表
create table task_instance_detail
(
    id                bigint        not null comment '主键ID' primary key,
    task_id           bigint        not null comment '任务实例ID',
    request           text          not null comment '任务请求(JSON格式)',
    context           varchar(255)  null comment '任务创建后产生的上下文(JSON格式)',
    storage           varchar(1024) null comment '任务存储',
    error             text          null comment '任务执行错误信息',
    error_stack_trace text          null comment '错误堆栈',
    constraint uniq_task_id unique (task_id)
) comment '任务详情表';

-- 任务结果表
create table task_instance_result
(
    id      bigint     not null comment '主键ID' primary key,
    task_id bigint     null comment '任务实例ID',
    url     text       not null comment '结果数据',
    passed  tinyint(1) not null comment '是否验收通过',
    created_time     datetime    not null comment '创建时间'
) comment '任务结果表';
create index idx_task_id on task_instance_result (task_id);

-- 工作流实例详情表
create table workflow_instance_detail
(
    id                   bigint not null comment '主键ID' primary key,
    workflow_instance_id bigint not null comment '工作流实例ID',
    storage              text   not null comment '工作流存储(JSON格式)',
    input                text   not null comment '工作流输入(JSON格式)',
    parameter            text   not null comment '工作流参数(JSON格式)',
    node_args            text   not null comment '节点输入参数(JSON格式)',
    constraint uniq_workflow_instance_id unique (workflow_instance_id)
) comment '工作流实例详情表';
