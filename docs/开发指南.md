# Murmuration 开发指导文档

## 1. 代码规范与组织

### 1.1 模块结构规范

项目采用多模块架构，各模块职责明确：

```
murmuration/
├── murmuration-common/          # 共享工具和数据模型
│   └── src/main/kotlin/team/aikero/murmuration/common/
├── murmuration-sdk/             # 对外API和客户端库
│   └── src/main/kotlin/team/aikero/murmuration/sdk/
├── murmuration-runtime/         # 核心工作流引擎
│   └── src/main/kotlin/team/aikero/murmuration/core/
├── murmuration-service/         # 主应用和业务逻辑
│   └── src/main/kotlin/team/aikero/murmuration/service/
└── murmuration-ksp/            # 代码生成插件
    ├── murmuration-ksp-service/
    └── murmuration-ksp-sdk/
```

### 1.2 代码组织规则

#### 任务实现位置
所有 AI 服务的任务处理器必须放在以下目录结构：
```
murmuration-service/src/main/kotlin/team/aikero/murmuration/service/node/task/[provider]/
├── [Provider]Client.kt          # HTTP 客户端
├── [Provider]DTO.kt             # 数据传输对象
├── [Provider]HttpClient.kt      # HTTP 客户端接口
└── [ability]/
    ├── [Provider][Ability]TaskHandler.kt    # 任务处理器
    └── [Provider][Ability]TaskNode.kt       # 工作流节点
```

## 2. 演示：如何添加一个新的 AI 服务提供商

以添加一个名为 "SmartAI" 的图像增强服务为例，演示完整的集成流程。

### 第 1 步：创建目录结构

```bash
mkdir -p murmuration-service/src/main/kotlin/team/aikero/murmuration/service/node/task/smartai/enhance
```

### 第 2 步：定义供应商和能力枚举

在 `murmuration-runtime` 模块中注册新的供应商和能力：

```kotlin
// murmuration-runtime/src/main/kotlin/team/aikero/murmuration/core/Supplier.kt
enum class Supplier {
    // ... 现有供应商
    SMART_AI,  // 新增
}

// murmuration-runtime/src/main/kotlin/team/aikero/murmuration/core/Ability.kt  
enum class Ability {
    // ... 现有能力
    IMAGE_ENHANCE,  // 新增
}
```

### 第 3 步：创建 HTTP 客户端接口

```kotlin
// SmartAiHttpClient.kt
package team.aikero.murmuration.service.node.task.smartai

import team.aikero.murmuration.infra.client.RestClient

@RestClient(
    url = "\${smartai.base-url:https://api.smartai.com}"
)
interface SmartAiHttpClient {
    
    @PostMapping("/v1/enhance")
    fun enhanceImage(
        @RequestHeader headers: Map<String, String>,
        @RequestBody request: SmartAiEnhanceRequest
    ): SmartAiResponse
    
    @GetMapping("/v1/task/{taskId}")
    fun getTaskStatus(
        @RequestHeader headers: Map<String, String>,
        @PathVariable taskId: String
    ): SmartAiResponse
}
```

### 第 4 步：定义 DTO 数据结构

```kotlin
// SmartAiDTO.kt
package team.aikero.murmuration.service.node.task.smartai

import com.fasterxml.jackson.annotation.JsonProperty
import jakarta.validation.constraints.NotBlank

/**
 * SmartAI 认证信息
 */
data class SmartAiCredentials(
    @JsonProperty("api_key") 
    val apiKey: String = "\${smartai.api-key}",
    
    @JsonProperty("api_secret")
    val apiSecret: String = "\${smartai.api-secret}"
) {
    fun toHeaders(): Map<String, String> {
        return mapOf(
            "X-API-Key" to apiKey,
            "X-API-Secret" to apiSecret,
            "Content-Type" to "application/json"
        )
    }
}

/**
 * 图像增强请求
 */
data class SmartAiEnhanceRequest(
    @field:NotBlank(message = "图片URL不能为空")
    @JsonProperty("image_url")
    val imageUrl: String,
    
    @JsonProperty("enhance_type")
    val enhanceType: String = "auto", // auto, clarity, color, denoise
    
    @JsonProperty("scale_factor") 
    val scaleFactor: Double = 2.0,
    
    @JsonProperty("callback_url")
    val callbackUrl: String? = null
)

/**
 * SmartAI 响应
 */
data class SmartAiResponse(
    val code: Int,
    val message: String? = null,
    val data: SmartAiTaskData? = null
) {
    fun isSuccess(): Boolean = code == 0
    fun isFailed(): Boolean = code != 0
}

/**
 * 任务数据
 */
data class SmartAiTaskData(
    @JsonProperty("task_id")
    val taskId: String,
    
    val status: String, // pending, processing, completed, failed
    val progress: Int = 0,
    
    @JsonProperty("result_url")
    val resultUrl: String? = null,
    
    @JsonProperty("error_message")
    val errorMessage: String? = null
) {
    fun isCompleted(): Boolean = status == "completed"
    fun isFailed(): Boolean = status == "failed"
    fun isProcessing(): Boolean = status in listOf("pending", "processing")
}
```

### 第 5 步：实现客户端包装类

```kotlin
// SmartAiClient.kt
package team.aikero.murmuration.service.node.task.smartai

import org.springframework.stereotype.Component

@Component
@Suppress("SpringJavaInjectionPointsAutowiringInspection")
class SmartAiClient(
    private val httpClient: SmartAiHttpClient
) {
    private val credentials = SmartAiCredentials()
    
    /**
     * 创建图像增强任务
     */
    fun createEnhanceTask(request: SmartAiEnhanceRequest): SmartAiResponse {
        return httpClient.enhanceImage(credentials.toHeaders(), request)
    }
    
    /**
     * 查询任务状态
     */
    fun getTaskStatus(taskId: String): SmartAiResponse {
        return httpClient.getTaskStatus(credentials.toHeaders(), taskId)
    }
}
```

### 第 6 步：实现 TaskHandler

```kotlin
// SmartAiImageEnhanceTaskHandler.kt
package team.aikero.murmuration.service.node.task.smartai.enhance

import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.murmuration.core.Ability
import team.aikero.murmuration.core.Supplier
import team.aikero.murmuration.core.annotations.TaskIdentifier
import team.aikero.murmuration.core.workflow.task.TaskHandler
import team.aikero.murmuration.core.workflow.task.TaskHandlerResult
import team.aikero.murmuration.core.workflow.task.TaskResult
import team.aikero.murmuration.service.node.task.smartai.*

/**
 * SmartAI 图像增强任务处理器
 */
@TaskIdentifier(
    supplier = Supplier.SMART_AI,
    ability = Ability.IMAGE_ENHANCE
)
class SmartAiImageEnhanceTaskHandler(
    private val smartAiClient: SmartAiClient
) : TaskHandler<SmartAiImageEnhanceRequest, String>() {

    override fun create(request: SmartAiImageEnhanceRequest): String {
        log.info { "创建SmartAI图像增强任务: ${request.imageUrl}" }
        
        val enhanceRequest = SmartAiEnhanceRequest(
            imageUrl = request.imageUrl,
            enhanceType = request.enhanceType,
            scaleFactor = request.scaleFactor
        )
        
        val response = smartAiClient.createEnhanceTask(enhanceRequest)
        
        check(response.isSuccess()) { 
            "SmartAI任务创建失败: ${response.message}" 
        }
        
        check(response.data != null) { 
            "SmartAI响应数据为空" 
        }
        
        log.info { "SmartAI任务创建成功: ${response.data!!.taskId}" }
        return response.data!!.taskId
    }

    override fun query(
        request: SmartAiImageEnhanceRequest, 
        context: String
    ): TaskResult<List<TaskHandlerResult>> {
        
        val taskId = context
        val response = smartAiClient.getTaskStatus(taskId)
        
        if (response.isFailed()) {
            return TaskResult.Failed(
                RuntimeException("任务查询失败: ${response.message}")
            )
        }
        
        val taskData = response.data!!
        
        return when {
            taskData.isCompleted() -> {
                check(taskData.resultUrl != null) { "结果URL为空" }
                TaskResult.Completed(
                    listOf(TaskHandlerResult.image(taskData.resultUrl!!))
                )
            }
            
            taskData.isFailed() -> {
                TaskResult.Failed(
                    RuntimeException("任务执行失败: ${taskData.errorMessage}")
                )
            }
            
            taskData.isProcessing() -> {
                log.debug { "任务进行中: $taskId, 进度: ${taskData.progress}%" }
                TaskResult.Running
            }
            
            else -> {
                TaskResult.Failed(
                    RuntimeException("未知任务状态: ${taskData.status}")
                )
            }
        }
    }
}

/**
 * SmartAI 图像增强请求参数
 */
data class SmartAiImageEnhanceRequest(
    val imageUrl: String,
    val enhanceType: String = "auto",
    val scaleFactor: Double = 2.0
)
```

### 第 7 步：实现 TaskNode

```kotlin
// SmartAiImageEnhanceTaskNode.kt
package team.aikero.murmuration.service.node.task.smartai.enhance

import team.aikero.murmuration.core.Ability
import team.aikero.murmuration.core.Supplier
import team.aikero.murmuration.core.annotations.NodeIdentifier
import team.aikero.murmuration.core.workflow.context.WorkflowNodeContext
import team.aikero.murmuration.core.workflow.node.builtin.SimpleTaskResult
import team.aikero.murmuration.core.workflow.node.builtin.TaskNode
import team.aikero.murmuration.core.workflow.node.param.Parameter
import team.aikero.murmuration.service.node.shared.ImageListInput
import team.aikero.murmuration.service.node.shared.ImageListOutput
import team.aikero.murmuration.service.node.shared.SimpleImage

/**
 * SmartAI 图像增强任务节点
 */
@NodeIdentifier(
    name = "SmartAI图像增强", 
    supplier = Supplier.SMART_AI, 
    ability = Ability.IMAGE_ENHANCE
)
class SmartAiImageEnhanceTaskNode : TaskNode<
    ImageListInput, 
    SmartAiImageEnhanceParameter, 
    ImageListOutput
>() {

    override fun createTask(
        context: WorkflowNodeContext,
        input: ImageListInput,
        parameter: SmartAiImageEnhanceParameter
    ) {
        // 为每张输入图片创建增强任务
        input.images.forEach { image ->
            taskManager.createTask(
                nodeInstanceId = context.node.id,
                supplier = Supplier.SMART_AI,
                ability = Ability.IMAGE_ENHANCE,
                request = SmartAiImageEnhanceRequest(
                    imageUrl = image.getUrl(),
                    enhanceType = parameter.enhanceType,
                    scaleFactor = parameter.scaleFactor
                )
            )
        }
    }

    override fun assembleOutput(
        context: WorkflowNodeContext,
        taskResults: List<SimpleTaskResult>
    ): ImageListOutput {
        val enhancedImages = taskResults.map { result ->
            SimpleImage(result.url)
        }
        return ImageListOutput(enhancedImages)
    }
}

/**
 * SmartAI 图像增强节点参数
 */
data class SmartAiImageEnhanceParameter(
    val enhanceType: String = "auto",
    val scaleFactor: Double = 2.0
) : Parameter
```

### 第 8 步：注册组件到 Spring

通过注解自动注册，无需手动配置。Spring 启动会自动：

1. 扫描 `@TaskIdentifier` 注解，注册任务处理器
2. 扫描 `@NodeIdentifier` 注解，注册工作流节点
3. KSP 生成对应的 REST 控制器和客户端代码

### 第 9 步：配置文件

在 Nacos 或 `application.yml` 中添加配置：

```yaml
# SmartAI 配置
smartai:
  base-url: https://api.smartai.com
  api-key: your-api-key
  api-secret: your-api-secret
  timeout: 30000
```

### 第 10 步：验证集成

编写单元测试验证集成：

```kotlin
@SpringBootTest(classes = [MurmurationApplication::class])
class SmartAiClientTest {

    @Autowired
    lateinit var smartAiClient: SmartAiClient

    @Test
    @Disabled("集成测试")
    fun testImageEnhance() {
        val request = SmartAiEnhanceRequest(
            imageUrl = "https://example.com/test-image.jpg",
            enhanceType = "clarity",
            scaleFactor = 2.0
        )
        
        val response = smartAiClient.createEnhanceTask(request)
        
        assertTrue(response.isSuccess())
        assertNotNull(response.data)
        assertNotNull(response.data!!.taskId)
        
        println("任务ID: ${response.data!!.taskId}")
    }
}
```

## 3. 测试策略

### 3.1 测试层次

项目采用分层测试策略：

#### 单元测试
- **范围**：单个类或方法的逻辑测试
- **工具**：JUnit 5 + Mockito
- **位置**：`src/test/kotlin`
- **命名**：`*Test.kt`

#### 集成测试  
- **范围**：多个组件协作的功能测试
- **工具**：Spring Boot Test + TestContainers
- **位置**：`src/test/kotlin`
- **命名**：`*IntegrationTest.kt`

#### 端到端测试
- **范围**：完整工作流的业务场景测试
- **工具**：Spring Boot Test + 真实外部服务
- **位置**：`src/test/kotlin`
- **命名**：`*E2ETest.kt`

### 3.2 测试编写规范

#### 单元测试示例

```kotlin
@ExtendWith(MockitoExtension::class)
class TaskManagerTest {

    @Mock
    lateinit var sqlClient: KSqlClient
    
    @Mock
    lateinit var eventPublisher: EventPublisher
    
    @InjectMocks
    lateinit var taskManager: TaskManager

    @Test
    fun `should create task successfully`() {
        // Given
        val nodeInstanceId = 1L
        val supplier = Supplier.SMART_AI
        val ability = Ability.IMAGE_ENHANCE
        val request = SmartAiImageEnhanceRequest("test-url")
        
        whenever(sqlClient.insert(any<TaskInstance>()))
            .thenReturn(mockTaskInstance())
        
        // When
        val taskId = taskManager.createTask(
            nodeInstanceId = nodeInstanceId,
            supplier = supplier,
            ability = ability,  
            request = request
        )
        
        // Then
        assertTrue(taskId > 0)
        verify(eventPublisher).publish(any<TaskPrepareEvent>())
    }
}
```

#### 集成测试示例

```kotlin
@SpringBootTest(classes = [MurmurationApplication::class])
@DirtiesContext
class GraphEngineIntegrationTest {

    @Autowired
    lateinit var graphEngine: GraphEngine
    
    @Autowired
    lateinit var sqlClient: KSqlClient

    @Test
    fun `should execute workflow successfully`() = withUser(testUser()) {
        // Given
        val workflowDefinitionId = createTestWorkflowDefinition()
        val input = mapOf("images" to listOf("test-image-url"))
        
        // When
        val workflowInstanceId = graphEngine.startWorkflow(
            workflowDefinitionId = workflowDefinitionId,
            input = input,
            parameter = emptyMap(),
            nodeArgs = emptyList()
        )
        
        // Then
        assertTrue(workflowInstanceId > 0)
        
        // 验证工作流状态
        val status = graphEngine.getWorkflowStatus(workflowInstanceId)
        assertEquals(WorkflowStatus.RUNNING, status.status)
    }
    
    private fun testUser() = CurrentUser(1, "test", "test", 2, 1)
}
```

### 3.3 Mock 和测试数据

#### 外部服务 Mock

```kotlin
@TestConfiguration
class TestConfiguration {

    @Bean
    @Primary
    fun mockSmartAiHttpClient(): SmartAiHttpClient {
        return Mockito.mock(SmartAiHttpClient::class.java).apply {
            whenever(enhanceImage(any(), any()))
                .thenReturn(SmartAiResponse(
                    code = 0,
                    data = SmartAiTaskData(
                        taskId = "mock-task-id",
                        status = "completed",
                        resultUrl = "https://mock-result.jpg"
                    )
                ))
        }
    }
}
```

#### 测试数据构建

```kotlin
object TestDataBuilder {
    
    fun createWorkflowDefinition(id: Long): WorkflowDefinition {
        return WorkflowDefinition {
            this.id = id
            name = "测试工作流"
            category = "测试类别"
            // ... 其他属性
        }
    }
    
    fun createNodeInstance(workflowInstanceId: Long): NodeInstance {
        return NodeInstance {
            this.workflowInstance = WorkflowInstance { this.id = workflowInstanceId }
            supplier = Supplier.SMART_AI
            ability = Ability.IMAGE_ENHANCE
            status = NodeStatus.READY
        }
    }
}
```

### 3.4 测试运行

```bash
# 运行所有测试
./gradlew test

# 运行特定模块测试
./gradlew :murmuration-service:test

# 运行特定测试类
./gradlew test --tests "*SmartAiClientTest"

# 运行集成测试（需要外部服务）
./gradlew integrationTest
```

## 4. 重要概念速查

### 4.1 核心概念定义

| 概念 | 定义 | 作用 |
|------|------|------|
| **Supplier** | AI 服务提供商的枚举标识 | 区分不同的 AI 服务商（如美图、优川、ComfyUI） |
| **Ability** | AI 服务能力的枚举标识 | 标识具体的 AI 功能（如抠图、放大、风格转换） |
| **Workflow** | 工作流程的定义和实例 | 定义 AI 任务的执行顺序和依赖关系 |
| **Node** | 工作流中的处理节点 | 工作流中的一个执行单元，其中 `TaskNode` 可包含多个 Task |
| **Task** | 具体的 AI 处理任务 | 向外部 AI 服务发起的单次处理请求 |

### 4.2 关键注解

| 注解 | 作用 | 使用位置 |
|------|------|----------|
| `@TaskIdentifier` | 标识任务处理器 | TaskHandler 类上 |
| `@NodeIdentifier` | 标识工作流节点 | TaskNode 类上 |
| `@RestClient` | 定义 HTTP 客户端 | 接口上 |
| `@Entity` | 标识 Jimmer 实体 | 数据实体接口上 |

### 4.3 核心状态枚举

#### WorkflowStatus（工作流状态）
- `START`: 启动
- `RUNNING`: 运行中  
- `PAUSED`: 已暂停
- `COMPLETED`: 完成
- `CANCELLED`: 取消
- `FAILED`: 失败

#### NodeStatus（节点状态）
- `PREPARE`: 准备中
- `READY`: 就绪
- `RUNNING`: 运行中
- `REVIEWING`: 复核中
- `REVIEWED`: 已复核
- `COMPLETED`: 完成
- `FAILED`: 失败

#### TaskStatus（任务状态）
- `PREPARED`: 已准备
- `SUBMITTED`: 已提交
- `RUNNING`: 运行中
- `COMPLETED`: 完成
- `FAILED`: 失败
- `CANCELLED`: 取消

### 4.4 常用工具类

| 类名 | 功能 | 示例用法 |
|------|------|----------|
| `WorkflowContext` | 工作流上下文 | `context.setStorage("key", value)` |
| `GenericData` | 通用数据容器 | `GenericData.from(object).to(TargetClass::class)` |
| `TaskManager` | 任务管理器 | `taskManager.createTask(nodeId, supplier, ability, request)` |

### 4.5 表达式语法

工作流节点支持表达式语法进行动态数据绑定：

| 表达式 | 含义 | 示例 |
|--------|------|------|
| `#{node.output.field}` | 上游节点输出 | `#{cutout.images}` |
| `#{start.field}` | 工作流输入参数 | `#{start.images}` |

### 4.6 快速参考

#### 创建新 AI 服务集成的必要步骤
1. 定义 Supplier 和 Ability 枚举
2. 创建 HTTP 客户端接口（`@RestClient`）
3. 定义 DTO 数据结构
4. 实现客户端包装类
5. 实现 TaskHandler（`@TaskIdentifier`）
6. 实现 TaskNode（`@NodeIdentifier`）
7. 编写测试用例
8. 添加配置参数

#### 常用目录结构模板
```
service/node/task/[provider]/
├── [Provider]Client.kt
├── [Provider]DTO.kt  
├── [Provider]HttpClient.kt
└── [ability]/
    ├── [Provider][Ability]TaskHandler.kt
    └── [Provider][Ability]TaskNode.kt
```