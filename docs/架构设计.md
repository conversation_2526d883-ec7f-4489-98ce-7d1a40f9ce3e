# Murmuration 架构设计文档

## 1. 概述

Murmuration 是一个分布式 AI 任务工作流系统，灵感来源于椋鸟群的同步飞行模式。系统致力于构建一个高度可扩展、松耦合的架构，以支持复杂的 AI 工作流编排和执行。

### 项目愿景
通过模拟椋鸟群的自组织行为，系统中的每个组件都能基于简单的局部交互规则，共同创造出复杂而优雅的分布式工作流执行模式。系统旨在为企业级 AI 应用提供统一的工作流编排平台，支持多种 AI 服务提供商的无缝集成。

## 2. 设计原则

基于对系统架构的深入分析，Murmuration 遵循以下核心设计原则：

### 2.1 异步化优先 (Asynchronous First)
- 所有任务执行采用协程（Coroutines）进行异步处理
- 基于事件驱动架构，通过 Spring Event 实现组件间的通信
- 工作流节点间采用非阻塞式依赖管理

### 2.2 高可扩展性 (High Scalability)
- 多模块架构设计，支持水平扩展
- 基于 Redis 的分布式调度系统，支持集群部署
- 插件化的节点系统，支持便捷地添加新的 AI 能力

### 2.3 松耦合架构 (Loose Coupling)
- 通过 `Supplier` 和 `Ability` 抽象层解耦 AI 服务集成
- 基于注解的组件注册机制（`@TaskIdentifier`, `@NodeIdentifier`）
- 模块间通过明确定义的接口和事件进行通信

### 2.4 多租户支持 (Multi-tenancy)
- 核心实体实现 `TenantId` 接口，支持租户隔离
- 工作流和任务执行支持租户级别的资源管理
- 配置和状态管理按租户进行隔离

### 2.5 容错与可靠性 (Fault Tolerance & Reliability)
- 指数退避策略结合抖动算法，提高任务重试的可靠性
- 基于 Redis Delayed Queue 的持久化任务调度
- 完整的事件溯源和状态恢复机制


## 3. 模块职责详述

### 3.1 murmuration-common
**核心职责**：提供系统范围内的共享基础设施和通用抽象

**主要组件**：
- **共享 DTO**：统一的数据传输对象，确保模块间数据交换的一致性
- **枚举定义**：任务类型、分辨率、颜色等业务枚举，支持注释生成
- **请求/响应模型**：标准化的 API 交互模型
- **核心注解**：`@EnumComment` 等元数据注解

**边界定义**：不包含任何业务逻辑，仅提供数据结构和工具类

### 3.2 murmuration-sdk
**核心职责**：为外部系统提供标准化的集成接口

**主要组件**：
- **公共 API 接口**：定义系统对外暴露的标准接口
- **客户端库**：封装复杂的系统交互逻辑
- **常量定义**：系统级别的常量和配置参数

**边界定义**：面向外部客户端，隐藏内部实现细节，提供简洁的编程接口

### 3.3 murmuration-runtime
**核心职责**：系统的核心执行引擎，负责工作流编排和任务管理

**主要组件**：
- **工作流引擎**：图执行引擎、状态管理、表达式评估
- **任务管理系统**：任务生命周期管理、执行器、处理器注册
- **调度系统**：基于 Redis 的延迟队列、指数退避策略
- **AI 服务抽象**：`Supplier`/`Ability` 抽象层，REST 客户端框架
- **事件系统**：工作流和任务事件的发布与监听

**边界定义**：不直接处理具体的 AI 服务调用，通过抽象层与具体实现解耦

### 3.4 murmuration-service
**核心职责**：主应用程序，承载业务逻辑和外部系统集成

**主要组件**：
- **REST 控制器**：Web API 端点实现
- **任务处理器**：具体 AI 服务的任务处理逻辑
- **节点实现**：各种工作流节点的具体实现
- **AI 服务集成**：与各种 AI 服务提供商的 HTTP 客户端集成

**边界定义**：专注于业务逻辑实现，依赖 runtime 模块提供的核心框架

### 3.5 murmuration-ksp
**核心职责**：基于 Kotlin Symbol Processing 的代码生成

**主要组件**：
- **枚举处理器**：自动生成枚举注释和元数据
- **REST 控制器生成器**：自动生成任务处理器的 REST 端点
- **SDK 客户端生成器**：自动生成 Feign 客户端代码

**边界定义**：编译时代码生成，不参与运行时逻辑

## 4. 核心系统剖析

### 4.1 工作流引擎 (Workflow Engine)

工作流引擎是系统的核心，采用有向无环图（DAG）模型来表示和执行复杂的 AI 工作流。

#### 核心设计概念

**WorkflowDefinition vs WorkflowInstance**：
- `WorkflowDefinition`：工作流的静态定义，描述节点间的依赖关系和执行逻辑
- `WorkflowInstance`：工作流的运行时实例，维护执行状态和上下文数据

#### 图执行引擎架构

```mermaid
graph TB
    subgraph "工作流执行模型"
        WD[WorkflowDefinition<br/>工作流定义]
        WI[WorkflowInstance<br/>工作流实例]
        
        subgraph "节点层次"
            ND[NodeDefinition<br/>节点定义]
            NI[NodeInstance<br/>节点实例]
        end
        
        subgraph "执行引擎"
            GE[GraphEngine<br/>图执行引擎]
            NE[NodeExecutor<br/>节点执行器]
        end
        
        subgraph "上下文管理"
            WC[WorkflowContext<br/>工作流上下文]
            WNC[WorkflowNodeContext<br/>节点上下文]
        end
    end
    
    WD --> WI
    ND --> NI
    WI --> GE
    GE --> NE
    NE --> WC
    WC --> WNC
    
    WI -.-> NI
    ND -.-> WD
```

**执行策略**：
- 基于依赖关系的并行执行
- 节点状态自动管理和恢复
- 表达式驱动的节点执行

### 4.2 任务调度系统 (Scheduling System)

调度系统采用 Redis Delayed Queue 架构，结合指数退避策略确保任务的可靠执行。

#### 为什么选择 Redis Delayed Queue？

1. **持久化保证**：任务状态持久化存储，系统重启不丢失
2. **分布式支持**：天然支持多实例部署和负载均衡
3. **高性能**：基于 Redis 的高性能内存操作
4. **原子性操作**：利用 Redis 事务保证调度操作的原子性

#### 指数退避与容错机制

```mermaid
graph LR
    subgraph "任务调度流程"
        TS[任务提交] --> DQ[延迟队列]
        DQ --> TE[任务执行]
        TE --> Success{执行成功?}
        Success -->|是| Complete[完成]
        Success -->|否| BackOff[指数退避计算]
        BackOff --> Jitter[添加抖动]
        Jitter --> Retry[重新调度]
        Retry --> DQ
        
        Retry -.-> MaxRetry{达到最大重试?}
        MaxRetry -->|是| Failed[标记失败]
        MaxRetry -->|否| DQ
    end
```

**退避策略配置**：
- 基础延迟时间：可配置的初始等待时间  
- 指数因子：控制退避时间的增长速度
- 最大延迟时间：防止无限增长的上限
- 抖动策略：避免"雷鸣群"效应的随机化延迟

### 5.3 AI 服务抽象 (AI Service Abstraction)

AI 服务抽象层通过 `Supplier` 和 `Ability` 两个核心概念实现了对不同 AI 服务的统一管理。

#### 抽象层设计理念

**Supplier（供应商）**：
- 代表一个 AI 服务提供商（如美图、悠船、ComfyUI）
- 封装服务商的认证、连接和通用配置

**Ability（能力）**：

- 代表供应商提供的具体 AI 能力（如图像处理、文本生成）
- 定义标准化的输入输出接口

#### 插件化支持架构

```mermaid
graph TB
    subgraph "AI 服务抽象层"
        SAL[服务抽象层]
        
        subgraph "供应商层"
            S1[美图 Supplier]
            S2[悠船 Supplier] 
            S3[ComfyUI Supplier]
            SN[... 其他供应商]
        end
        
        subgraph "能力层"
            A1[图像切割 Ability]
            A2[背景替换 Ability]
            A3[风格转换 Ability]
            AN[... 其他能力]
        end
        
        subgraph "实现层"
            TH1[MeiTuCutoutsTaskHandler]
            TH2[LazadaChangeBackgroundTaskHandler]
            TH3[YouChuanDiffusionTaskHandler]
            THN[... 其他处理器]
        end
    end
    
    SAL --> S1
    SAL --> S2
    SAL --> S3
    SAL --> SN
    
    S1 --> A1
    S2 --> A2
    S3 --> A3
    
    A1 --> TH1
    A2 --> TH2
    A3 --> TH3
    
    TH1 -.-> |TaskIdentifier| Registry[任务处理器注册表]
    TH2 -.-> |TaskIdentifier| Registry
    TH3 -.-> |TaskIdentifier| Registry
```

**插件化优势**：
1. **动态扩展**：新的 AI 服务可以通过实现标准接口快速集成
2. **版本兼容**：支持同一能力的多个版本并存
3. **配置灵活**：基于注解的自动注册和配置
4. **测试友好**：标准接口便于模拟和单元测试

通过这种设计，系统能够轻松支持新的 AI 服务提供商，同时保持现有功能的稳定性和向后兼容性。
