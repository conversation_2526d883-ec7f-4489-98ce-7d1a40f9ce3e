Murmuration 是一个分布式 AI 任务工作流系统，灵感来源于椋鸟群的同步飞行模式。系统致力于构建一个高度可扩展、松耦合的架构，以支持复杂的 AI 工作流编排和执行。

### **前序**

#### **基本定义**

- model 模型：AI 模型名称
- ability 类型：[文本模型、图像识别]、{文生图、转绘（图文生图）、抠图、区域重绘、衍生、超分}
- input 输入：prompt、image
- parameters 控制参数：seed、num、width、height
- output 输出: text、json、image

```
[文本模型、图像识别]
这里是文本模型，参数是 提示词、图像；返回文本

{文生图、转绘（图文生图）、抠图、区域重绘、衍生、超分}

这里是图像模型，参数是 多个提示词、多个图像；返回多个图像。

稍微分分就是，几张图、几个提示词，返回若干个图像

文生图： 两个提示词（正面、负面），返回若干图像
转绘（图文生图）： 一张图，两个提示词（正面、负面），返回若干图像
抠图（去除背景或图案提取）： 一张图，返回若干图像
区域重绘： 两张图，一个提示词，返回若干图像
衍生（姿势裂变、图案衍生）：一张图，一段提示词，返回若干图像
超分： 一张图，返回一张图像
```



#### **实体关系**

1. Workflow  工作流
2. Node 节点
3. Task 任务

![img](data:image/svg+xml;base64,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)



![img](data:image/svg+xml;base64,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)



### **1. 系统概述**

1. 可扩展、事件驱动的通用工作流系统。
2. 支持通过节点编排（DAG，有向无环图）来定义复杂的业务流程
3. 支持动态参数绑定、节点间参数转换、工作流与节点的生命周期管理、以及基于任务调度器的异步任务执行。

设计目标：

- **灵活性与扩展性**：便于添加新的业务能力节点（`Node`）和任务处理器（`TaskHandler`）。
- **可靠性**：通过事件驱动和持久化，确保工作流在节点执行失败或系统重启后可以恢复。支持重试和指数退避策略。
- **解耦**：将工作流的“编排”与具体任务的“执行”完全分离，`GraphEngine` 专注于流程流转，`TaskHandler` 专注于业务逻辑。
- **可观测性**：所有实例的执行状态、输入输出都将被记录，便于追踪和调试。

### **2. 核心概念与领域模型**

#### **2.1 Workflow 域**

- `WorkflowDefinition` **(工作流定义)**
  - **描述**: 工作流的静态模板，定义了工作流的结构。它是一个蓝图。
  - **属性**: `id`, `name`, `version`, `description`, 一个由 `NodeDefinition` `Edges`组成的图（通常用邻接表表示节点及其依赖关系），全局输入参数定义。
  - **存储**: 持久化存储在数据库中，以关联表定义图结构。
- `WorkflowInstance` **(工作流实例)**
  - **描述**: `WorkflowDefinition` 的一个具体运行实例。
  - **属性:** `id`, `workflowDefinitionId`, `status` (START，RUNNING, WAITING，COMPLETED, FAILED, CANCELLED), `storage`。
  - **存储**: 持久化存储，记录了工作流的实时状态。
- `WorkflowContext` **(工作流上下文)**
  - **描述**: `WorkflowInstance` 的运行时数据容器。它包含了工作流的初始输入参数以及所有已执行节点的输出。
  - **作用**: 为参数绑定和转换提供数据源。生命周期与 `WorkflowInstance` 绑定。
- `GraphEngine` **(图执行引擎)**
  - **描述**: 工作流的核心驱动器。负责解析 `WorkflowDefinition` 的图结构，并根据节点依赖关系和执行结果，决定下一步要执行哪些节点。
  - **职责**:
    1. 接收工作流启动指令，创建 `WorkflowInstance`。
    2. 在工作流启动或任一节点完成后，计算出所有入度为 0 的“就绪”节点。
    3. 发出 `NodePrepareEvent` 事件。
    4. 监听 `Node[*]Event` 事件，更新图的状态。
    5. 判断工作流是否已全部完成或失败。
- `ExpressionEvaluator` **(表达式计算器)**
  - **描述**: 一个工具类或服务，负责在节点执行前，解析其输入参数定义，并从 `WorkflowContext` 或 `NodeInstance` 中提取、转换数据。
  - **语法示例**: 支持类似 `#{workflow.input.userId}` 或 `#{nodeA.output.orderId}` 的表达式。

#### **2.2 Node 域**

- `NodeDefinition` **(节点定义)**
  - **描述**: 工作流定义中的一个节点单元。
  - **属性**: `id` (在工作流定义内唯一), `name`, `type` (例如, `Task`, `Start`, `End`),`Variables` (参数定义及规则), `node` (节点实现信息)。
- `NodeInstance` **(节点实例)**
  - **描述**: `NodeDefinition` 在 `WorkflowInstance` 中的一个具体运行实例。
  - **属性**: `id`, `nodeDefinitionId`, `workflowInstanceId`, `status` (PENDING, RUNNING, COMPLETED, FAILED),  `input,Data`, `outputData`, `retryCount`。
  - **存储**: 持久化存储，记录每个节点的执行细节。
- `WorkflowNode` **(工作流节点)**
  - **描述**: 工作流节点接口，其定义一个执行方法，关注节点输入输出参数。
- `TaskNode` **(任务节点)**
  - **描述**: 一个特殊的节点类型，它关联一个或多个具体的 `Task`。当此节点执行时，会创建 `Task` 并交由 `TaskScheduler` 处理。

#### **2.3 Task 域**

- `Task` **(任务)**
  - **描述**: `TaskNode` 中的一个任务实例。
  - **属性**: `id`, `bizId`, `model`,`ability`, `input`, `parameter`, `status` (QUEUEING, RUNNING, COMPLETED, FAILED，CANCELLED)。
- `TaskScheduler` **(任务调度器)**
  - **描述**: 负责接收 `Task`，并将其分发给合适的 `TaskHandler` 执行。
  - **职责**:
    1. 提供提交任务的接口。
    2. 维护一个或多个任务队列（例如，按优先级）。
    3. 当任务执行失败时，根据节点的重试策略（如指数退避）决定是否以及何时重新调度。
    4. 轮询检查长时间未完成的任务，处理超时。
- `TaskHandler` **(任务处理器)**
  - **描述**: 具体的业务逻辑执行者。每个 `TaskHandler` 实现一种特定类型的任务（如 `Midjourney 文生图`,  `抠图`）。
  - **特点**: `TaskHandler` 只关心单次任务的输入和输出，存在两个抽象接口方法，`create`,`update`。
- `TaskRegisterManager` **(任务注册管理器)**
  - **描述**: 负责在系统启动时，发现并注册所有可用的 `TaskHandler` 实现。
  - **实现**: 使用依赖注入框架（如 Spring）的扫描功能。它维护一个 `Map<String, TaskHandler>`，其中 key 是 `model`+`ability`。
- `TaskRunnable` **(任务执行器)**
  - **描述**: 一个原子性的、可被独立调度的执行单元。执行提交的任务，统一持久化。

### **3. 架构设计**

系统采用分层和事件驱动的架构。

```
 +-------------------------------------------------------------------+
 |                       Workflow API / 触发器                        |
 |                           (REST API)                              |
 +-------------------------------------------------------------------+
                                 | (Start)
                                 v
 +-------------------------------------------------------------------+
 |                        Workflow Core Engine                       |
 |                                                                   |
 |  +-----------------+      (Event)      +------------------------+ |
 |  |  GraphEngine    |<------------------|      Node Executor     | |
 |  | (Manages Graph) |------------------>| (Executes single node) | |
 |  +-----------------+      (Event)      +------------------------+ |
 |         |                                         |               |
 |         | (查找可执行节点）                          | (提交任务)     |
 |         v                                         v               |
 |  +---------------------+                 +----------------------+ |
 |  | ExpressionEvaluator |                 |    TaskScheduler     | |
 |  +---------------------+                 +----------------------+ |
 +-------------------------------------------------------------------+
                                                       | (Dispatches Task)
                                                       v
 +-------------------------------------------------------------------+
 |                         Task Execution Layer                      |
 |                                                                   |
 |  +----------------------+      +--------------------------------+ |
 |  | TaskRegisterManager  |----->| TaskHandler                    | |
 |  +----------------------+      +--------------------------------+ |
 +-------------------------------------------------------------------+
                                 | (DB Operations)
                                 v
 +-------------------------------------------------------------------+
 |                        Persistence Layer                          |
 |                (Workflow Definitions, Instances, etc.)            |
 +-------------------------------------------------------------------+
```

**事件流**:

1. `WoorkflowStartEvent`: API层启动工作流时触发，工作流实例化后发出，`GraphEngine` 监听。
2. `NodePrepareEvent`: `GraphEngine` 发出，`Node Executor` 监听。
3. `NodeFinishedEvent`: `Node Executor` 发出，`GraphEngine` 监听。
4. `WoorkflowFinishedEvent`: `GraphEngine` 发出，可用于通知外部系统。

Completed、Cancelled、Failed 均视为 Finished

### **4. 核心流程设计**

#### **4.1 工作流定义**

用户通过 UI 或直接编写 JSON/YAML 来定义工作流。

**示例** `workflow_definition.json`:

```
 {
   "id": 0,
   "nodeDefinitions": [
     {
       "id": 0,
       "name": "Midjourney 文生图",
       "type": "Task",
       "component": "Midjourney",
       "input": [
         {
           "name": "prompt",
           "type": "String",
           "elementType": "",
           "component":"?没想好，要不要记录前端组件",
           "required": true,
           "description": "提示词",
           "defaultValue": "",
           "min": 1,
           "max": 8192
         }
       ],
       "parameter": [
         {
           "name": "num",
           "type": "Number",
           "elementType": "",
           "required": false,
           "description": "生图数量",
           "defaultValue": "4",
           "min": 1,
           "max": 4
         }
       ],
       "version": "1.0.0"
     }
   ],
   "edges": [
     {
       "source": 0,
       "target": 0
     }
   ],
   "version": "1.0.0"
 }
```

#### **4.2 工作流实例化与启动**

1. 外部触发器（如 REST API 调用）请求启动一个工作流，并提供 `workflowDefinitionId` 和初始输入参数。
2. `GraphEngine` 监听到启动请求。
3. 创建一个 `WorkflowInstance` 记录，状态为 `Start`。
4. 创建 `WorkflowContext` 并存入初始参数。
5. 为定义中的每一个 `NodeDefinition` 创建一个对应的 `NodeInstance`，状态为 `PREPARE`。
6. `GraphEngine` 分析图，找到所有没有入边的节点（入度为0）。
7. 对于每个找到的节点，发布一个 `NodePrepareEvent` 事件，事件内容包含 `NodeInstance`。

#### **4.3 节点执行**

1. `Node Executor` 监听到 `NodePrepareEvent` 事件。
2. 获取 `NodeInstance` 和其 `NodeDefinition`。
3. **参数处理**:
   - 调用 `ParameterParser`。
   - `ParameterParser` 读取节点的 `input`,`parameters` 映射规则。
   - 从 `WorkflowContext` 中解析 `#{...}` 表达式，获取真实数据。
   - 生成该节点本次执行的实际输入 `input`,`parameters`，并存入 `NodeInstance`。
4. **执行分派**:
   - **如果是** `TaskNode`:
     - 创建 N 个 `TaskRunnable` 对象，包含 `model`,`ability` 和 `input`, `parameters`。
     - 将 `TaskRunnable` 包装提交给 `TaskScheduler`。
     - 发送 `NodeReadyEvent`, 等待再次调度。（循环，直至状态更新到任一 Finished）
   - **如果是** `NormalNode`:
     - 直接执行其逻辑，由节点内部控制何时发布何种事件。

#### **4.4 任务执行与调度**

1. `TaskScheduler` 接收到 `TaskRunnable` 后，将其放入持久化任务队列（如 Redis List 或 RabbitMQ），`Task` 状态变为 `QUEUEING`。
2. 协程+虚拟线程或线程池，实现独立的 `Task Worker` 协程/线程从队列中获取任务。
3. `Worker` 调用 `TaskRegisterManager`，根据 `model+ability` 找到对应的 `TaskHandler`。
4. `Worker` 调用 `TaskRunnable.run()`。
5. `TaskRunnable` 根据任务状态调用 `handler.create(task.input)` 或`handler.update(task)`
6. **执行结果处理**:
   - **执行中**: `TaskRunnable` 持久化 `Task` 对象，并重新入队。
   - **成功**: `TaskRunnable` 持久化 `output`，发布 `TaskCompletedEvent` 事件，包含 `Task` 。
   - **失败**: `TaskRunnable` 持久化 `TaskFailedEvent` 事件，包含 `taskId` 和错误信息, 检查重试次数，指数退避后重新入队。



### **6. 扩展性设计**

系统的扩展性主要体现在 `TaskHandler` 和`TaskNode`的添加上。

1. **TaskHandler 接口**:

```
 interface TaskHandler<I, P> {
     /**
      * 创建任务
      *
      * @param bizId 业务id
      * @param source 来源
      * @param input 任务输入
      * @param parameter 任务参数
      */
     suspend fun create(bizId: Long, source: String, input: I, parameter: P): String//outerJobId
 
     /**
      * 任务更新
      *
      * @param task
      */
     suspend fun update(task: Task): TaskUpdateEvent
 }
```

1. **TaskNode 接口**:

```
 interface TaskNode<I : Input, P : Parameter, O : Output> :
     WorkflowNode<I, P, O> {
 
     override val type: NodeType
         get() = NodeType.TASK
 
     /**
      * 节点执行
      *
      * @param context
      * @param input
      * @param parameter
      * @return
      */
     override fun execute(context: WorkflowNodeContext, input: I, parameter: P): O {
 
         return when (context.node.status) {
             PREPARE -> {
                 // 初始化
                 init(context, input, parameter)
                 context.publisher.publish(NodeReadyEvent(context.node))
                 empty()
             }
 
             READY -> {
                 // 运行
                 run(context, input, parameter)
                 context.publisher.publish(NodeRunningEvent(context.node))
                 empty()
             }
 
             RUNNING -> {
                 // 状态更新
                 val output = status(context, input, parameter)
                 if (output !is Output.Empty) {
                     context.publisher.publish(NodeReviewEvent(context.node))
                 }else {
                     context.publisher.publish(NodeCompletedEvent(context.node))
                 }
                 output
             }
 
             CANCELLED -> {
                 // 取消
                 cancel(context, input, parameter)
                 empty()
             }
             // 不会到这里
             REVIEW, COMPLETED, FAILED -> throw NodeExecutionException(context.node)
         }
     }
 
     fun empty() = Output.Empty as O
 
     /**
      * 节点初始化
      *
      * @param context
      * @param input
      * @param parameter
      * @return
      */
     fun init(context: WorkflowNodeContext, input: I, parameter: P) {
 
     }
 
     /**
      * 节点执行
      *
      * @param context
      * @param input
      * @param parameter
      * @return
      */
     fun run(context: WorkflowNodeContext, input: I, parameter: P)
 
     /**
      * 节点状态更新
      *
      * @param context
      * @param input
      * @param parameter
      * @return
      */
     fun status(context: WorkflowNodeContext, input: I, parameter: P): O
 
     /**
      * 节点取消
      *
      * @param context
      * @param input
      * @param parameter
      * @return
      */
     fun cancel(context: WorkflowNodeContext, input: I, parameter: P) {
 
     }
 
 }
 
```

1. **自动注册**:`TaskRegisterManager` 在启动时，会扫描所有实现了 `TaskHandler` 接口并带有 `@Component` 注解的类。它会创建一个 `Map<String, TaskHandler>`，循环调用每个 handler 的 `getTaskType()` 作为 key，handler 实例作为 value，完成注册。

只需编写新的 `TaskHandler`、	`TaskNode` 实现类，系统就能自动集成新的业务能力，无需改动和关注核心代码。