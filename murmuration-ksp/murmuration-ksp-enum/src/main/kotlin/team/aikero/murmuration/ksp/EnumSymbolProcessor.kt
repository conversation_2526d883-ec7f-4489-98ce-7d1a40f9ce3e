package team.aikero.murmuration.ksp

import com.google.devtools.ksp.KspExperimental
import com.google.devtools.ksp.processing.CodeGenerator
import com.google.devtools.ksp.processing.Dependencies
import com.google.devtools.ksp.processing.Resolver
import com.google.devtools.ksp.processing.SymbolProcessor
import com.google.devtools.ksp.processing.SymbolProcessorEnvironment
import com.google.devtools.ksp.processing.SymbolProcessorProvider
import com.google.devtools.ksp.symbol.KSAnnotated
import com.google.devtools.ksp.symbol.KSClassDeclaration
import java.io.OutputStream

fun OutputStream.appendText(str: String) {
    this.write(str.toByteArray())
}

class EnumSymbolProcessor(
    val codeGenerator: CodeGenerator,
) : SymbolProcessor {
    lateinit var file: OutputStream
    var invoked = false

    fun log(content: String) {
        file.appendText("$content\n")
    }

    @OptIn(KspExperimental::class)
    override fun process(resolver: Resolver): List<KSAnnotated> {
        if (invoked) {
            return emptyList()
        }

        file = codeGenerator.createNewFile(Dependencies(false), "", "ksp-enum", "log")
        log("EnumSymbolProcessor invoked")

        resolver
            .getSymbolsWithAnnotation("team.aikero.murmuration.common.annotation.EnumComment")
            .filterIsInstance<KSClassDeclaration>()
            .forEach(this::generateExtensionFunction)

        invoked = true
        return emptyList()
    }

    /**
     * 生成扩展方法
     */
    private fun generateExtensionFunction(classDecl: KSClassDeclaration) {
        val enumName = classDecl.simpleName.asString()
        log("Generating extension function for $enumName")
        val commentFile = codeGenerator.createNewFile(Dependencies(false), "team.aikero.murmuration.metadata", "${enumName}Comments")
        commentFile.appendText("package team.aikero.murmuration.metadata\n\n")

        commentFile.appendText("""
            import ${classDecl.qualifiedName!!.asString()}
            import ${classDecl.qualifiedName!!.asString()}.*
            
        """.trimIndent())

        val enums = classDecl.declarations.filterIsInstance<KSClassDeclaration>()
        val mapName = "${enumName}Comments"

        val pairs = enums
            .map {
                """    $it to "${it.docString!!.trim()}","""
            }
            .joinToString("\n")
        commentFile.appendText("""
val $mapName = mapOf(
$pairs
)""")
        commentFile.appendText("""
            
            
            val ${enumName}.comment: String
                get() = ${mapName}[this]!!
        """.trimIndent())

        commentFile.appendText("""
            
            
            fun to${enumName}(comment: String): ${enumName}? {
                return ${mapName}.entries.find { it.value == comment }?.key
            }
        """.trimIndent())
    }

    internal fun String.toKebabCase(): String {
        // 驼峰转为 kebab-case
        return this
            .replace(Regex("([a-z])([A-Z])"), "$1-$2")
            .replace(Regex("([A-Z])([A-Z][a-z])"), "$1-$2")
            .lowercase()
    }
}

class EnumSymbolProcessorProvider : SymbolProcessorProvider {
    override fun create(environment: SymbolProcessorEnvironment): SymbolProcessor {
        return EnumSymbolProcessor(environment.codeGenerator)
    }
}
