# Murmuration

> *灵感来源于椋鸟群的飞行模式，成千上万只鸟儿通过简单的局部交互创造出复杂而美丽的队形，本系统以协调的分布式工作流编排AI任务。*

## 关于项目

Murmuration 是一个强大的 AI 任务工作流系统，通过分布式工作流实现复杂 AI 工作负载的无缝执行。系统设计灵感来源于椋鸟群的同步运动，为协调多个 AI 服务提供商和能力提供了统一、可扩展的架构框架。

无论是构建复杂的 AI 管道、管理多步骤图像处理工作流，还是编排复杂的 AI 驱动业务流程，Murmuration 都能提供所需的灵活性和可靠性。

## 🚀 核心特性

- **🌐 分布式工作流引擎**: 跨分布式系统执行复杂AI工作流，支持自动依赖管理和并行处理
- **🔌 多提供商AI集成**: 通过统一接口无缝集成多个AI服务提供商（ComfyUI、Flux Kontext、美图、优川等）
- **⚡ 基于Redis的健壮调度**: 高级任务调度系统，支持指数退避、抖动和容错机制，确保可靠执行
- **📊 事件驱动架构**: 基于RocketMQ集成的实时工作流监控和事件处理
- **🔧 动态节点系统**: 可扩展的基于节点的架构，支持轻松添加新的AI能力和自定义处理逻辑

## 🛠 技术栈

- **后端框架**: Spring Boot 3.x
- **开发语言**: Kotlin
- **配置管理**: Nacos
- **缓存与调度**: Redis with Redisson
- **消息队列**: RocketMQ
- **代码生成**: Kotlin Symbol Processing (KSP)
- **构建工具**: Gradle
- **架构模式**: 多模块微服务

## 🏃‍♂️ 快速开始

### 环境要求
- JDK 17+
- Redis Server
- Nacos Server
- RocketMQ（可选，用于事件处理）

### 本地环境搭建

1. **克隆代码库**
   ```bash
   git clone <this-repository-url>
   cd murmuration
   ```

2. **配置环境变量**
   ```bash
   # 复制并配置环境变量
   cp .env.example .env
   # 编辑 .env 文件，配置您的服务参数
   ```

3. **启动依赖服务**
   ```bash
   # 启动 Redis、Nacos 和其他必需服务
   # 在 Nacos 中配置您的应用程序配置文件
   ```

4. **构建并运行**
   ```bash
   ./gradlew build
   ./gradlew :murmuration-service:bootRun
   ```

5. **验证安装**
   ```bash
   curl http://localhost:8080/murmuration/health
   ```

应用程序将在 `http://localhost:8080/murmuration` 上可用

## 📁 项目结构

Murmuration 采用多模块架构设计，确保可扩展性和可维护性：

```
murmuration/
├── murmuration-common/      # 共享工具和核心模型
├── murmuration-sdk/         # 公共API接口和客户端库
├── murmuration-runtime/     # 核心工作流引擎和任务管理
├── murmuration-service/     # 主应用程序，包含REST API和业务逻辑
└── murmuration-ksp/         # 代码生成的 KSP 插件
```

### 模块职责

- **murmuration-common**: 基础工具、共享DTO和通用抽象
- **murmuration-sdk**: 面向客户端的API和外部集成SDK
- **murmuration-runtime**: 核心工作流引擎、调度系统和AI服务抽象
- **murmuration-service**: REST控制器、业务逻辑和AI服务实现
- **murmuration-ksp**: 代码生成插件，提供便于前端对接的REST API

## 🤝 贡献指南

有关详细的开发指南、编码标准和贡献工作流程，请参阅我们的[开发指导文档](docs/开发指南.md)。

## 📖 文档

- [架构设计文档](docs/架构设计.md) - 系统设计和组件的深入介绍
- [详细设计文档](docs/详细设计.md) - 实现细节和设计模式
- [开发指导文档](docs/开发指南.md) - 环境搭建、编码规范和最佳实践
