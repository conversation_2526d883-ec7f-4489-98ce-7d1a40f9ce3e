package team.aikero.murmuration.common.req.task.ai_box

import team.aikero.murmuration.common.vo.BackgroundImageVo
import team.aikero.murmuration.common.vo.ModelReferenceImageVo
import team.aikero.murmuration.common.vo.PostureVo
import team.aikero.murmuration.common.vo.ScenePictureVo

interface TaskRequest<T> {
    val bizId: String?
    val bizType: String?
    val handlerRequest: T
}

data class HandRepairRequest(
    override val bizId: String?,
    override val bizType: String?,
    override val handlerRequest: HandRepairHandlerRequest,
) : TaskRequest<HandRepairHandlerRequest>

data class SmartCuttingHeadRequest(
    override val bizId: String?,
    override val bizType: String?,
    override val handlerRequest: SmartCuttingHeadHandlerRequest
) : TaskRequest<SmartCuttingHeadHandlerRequest>

data class TextEditRequest(
    override val bizId: String?,
    override val bizType: String?,
    override val handlerRequest: TextEditHandlerRequest
) : TaskRequest<TextEditHandlerRequest>

data class RemoveWatermarkRequest(
    override val bizId: String?,
    override val bizType: String?,
    override val handlerRequest: RemoveWatermarkHandlerRequest,
    /**
     * 生成数量
     */
    val count: Int
) : TaskRequest<RemoveWatermarkHandlerRequest>

data class FourKRequest(
    override val bizId: String?,
    override val bizType: String?,
    override val handlerRequest: FourKHandlerRequest,
    /**
     * 生成数量
     */
    val count: Int,
) : TaskRequest<FourKHandlerRequest>

data class ChangeBackgroundRequest(
    override val bizId: String?,
    override val bizType: String?,
    override val handlerRequest: ChangeBackgroundHandlerRequest,
    /**
     * 场景list
     */
    val scenePictureList: List<ScenePictureVo>
) : TaskRequest<ChangeBackgroundHandlerRequest>

data class PostureRequest(
    override val bizId: String?,
    override val bizType: String?,
    override val handlerRequest: PostureFissionHandlerRequest,
    /**
     * 姿势list
     */
    val postureList: List<PostureVo>
) : TaskRequest<PostureFissionHandlerRequest>

data class TryOnRequest(
    override val bizId: String?,
    override val bizType: String?,
    override val handlerRequest: TryOnHandlerRequest,
    /**
     * 参考图list
     */
    val modelImageList: List<ModelReferenceImageVo>?,
    /**
     * 背景图list
     */
    val backgroundImageList: List<BackgroundImageVo>?,
    /**
     * 模特图list  //todo 本期不实现，后面要改成modelImageUrlList这样的类型
     */
    val targetModelFaceImageList: List<String>?,
) : TaskRequest<TryOnHandlerRequest>



