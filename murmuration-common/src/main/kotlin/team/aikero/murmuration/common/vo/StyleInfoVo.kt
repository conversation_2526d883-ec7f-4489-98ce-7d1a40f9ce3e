package team.aikero.murmuration.common.vo

data class StyleInfoVo (
    /**
     * 款式关联编号
     */
    val styleCode: String? = null,
    /**
     * 款式品类(款式品类-商品类型-商品末级分类)(code1-code2-code3)
     */
    val category: String,

    /**
     * 款式品类名(三级分类以"-"隔开)（如：女装-上装-T恤）
     */
    val categoryName: String? = null,

    /**
     * 市场编码
     */
    val marketCode: String? = null,

    /**
     * 市场风格编码
     */
    val marketStyleCode: String? = null,

    /**
     * 市场系列编码
     */
    val marketSeriesCode: String? = null,
)