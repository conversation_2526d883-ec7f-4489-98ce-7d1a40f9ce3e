package team.aikero.murmuration.common.req.task

import team.aikero.murmuration.common.enums.task.BgColor
import team.aikero.murmuration.common.enums.task.GenerationMode
import java.math.BigDecimal

data class LingVisionsReimageRequest(
    /**
     * 图片URL
     */
    val imageUrl: String,

    /**
     * 生图数量
     */
    val n: Int?,

    /**
     * 背景颜色
     */
    val bgColor: BgColor?,

    /**
     * 图片比例
     */
    val aspectRatio: String?,

    /**
     * 生成模式
     */
    val generationMode: GenerationMode?,

    /**
     * 相似度
     */
    val similarity: BigDecimal?,
)
