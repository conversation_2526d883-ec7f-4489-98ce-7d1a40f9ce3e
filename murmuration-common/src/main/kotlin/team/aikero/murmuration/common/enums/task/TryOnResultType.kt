package team.aikero.murmuration.common.enums.task

import com.fasterxml.jackson.annotation.JsonProperty

enum class TryOnResultType {
    /**
     * mj生成图
     */
    @JsonProperty("mj")
    MJ,

    /**
     * try on生成图
     */
    @JsonProperty("tryon")
    TRY_ON,

    /**
     * 换背景生成图
     */
    @JsonProperty("change_background")
    CHANGE_BACKGROUND,

    /**
     * 姿势裂变生成图
     */
    @JsonProperty("kontext")
    KONTEXT,

    /**
     * 换脸生成图
     */
    @JsonProperty("swap_face")
    SWAP_FACE,
}