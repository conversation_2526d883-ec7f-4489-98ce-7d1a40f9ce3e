#!/usr/bin/env kotlin
@file:DependsOn("cn.idev.excel:fastexcel:1.2.0")

import cn.idev.excel.annotation.ExcelProperty
import cn.idev.excel.converters.Converter
import cn.idev.excel.enums.CellDataTypeEnum
import cn.idev.excel.metadata.GlobalConfiguration
import cn.idev.excel.metadata.data.ReadCellData
import cn.idev.excel.metadata.data.WriteCellData
import cn.idev.excel.metadata.property.ExcelContentProperty

// 简化的测试版本，包含核心类定义
@Target(AnnotationTarget.FIELD)
@Retention(AnnotationRetention.RUNTIME)
annotation class DictFormat(val value: String)

@Target(AnnotationTarget.FIELD)
@Retention(AnnotationRetention.RUNTIME)
annotation class EnumName(val value: String)

interface DictProvider {
    fun support(code: String): Boolean
    fun mapping(code: String): Map<String, String> = mapping()
    fun mapping(): Map<String, String>
}

class MapDictProvider(val code: String, val dict: Map<String, String>) : DictProvider {
    override fun support(code: String): Boolean = this.code == code
    override fun mapping(): Map<String, String> = dict
}

object DictProviderRegistry {
    private val providers = linkedSetOf<DictProvider>()
    
    fun registerProvider(provider: DictProvider): DictProviderRegistry {
        providers.addFirst(provider)
        return this
    }
    
    fun getMapping(dictCode: String): Map<String, String> {
        for (it in providers) {
            if (it.support(dictCode)) {
                return it.mapping(dictCode)
            }
        }
        return emptyMap()
    }
}

// 通用枚举字典转换器（简化版）
object UniversalEnumDictConverter {
    private val converterCache = mutableMapOf<Class<*>, Converter<*>>()
    private val enumMetadataCache = mutableMapOf<Class<*>, EnumMetadata>()
    
    private data class EnumMetadata(
        val enumClass: Class<*>,
        val constants: Array<out Enum<*>>,
        val nameToConstant: Map<String, Enum<*>>,
        val displayNameToConstant: Map<String, Enum<*>>,
        val constantToDisplayName: Map<Enum<*>, String>
    )

    fun createConvertersForClass(dataClass: Class<*>): List<Converter<*>> {
        val converters = mutableListOf<Converter<*>>()
        
        dataClass.declaredFields.forEach { field ->
            if (field.type.isEnum) {
                val converter = getOrCreateConverter(field.type)
                converters.add(converter)
            }
        }
        
        return converters
    }

    @Suppress("UNCHECKED_CAST")
    private fun <E : Enum<E>> getOrCreateConverter(enumClass: Class<*>): Converter<E> {
        return converterCache.getOrPut(enumClass) {
            DynamicEnumConverter(enumClass as Class<E>)
        } as Converter<E>
    }

    private class DynamicEnumConverter<E : Enum<E>>(
        private val enumClass: Class<E>
    ) : Converter<E> {

        override fun supportJavaTypeKey(): Class<E> = enumClass
        override fun supportExcelTypeKey(): CellDataTypeEnum = CellDataTypeEnum.STRING

        override fun convertToJavaData(
            cellData: ReadCellData<*>,
            contentProperty: ExcelContentProperty?,
            globalConfiguration: GlobalConfiguration?
        ): E? {
            val cellValue = cellData.stringValue
            if (cellValue.isNullOrBlank()) return null

            val enumMetadata = getOrCreateEnumMetadata(enumClass)
            
            // 简化的转换逻辑
            return enumMetadata.nameToConstant[cellValue] as? E
                ?: enumMetadata.displayNameToConstant[cellValue] as? E
                ?: enumMetadata.nameToConstant.entries.find { 
                    it.key.equals(cellValue, ignoreCase = true) 
                }?.value as? E
        }

        override fun convertToExcelData(
            value: E?,
            contentProperty: ExcelContentProperty?,
            globalConfiguration: GlobalConfiguration?
        ): WriteCellData<*> {
            if (value == null) return WriteCellData<String>()

            val enumMetadata = getOrCreateEnumMetadata(enumClass)
            val displayValue = enumMetadata.constantToDisplayName[value] ?: value.name
            return WriteCellData<String>(displayValue)
        }
    }

    private fun getOrCreateEnumMetadata(enumClass: Class<*>): EnumMetadata {
        return enumMetadataCache.getOrPut(enumClass) {
            createEnumMetadata(enumClass)
        }
    }

    @Suppress("UNCHECKED_CAST")
    private fun createEnumMetadata(enumClass: Class<*>): EnumMetadata {
        val constants = enumClass.enumConstants as Array<out Enum<*>>
        val nameToConstant = constants.associateBy { it.name }
        val displayNameToConstant = mutableMapOf<String, Enum<*>>()
        val constantToDisplayName = mutableMapOf<Enum<*>, String>()
        
        constants.forEach { enumConstant ->
            try {
                val field = enumClass.getField(enumConstant.name)
                val enumNameAnnotation = field.getAnnotation(EnumName::class.java)
                if (enumNameAnnotation != null) {
                    val displayName = enumNameAnnotation.value
                    displayNameToConstant[displayName] = enumConstant
                    constantToDisplayName[enumConstant] = displayName
                } else {
                    constantToDisplayName[enumConstant] = enumConstant.name
                }
            } catch (e: Exception) {
                constantToDisplayName[enumConstant] = enumConstant.name
            }
        }
        
        return EnumMetadata(
            enumClass = enumClass,
            constants = constants,
            nameToConstant = nameToConstant,
            displayNameToConstant = displayNameToConstant,
            constantToDisplayName = constantToDisplayName
        )
    }
}

// 测试枚举
enum class TestStatus {
    @EnumName("活跃状态")
    ACTIVE,
    
    @EnumName("非活跃状态")
    INACTIVE,
    
    @EnumName("已禁用状态")
    DISABLED
}

enum class Priority {
    HIGH, MEDIUM, LOW
}

// 测试数据类
data class TestData(
    @ExcelProperty("姓名")
    var name: String,
    
    @DictFormat("status")
    @ExcelProperty("状态")
    var status: TestStatus,
    
    @DictFormat("priority")
    @ExcelProperty("优先级")
    var priority: Priority
)

// 测试函数
fun testUniversalEnumConverter() {
    println("=== 通用枚举转换器测试 ===")
    
    // 注册字典提供者
    DictProviderRegistry.registerProvider(
        MapDictProvider("priority", mapOf("HIGH" to "高优先级", "MEDIUM" to "中等优先级", "LOW" to "低优先级"))
    )
    
    // 测试自动创建转换器
    val converters = UniversalEnumDictConverter.createConvertersForClass(TestData::class.java)
    println("为 TestData 类自动创建了 ${converters.size} 个枚举转换器")
    
    converters.forEach { converter ->
        println("- 转换器类型: ${converter.supportJavaTypeKey().simpleName}")
    }
    
    // 测试转换功能
    val statusConverter = converters.find { it.supportJavaTypeKey() == TestStatus::class.java }
    val priorityConverter = converters.find { it.supportJavaTypeKey() == Priority::class.java }

    if (statusConverter != null) {
        println("\n测试 TestStatus 转换器:")
        @Suppress("UNCHECKED_CAST")
        val typedStatusConverter = statusConverter as Converter<TestStatus>
        val writeData = typedStatusConverter.convertToExcelData(TestStatus.ACTIVE, null, null)
        println("- TestStatus.ACTIVE -> Excel: ${writeData.stringValue}")
    }

    if (priorityConverter != null) {
        println("\n测试 Priority 转换器:")
        @Suppress("UNCHECKED_CAST")
        val typedPriorityConverter = priorityConverter as Converter<Priority>
        val writeData = typedPriorityConverter.convertToExcelData(Priority.HIGH, null, null)
        println("- Priority.HIGH -> Excel: ${writeData.stringValue}")
    }
    
    println("\n=== 测试完成 ===")
    println("✅ 通用枚举转换器成功实现自动类型检测和转换器创建")
    println("✅ 避免了手动为每个枚举类型创建转换器的繁琐工作")
    println("✅ 保持了类型安全和性能优化")
}

// 运行测试
testUniversalEnumConverter()
