package team.aikero.murmuration.core.scheduler.backoff

import org.junit.jupiter.api.Test
import java.math.BigDecimal
import java.time.Duration
import kotlin.time.Duration.Companion.milliseconds

class ExponentialBackoffPolicyTest {

    @Test
    fun base2() {
        val backoff = ExponentialBackoffPolicy(
            base = BigDecimal.TWO,
            initialDelay = Duration.ofSeconds(1).toMillis().toBigDecimal(),
            maxDelay = Duration.ofMinutes(10).toMillis().toBigDecimal(),
            jitterPolicy = JitterPolicy.NONE,
        )
        calculateAndPrint(backoff)
    }

    /**
     * 默认配置
     */
    @Test
    fun defaultConfiguration() {
        val backoff = ExponentialBackoffPolicy(
            base = BigDecimal("1.6"),
            initialDelay = Duration.ofSeconds(1).toMillis().toBigDecimal(),
            maxDelay = Duration.ofMinutes(30).toMillis().toBigDecimal(),
            jitterPolicy = JitterPolicy.EQUAL,
        )
        calculateAndPrint(backoff)
    }

    private fun calculateAndPrint(backoff: BackoffPolicy) {
        val total = (0..59).sumOf {
            val nextTime = backoff.next(it)
            println("任务-第${it + 1}次退避: ${nextTime.milliseconds}")
            nextTime
        }
        println("任务-总耗时: ${total.milliseconds}")
    }
}
