package team.aikero.murmuration.core.workflow.engine.expression

import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import team.aikero.murmuration.core.workflow.context.WorkflowContext
import team.aikero.murmuration.core.workflow.context.WorkflowNodeContext
import team.aikero.murmuration.core.workflow.entity.*

/**
 * 工作流表达式评估器单元测试
 *
 * <AUTHOR>
 */
class WorkflowExpressionEvaluatorTest {

    private lateinit var evaluator: WorkflowExpressionEvaluator
    private lateinit var mockContext: WorkflowNodeContext
    private lateinit var mockWorkflowContext: WorkflowContext
    private lateinit var mockNodeInstance: NodeInstance
    private lateinit var mockWorkflowInstance: WorkflowInstance

    @BeforeEach
    fun setup() {
        evaluator = WorkflowExpressionEvaluator()

        // 创建模拟对象
        mockNodeInstance = mock()
        mockWorkflowInstance = mock()
        mockWorkflowContext = mock()

        // 设置模拟对象行为
        whenever(mockWorkflowContext.workflowInstance).thenReturn(mockWorkflowInstance)
        whenever(mockWorkflowContext.storage).thenReturn(mutableMapOf())

        // 创建上下文
        mockContext = WorkflowNodeContext(
            workflowContext = mockWorkflowContext,
            node = mockNodeInstance,
            storage = mutableMapOf(),
        )
    }

    /**
     * 测试节点输出引用表达式
     */
    @Test
    fun `test node output reference expression`() {
        // 准备测试数据
        val startNodeInstance = mock<NodeInstance>()
        val startNodeDefinition = mock<NodeDefinition>()
        val startNodeDetail = mock<NodeInstanceDetail>()

        // 设置节点定义
        whenever(startNodeDefinition.nodeKey).thenReturn("开始")
        whenever(startNodeInstance.nodeDefinition).thenReturn(startNodeDefinition)
        whenever(startNodeInstance.status).thenReturn(NodeStatus.COMPLETED)
        whenever(startNodeInstance.id).thenReturn(1L)
        whenever(startNodeInstance.detail).thenReturn(startNodeDetail)

        // 设置节点输出数据
        val outputData = mapOf(
            "data" to mapOf(
                "images" to listOf(
                    mapOf("url" to "https://example.com/image1.jpg")
                )
            )
        )
        whenever(startNodeDetail.output).thenReturn(outputData)

        // 设置工作流实例包含这个节点
        whenever(mockWorkflowInstance.nodeInstances).thenReturn(listOf(startNodeInstance, mockNodeInstance))

        // 测试表达式
        val expression = "#{开始.data.images}"
        val result = evaluator.evaluate(expression, mockContext)

        // 验证结果
        assertNotNull(result)
        assertTrue(result is List<*>)
        assertEquals(1, (result as List<*>).size)
        assertEquals("https://example.com/image1.jpg", (result[0] as Map<*, *>)["url"])
    }

    /**
     * 测试条件表达式
     */
    @Test
    fun `test conditional expression`() {
        // 准备测试数据
        val startNodeInstance = mock<NodeInstance>()
        val startNodeDefinition = mock<NodeDefinition>()
        val startNodeDetail = mock<NodeInstanceDetail>()

        // 设置节点定义
        whenever(startNodeDefinition.nodeKey).thenReturn("开始")
        whenever(startNodeInstance.nodeDefinition).thenReturn(startNodeDefinition)
        whenever(startNodeInstance.status).thenReturn(NodeStatus.COMPLETED)
        whenever(startNodeInstance.id).thenReturn(1L)
        whenever(startNodeInstance.detail).thenReturn(startNodeDetail)

        // 设置节点输出数据
        val outputData = mapOf(
            "count" to 5,
            "images" to listOf(
                mapOf("url" to "https://example.com/image1.jpg")
            )
        )
        whenever(startNodeDetail.output).thenReturn(outputData)

        // 设置工作流实例包含这个节点
        whenever(mockWorkflowInstance.nodeInstances).thenReturn(listOf(startNodeInstance, mockNodeInstance))

        // 测试表达式
        val expression = "#{开始.count > 0 ? 开始.images[0].url : 'default.jpg'}"
        val result = evaluator.evaluate(expression, mockContext)

        // 验证结果
        assertEquals("https://example.com/image1.jpg", result)
    }

    /**
     * 测试表达式标准化
     */
    @Test
    fun `test normalize expression`() {
        val expression = "#{开始.images} + #{开始.images}"
        val method = evaluator.javaClass.getDeclaredMethod("normalizeExpression", String::class.java)
        method.isAccessible = true
        val result = method.invoke(evaluator, expression) as String
        assertEquals("#{开始.images + 开始.images}", result)
    }

    /**
     * 测试两个集合相加的表达式运算
     */
    @Test
    fun `test list addition expression`() {
        // 准备测试数据
        val startNodeInstance = mock<NodeInstance>()
        val startNodeDefinition = mock<NodeDefinition>()
        val startNodeDetail = mock<NodeInstanceDetail>()

        // 设置节点定义
        whenever(startNodeDefinition.nodeKey).thenReturn("开始")
        whenever(startNodeInstance.nodeDefinition).thenReturn(startNodeDefinition)
        whenever(startNodeInstance.status).thenReturn(NodeStatus.COMPLETED)
        whenever(startNodeInstance.id).thenReturn(1L)
        whenever(startNodeInstance.detail).thenReturn(startNodeDetail)

        // 设置节点输出数据
        val outputData = mapOf(
            "images" to listOf(
                mapOf("url" to "https://example.com/image1.jpg")
            )
        )
        whenever(startNodeDetail.output).thenReturn(outputData)

        // 设置工作流实例包含这个节点
        whenever(mockWorkflowInstance.nodeInstances).thenReturn(listOf(startNodeInstance, mockNodeInstance))

        // 测试表达式
        val expression = "#{开始.images} + #{开始.images}"
        val result = evaluator.evaluate(expression, mockContext)

        // 验证结果
        assertNotNull(result)
        assertTrue(result is List<*>)
        assertEquals(2, (result as List<*>).size)
    }

    /**
     * 测试不同集合相加的表达式运算
     */
    @Test
    fun `test different lists addition expression`() {
        // 准备测试数据
        val node1Instance = mock<NodeInstance>()
        val node1Definition = mock<NodeDefinition>()
        val node1Detail = mock<NodeInstanceDetail>()

        val node2Instance = mock<NodeInstance>()
        val node2Definition = mock<NodeDefinition>()
        val node2Detail = mock<NodeInstanceDetail>()

        // 设置第一个节点
        whenever(node1Definition.nodeKey).thenReturn("节点1")
        whenever(node1Instance.nodeDefinition).thenReturn(node1Definition)
        whenever(node1Instance.status).thenReturn(NodeStatus.COMPLETED)
        whenever(node1Instance.id).thenReturn(1L)
        whenever(node1Instance.detail).thenReturn(node1Detail)

        val outputData1 = mapOf(
            "items" to listOf("a", "b")
        )
        whenever(node1Detail.output).thenReturn(outputData1)

        // 设置第二个节点
        whenever(node2Definition.nodeKey).thenReturn("节点2")
        whenever(node2Instance.nodeDefinition).thenReturn(node2Definition)
        whenever(node2Instance.status).thenReturn(NodeStatus.COMPLETED)
        whenever(node2Instance.id).thenReturn(2L)
        whenever(node2Instance.detail).thenReturn(node2Detail)

        val outputData2 = mapOf(
            "items" to listOf("c", "d")
        )
        whenever(node2Detail.output).thenReturn(outputData2)

        // 设置工作流实例包含这些节点
        whenever(mockWorkflowInstance.nodeInstances).thenReturn(listOf(node1Instance, node2Instance, mockNodeInstance))

        // 测试表达式
        val expression = "#{节点1.items} + #{节点2.items}"
        val result = evaluator.evaluate(expression, mockContext)

        // 验证结果
        assertNotNull(result)
        assertTrue(result is List<*>)
        val resultList = result as List<*>
        assertEquals(4, resultList.size)
        assertEquals("a", resultList[0])
        assertEquals("b", resultList[1])
        assertEquals("c", resultList[2])
        assertEquals("d", resultList[3])
    }

}
