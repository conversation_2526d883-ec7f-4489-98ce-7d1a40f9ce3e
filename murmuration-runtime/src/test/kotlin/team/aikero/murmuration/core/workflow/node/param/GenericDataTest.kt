package team.aikero.murmuration.core.workflow.node.param

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import team.aikero.blade.user.entity.CurrentUser
import team.aikero.murmuration.core.workflow.node.param.GenericData


/**
 * GenericData 单元测试
 *
 * <AUTHOR>
 */
class GenericDataTest {

    /**
     * 处理图片数据
     */
    @Test
    fun `process images`() {
        // 创建图片数据
        val imageData = mapOf(
            "images" to listOf(
                mapOf("url" to "https://example.com/image1.jpg"),
                mapOf("url" to "https://example.com/image2.jpg")
            ),
            "processType" to "resize",
        )

        val input = GenericData(imageData)

        // 可以从GenericData中获取数据
        val images = input.get<List<Map<String, Any>>>("images")

        assertEquals(2, images?.size)
    }

    /**
     * 处理用户信息
     */
    @Test
    fun `process user info`() {

        val userInfo = CurrentUser(1, "test", "test", 1, 1)

        // 转换为GenericInput
        val input = GenericData.from(userInfo)

        // 可以转换回原始类型
        val reconstructedUserInfo = input.to<CurrentUser>()

        assertEquals(userInfo.name, reconstructedUserInfo.name)
    }

    /**
     * 处理混合数据结构
     */
    @Test
    fun `process mixed data`() {
        // 创建复杂的混合数据结构
        val complexData = mapOf(
            "taskId" to "task_001",
            "type" to "AI_PROCESSING",
            "inputData" to mapOf(
                "text" to "这是一段需要处理的文本",
                "images" to listOf("img1.jpg", "img2.jpg"),
                "options" to mapOf(
                    "model" to "gpt-4",
                    "temperature" to 0.7,
                    "maxTokens" to 1000
                )
            ),
            "metadata" to mapOf(
                "userId" to "user123",
                "timestamp" to System.currentTimeMillis(),
                "priority" to "high"
            )
        )

        // 转换为GenericInput
        val input = GenericData(complexData)

        // 可以访问嵌套数据
        val taskType = input.get<String>("type")
        val inputData = input.get<Map<String, Any>>("inputData")
        val text = inputData?.get("text") as? String

        assertEquals("AI_PROCESSING", taskType)
        assertEquals("这是一段需要处理的文本", text)
        assertEquals(3, inputData?.size)
    }

    /**
     * 数据转换和增强
     */
    @Test
    fun `data transformation and enhancement`() {
        // 原始数据
        val originalData = mapOf(
            "productId" to "prod123",
            "name" to "智能音箱",
            "price" to 299.99
        )

        // 创建GenericInput
        val input = GenericData(originalData)

        // 增强数据
        val enhancedData = input
            .set("category", "智能设备")
            .set("stock", 100)

        // 创建输出
        val output = GenericData.from(enhancedData)

        assertEquals(100, output.get<Int>("stock"))
        assertEquals("智能设备", output.get<String>("category"))
    }

    /**
     * 不同数据类型的合并
     */
    @Test
    fun `data merging`() {
        // 第一个数据源
        val data1 = GenericData.from(
            mapOf(
                "userId" to "user123",
                "name" to "张三"
            )
        )

        // 第二个数据源
        val data2 = GenericData.from(
            mapOf(
                "email" to "<EMAIL>",
                "phone" to "13800138000"
            )
        )

        // 合并数据
        val mergedData = data1.merge(data2)

        assertEquals("张三", mergedData.get<String>("name"))
        assertEquals("<EMAIL>", mergedData.get<String>("email"))
    }

}
