package team.aikero.murmuration.infra.rocketmq

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import team.aikero.murmuration.MurmurationApplication
import team.aikero.murmuration.core.workflow.entity.TaskStatus
import team.aikero.murmuration.event.listener.MQTaskResult
import team.aikero.murmuration.event.listener.TaskStatusNotification
import java.util.*

@DisplayName("RocketMQ阿里云客户端测试")
@SpringBootTest(classes = [MurmurationApplication::class])
class RocketMQAliyunClientTest(
    @Autowired val aliyunClient: AliyunRocketMQClient,
    @Autowired val rocketMQClient: RocketMQClient,
) {

    @Test
    @DisplayName("根据MessageKey查询消息")
    fun getMessageByKey() {
        val messageKey = UUID.randomUUID().toString()
        val sendMessage = Message(
            topic = TopicHolder.topic,
            keys = listOf(messageKey),
            payload = TaskStatusNotification(
                taskId = 7344299007902273537,
                bizId = "7e2165b5-9e33-48a7-8072-af09e44240d2",
                bizType = "UNIT_TEST",
                status = TaskStatus.COMPLETED,
                results = listOf(
                    MQTaskResult(
                        url = "https://oss-datawork.oss-cn-hangzhou.aliyuncs.com/ai_images/server/UPscale/3693618586259490750.png",
                    )
                ),
            )
        )
        rocketMQClient.send(sendMessage)

        val receivedMessage = aliyunClient.getMessageByKey(messageKey)
        assertThat(receivedMessage).isEqualTo("{\"taskId\":\"7344299007902273537\",\"bizId\":\"7e2165b5-9e33-48a7-8072-af09e44240d2\",\"bizType\":\"UNIT_TEST\",\"status\":\"COMPLETED\",\"results\":[{\"url\":\"https://oss-datawork.oss-cn-hangzhou.aliyuncs.com/ai_images/server/UPscale/3693618586259490750.png\"}]}")
    }
}
