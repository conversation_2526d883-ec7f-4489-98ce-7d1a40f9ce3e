server:
  servlet:
    # 本地定义便于 api 识别
    context-path: /murmuration
spring:
  application:
    name: murmuration-service
  cloud:
    nacos:
      config:
        server-addr: ${nacos.registry-server}
        username: ${nacos.username}
        password: ${nacos.password}
        namespace: ${spring.profiles.active}
        group: ${nacos.group}
        file-extension: yml
  config:
    import:
      - nacos:nacos-register.yml
      - nacos:common-configuration.yml
      - nacos:jackson-config
      - nacos:blade-config
#      - nacos:jimmer-conf
      - nacos:${spring.application.name}

jimmer:
  language: kotlin
  show-sql: false

logging:
  level:
    team.aikero.murmuration: debug
