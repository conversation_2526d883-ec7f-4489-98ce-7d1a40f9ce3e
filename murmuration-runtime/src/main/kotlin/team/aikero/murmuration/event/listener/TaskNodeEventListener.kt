package team.aikero.murmuration.event.listener

import org.babyfish.jimmer.sql.ast.mutation.SaveMode
import org.babyfish.jimmer.sql.kt.KSqlClient
import org.babyfish.jimmer.sql.kt.ast.expression.eq
import org.babyfish.jimmer.sql.kt.fetcher.newFetcher
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Component
import team.aikero.blade.auth.withSystemUser
import team.aikero.murmuration.core.scheduler.Scheduler
import team.aikero.murmuration.core.workflow.entity.NodeInstance
import team.aikero.murmuration.core.workflow.entity.NodeStatus
import team.aikero.murmuration.core.workflow.entity.TaskInstance
import team.aikero.murmuration.core.workflow.entity.by
import team.aikero.murmuration.core.workflow.entity.nodeInstanceId
import team.aikero.murmuration.core.workflow.event.NodeWork
import team.aikero.murmuration.core.workflow.event.NodeWork.Companion.toWork
import team.aikero.murmuration.core.workflow.event.TaskCompletedEvent
import team.aikero.murmuration.core.workflow.event.TaskPrepareEvent

/**
 * 任务节点特有的事件监听器
 *
 * <AUTHOR>
 */
@Component
class TaskNodeEventListener(
    val sql: KSqlClient,
    val scheduler: Scheduler<NodeWork>,
) {

    /**
     * 当任务重试时，更新节点状态
     */
    @EventListener
    fun onTaskRetryUpdateNodeStatus(event: TaskPrepareEvent) = withSystemUser {
        val task = event.task
        val nodeInstanceId = task.nodeId ?: return@withSystemUser

        // 检查节点下任务是否处于验收状态，如果是验收状态，更新到运行中
        val nodeInstance = sql.findOneById(NodeInstance::class, nodeInstanceId)
        if (nodeInstance.status != NodeStatus.REVIEWING) {
            return@withSystemUser
        }

        sql.save(NodeInstance {
            this.id = nodeInstanceId
            this.status = NodeStatus.RUNNING
        }, SaveMode.UPDATE_ONLY)

        // 直接提交节点事件，触发节点更新动作。
        scheduler.submit(nodeInstance.toWork())
    }

    /**
     * 当任务完成后，更新节点状态
     */
    @EventListener
    fun onTaskCompletedUpdateNodeStatus(event: TaskCompletedEvent) = withSystemUser {
        val task = event.task
        val nodeInstanceId = task.nodeId ?: return@withSystemUser

        // 检查节点下任务是否全部完成
        val taskInstances = sql.findAll(newFetcher(TaskInstance::class).by {
            status()
        }){
            where(table.nodeInstanceId eq nodeInstanceId)
        }
        if (taskInstances.any { !it.status.isFinished() }) {
            return@withSystemUser
        }

        val nodeInstance = sql.findOneById(newFetcher(NodeInstance::class).by {
            supplier()
            ability()
            workflowInstance()
        }, nodeInstanceId)

        // 直接提交节点事件，触发节点更新动作。
        scheduler.submit(nodeInstance.toWork())
    }

}
