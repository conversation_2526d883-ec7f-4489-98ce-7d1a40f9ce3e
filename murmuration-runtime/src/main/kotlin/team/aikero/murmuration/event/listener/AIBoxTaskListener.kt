package team.aikero.murmuration.event.listener

import com.fasterxml.jackson.databind.ObjectMapper
import org.babyfish.jimmer.sql.ast.mutation.SaveMode
import org.babyfish.jimmer.sql.kt.KSqlClient
import org.babyfish.jimmer.sql.kt.fetcher.newFetcher
import org.redisson.api.RedissonClient
import org.springframework.cloud.openfeign.EnableFeignClients
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Component
import team.aikero.blade.auth.withSystemUser
import team.aikero.blade.core.event.ErrorReportEvent
import team.aikero.blade.core.toolkit.isBlank
import team.aikero.blade.core.toolkit.isNull
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.spring.Springs
import team.aikero.murmuration.common.req.task.ai_box.ChangeBackgroundHandlerInputRequest
import team.aikero.murmuration.common.req.task.ai_box.TaskSource
import team.aikero.murmuration.core.Ability
import team.aikero.murmuration.core.Supplier
import team.aikero.murmuration.core.workflow.entity.*
import team.aikero.murmuration.core.workflow.event.TaskCompletedEvent
import team.aikero.murmuration.core.workflow.event.TaskEvent
import team.aikero.murmuration.core.workflow.event.TaskPrepareEvent
import team.aikero.murmuration.core.workflow.event.TaskWork
import team.aikero.murmuration.infra.rocketmq.AIBoxTopicHolder
import team.aikero.murmuration.infra.rocketmq.Message
import team.aikero.murmuration.infra.rocketmq.RocketMQClient
import team.aikero.murmuration.infra.rocketmq.TopicHolder
import tech.tiangong.bfg.common.req.LocalPictureCaptionCallbackReq
import tech.tiangong.bfg.sdk.client.FmSceneInfoClient
import java.time.LocalDateTime

@Component
@EnableFeignClients(basePackages = ["tech.tiangong.bfg.sdk.client"])
class AIBoxTaskListener(
    val sql: KSqlClient,
    val objectMapper: ObjectMapper,
    val redisson: RedissonClient,
    val rocketMQClient: RocketMQClient,
    val fmSceneInfoClient: FmSceneInfoClient,
) {

    @EventListener
    fun onTaskRunning(event: TaskPrepareEvent) = withSystemUser {
        val task = event.task
        val taskInstance = sql.findOneById(TASK_INSTANCE_FETCHER, task.id)

        // 只处理触发来源为API调用的任务
        if (taskInstance.triggerSource != TriggerSource.AI_BOX) {
            return@withSystemUser
        }

        // 状态检查
        if (taskInstance.status != TaskStatus.PREPARED) {
            throw IllegalStateException("任务实例[${taskInstance.id}]状态异常: 预期为[${TaskStatus.COMPLETED}], 实际为[${taskInstance.status}]")
        }

        val aiBoxTask = taskInstance.aiBoxTask!!

        sql.save(
            AIBoxTask {
                this.id = aiBoxTask.id
                this.finishTime = LocalDateTime.now()
                this.status = TaskStatus.RUNNING
            }, SaveMode.UPDATE_ONLY
        )

    }

    /**
     * 任务完成事件
     */
    @EventListener
    fun onTaskCompleted(event: TaskEvent) = withSystemUser {
        // 只处理终结状态
        if (!event.status.isFinished()) {
            return@withSystemUser
        }

        val task = event.task
        val taskInstance = sql.findOneById(TASK_INSTANCE_FETCHER, task.id)

        // 只处理触发来源为API调用的任务
        if (taskInstance.triggerSource != TriggerSource.AI_BOX) {
            return@withSystemUser
        }

        // 状态检查
        if (taskInstance.status != event.status) {
            throw IllegalStateException("任务实例[${taskInstance.id}]状态异常: 预期为[${event.status}], 实际为[${taskInstance.status}]")
        }

        // 只有 AiBoxTask 下的所有任务完成后，才更新 AiBoxTask 本身的状态
        // 由于多个任务可能同时完成，因此需要加锁
        val aiBoxTask = taskInstance.aiBoxTask!!

        val lock = redisson.getLock("AiBoxTaskListener:${aiBoxTask.id}")
        lock.lock()

        try {
            // 有一个执行成功且没有生成中的任务为AIBox成功
            val asCompleted = aiBoxTask.taskInstances.any { it.status == TaskStatus.COMPLETED } &&
                    aiBoxTask.taskInstances.any { it.status != TaskStatus.RUNNING } &&
                    aiBoxTask.taskInstances.all { it.status != TaskStatus.PREPARED }
            // 全部失败才为失败
            val asFailed = aiBoxTask.taskInstances.all { it.status == TaskStatus.FAILED }
            val modify = AIBoxTask {
                this.id = aiBoxTask.id
                this.finishTime = LocalDateTime.now()
            }
            if (asCompleted) {
                asCompleted(aiBoxTask, modify, task)
            }
            if (asFailed) {
                asFailed(aiBoxTask, modify, task)
            }
        } finally {
            lock.unlock()
        }
    }

    private fun asCompleted(origin: AIBoxTask, modify: AIBoxTask, task: TaskWork) {
        sql.save(modify.copy { this.status = TaskStatus.COMPLETED }, SaveMode.UPDATE_ONLY)
        if (origin.taskSource != TaskSource.AI_BOX) {
            sendMq(origin, task, TaskStatus.COMPLETED)
        }
    }

    private fun asFailed(origin: AIBoxTask, modify: AIBoxTask, task: TaskWork) {
        sql.save(modify.copy { this.status = TaskStatus.FAILED }, SaveMode.UPDATE_ONLY)
        if (origin.taskSource != TaskSource.AI_BOX) {
            sendMq(origin, task, TaskStatus.FAILED)
        }
    }

    private fun sendMq(aiBoxTask: AIBoxTask, task: TaskWork, status: TaskStatus) {
        val messageBody = AIBoxTaskFinishNotification(
            aiBoxTaskId = aiBoxTask.id,
            taskSourceId = aiBoxTask.taskSourceId!!,
            taskSource = aiBoxTask.taskSource,
            status = status,
            results = aiBoxTask.taskInstances
                .flatMap { taskInstance ->
                    taskInstance.results
                        .map { result ->
                            AIBoxMQTaskResult(result.url)
                        }
                }
        )

        val message = Message(
            topic = AIBoxTopicHolder.topic,
            keys = listOf("${aiBoxTask.id}"),
            tag = "${task.ability}:${task.supplier}",
            payload = messageBody,
        )
        rocketMQClient.send(message)
    }


    /**
     * 回调本地上传的背景图片的提示词
     */
    @EventListener
    fun scenePictureCaptionCallback(event: TaskCompletedEvent) = withSystemUser {
        val task = event.task
        // 不是换背景的任务直接返回
        if (task.ability != Ability.CHANGE_BACKGROUND || task.supplier != Supplier.AIP) {
            return@withSystemUser
        }

        val taskInstance = sql.findOneById(newFetcher(TaskInstance::class).by {
            status()
            results {
                attributes()
            }
            detail {
                request()
            }
        }, task.id)

        if (taskInstance.status != TaskStatus.COMPLETED
            || taskInstance.detail?.request.isNull()
            || taskInstance.results.isEmpty()
            || taskInstance.results.first().attributes.isNull()) {
            return@withSystemUser
        }

        var pictureId = 1L

        try {
            val request = objectMapper.treeToValue(taskInstance.detail!!.request, ChangeBackgroundHandlerInputRequest::class.java)
            if (request.scenePicture.caption.isBlank()) {   //没提示才需要回填
                return@withSystemUser
            }
            pictureId = request.scenePicture.pictureId
            val caption = objectMapper.treeToValue(taskInstance.results.first().attributes, String::class.java)
            if (caption.isBlank()) {
                return@withSystemUser
            }
            val localCaptionCallback =
                fmSceneInfoClient.localCaptionCallback(LocalPictureCaptionCallbackReq(pictureId, caption))
            log.info { "更新背景图提示词:[${pictureId}]结果=${localCaptionCallback.successful}" }
        } catch (e: Exception) {
            log.error(e) { "更新背景图提示词失败:[${pictureId}] ${e.message}" }
            Springs.publishEvent(
                ErrorReportEvent(
                    source = "AIBox换背景-更新背景图提示词",
                    ex = e,
                    context = mapOf(
                        "背景图ID" to pictureId
                    ),
                )
            )
        }
    }

    companion object {
        private val TASK_INSTANCE_FETCHER = newFetcher(TaskInstance::class).by {
            triggerSource()
            bizId()
            bizType()
            status()
            results {
                url()
            }
            aiBoxTask {
                taskSourceId()
                taskSource()
                taskInstances {
                    status()
                }
            }
        }
    }

}


data class AIBoxTaskFinishNotification(
    override val aiBoxTaskId: Long,
    override val taskSourceId: String,
    override val taskSource: TaskSource,
    override val status: TaskStatus,
    val results: List<AIBoxMQTaskResult>,
) : AIBoxTaskStatusNotification(aiBoxTaskId, taskSourceId, taskSource, status)

open class AIBoxTaskStatusNotification(
    open val aiBoxTaskId: Long,
    open val taskSourceId: String,
    open val taskSource: TaskSource,
    open val status: TaskStatus,
)

data class AIBoxMQTaskResult(
    val url: String
)



