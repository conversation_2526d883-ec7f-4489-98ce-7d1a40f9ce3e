package team.aikero.murmuration.util

import org.springframework.core.GenericTypeResolver
import kotlin.reflect.KClass

/**
 * 解析泛型参数类型
 *
 * @param self 当前对象
 * @param genericType 泛型类型
 * @param argumentIndex 泛型参数索引
 * @return 泛型参数类型
 */
@Suppress("UNCHECKED_CAST")
fun <T : Any> resolveGenericType(self: Any, genericType: KClass<*>, argumentIndex: Int): KClass<T> {
    return GenericTypeResolver
        .resolveTypeArguments(self::class.java, genericType.java)
        ?.let { it[argumentIndex].kotlin as KClass<T> }
        ?: throw IllegalStateException("解析泛型参数类型失败")
}
