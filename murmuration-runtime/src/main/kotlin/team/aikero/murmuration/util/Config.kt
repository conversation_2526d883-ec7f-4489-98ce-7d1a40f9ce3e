package team.aikero.murmuration.util

import team.aikero.blade.util.spring.property
import team.aikero.murmuration.util.exception.MissingRequiredConfigException

inline fun <reified T> requiredProperty(key: String): T = property<T>(key) ?: throw MissingRequiredConfigException(key)

/**
 * 获取当前环境：dev/qa/uat/prod
 */
val env by lazy {
    val profile = requiredProperty<String>("spring.profiles.active")
    profile.substringBefore("-")
}
