package team.aikero.murmuration.util

import java.util.concurrent.CompletableFuture
import java.util.concurrent.Executors

private val executor = Executors.newVirtualThreadPerTaskExecutor()

fun <R> async(block: () -> R): CompletableFuture<R> {
    return CompletableFuture.supplyAsync(block, executor)
}

fun <T> List<CompletableFuture<T>>.await(): List<T> {
    val allDone = CompletableFuture.allOf(*this.toTypedArray())
    return allDone.thenApply { this.map { it.join() } }.join()
}
