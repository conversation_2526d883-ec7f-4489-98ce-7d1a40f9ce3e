package team.aikero.murmuration.util

import java.net.URI
import java.net.URISyntaxException
import kotlin.IllegalStateException

/**
 * 校验字符串非空
 */
fun checkNotBlank(string: String?, lazyMessage: () -> Any): String {
    if (string == null || string.isBlank()) {
        val message = lazyMessage()
        throw IllegalStateException(message.toString())
    }
    return string
}

/**
 * 校验URL合法
 */
fun checkValidUrl(url: String, lazyMessage: () -> Any) {
    try {
        URI(url)
    }
    catch (ex: URISyntaxException) {
        val message = lazyMessage()
        throw IllegalStateException(message.toString(), ex)
    }
}
