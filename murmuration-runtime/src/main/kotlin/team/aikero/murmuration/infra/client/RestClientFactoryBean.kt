package team.aikero.murmuration.infra.client

import org.springframework.beans.factory.FactoryBean
import org.springframework.http.client.ClientHttpRequestInterceptor

/**
 * RestClientFactoryBean
 *
 * @see RestClient
 * <AUTHOR>
 */
@Suppress("MemberVisibilityCanBePrivate")
class RestClientFactoryBean @JvmOverloads constructor(
    /**
     * 代理对象
     */
    val clazz: Class<*>,
    /**
     * 绝对URL或可解析主机名
     */
    val url: String,

    /**
     * 连接超时（以秒为单位）
     */
    val connectionTimeout: Long = 5,

    /**
     * 读取超时（以秒为单位）
     */
    val readTimeout: Long = 10,

    /**
     * 拦截器配置类数组
     */
    val interceptorClasses: Array<Class<out ClientHttpRequestInterceptor>> = emptyArray()
) : FactoryBean<Any> {

    override fun getObject(): Any {
        return RestClients.build(clazz, url, connectionTimeout, readTimeout, interceptorClasses)
    }

    override fun getObjectType(): Class<*> {
        return clazz
    }

}
