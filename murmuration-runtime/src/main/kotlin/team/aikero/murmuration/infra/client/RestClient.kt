package team.aikero.murmuration.infra.client

import org.springframework.http.client.ClientHttpRequestInterceptor
import kotlin.reflect.KClass

/**
 * RestClient 客户端注解
 *
 * 与 @HttpExchange 区分
 *
 * <AUTHOR>
 */
@Target(AnnotationTarget.CLASS)
@Retention(AnnotationRetention.RUNTIME)
annotation class RestClient(
    /**
     * 绝对URL或可解析主机名
     */
    val url: String,

    /**
     * 连接超时（以秒为单位）
     */
    val connectionTimeout: Long = 5,

    /**
     * 读取超时（以秒为单位）
     */
    val readTimeout: Long = 10,

    /**
     * 拦截器配置类
     */
    val interceptor: Array<KClass<out ClientHttpRequestInterceptor>> = [],
)
