package team.aikero.murmuration.infra.client

import com.fasterxml.jackson.module.kotlin.convertValue
import org.springframework.beans.BeanUtils
import org.springframework.core.MethodParameter
import org.springframework.core.Ordered
import org.springframework.http.HttpMethod
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.multipart.MultipartFile
import org.springframework.web.service.invoker.HttpRequestValues
import org.springframework.web.service.invoker.HttpServiceArgumentResolver
import org.springframework.web.util.UriBuilderFactory
import team.aikero.blade.util.json.Json
import java.net.URI
import java.util.*

/**
 * [ObjectRequestParamArgumentResolver]用于转换 Java Bean 为请求参数。
 *
 * Spring 默认行为会将 GET 请求参数转为 Java 对象，但 HttpExchange Client 端不支持，增加兼容性。
 *
 * <AUTHOR>
 */
class ObjectRequestParamArgumentResolver : HttpServiceArgumentResolver, Ordered {

    override fun resolve(argument: Any?, parameter: MethodParameter, requestValues: HttpRequestValues.Builder): Boolean {
        if (isNonResolvableArgument(argument, parameter)) {
            return false
        }

        return process(argument!!, requestValues)
    }

    private fun process(argument: Any, requestValues: HttpRequestValues.Builder): Boolean {
        /*
        NOTE: why not convert map to request parameters?

        The following code will not fill the map with request parameters by default, you need @RequestParam to do that.

        @GetMapping
        public List<Foo> findAll(Map<String, Object> map) {
            return List.of();
        }

        So on the client side, we do the same thing as the server side, DO NOT convert map to request parameters.
        */
        if (argument is Map<*, *>) {
            return false
        }
        val nameToValue: Map<String, Any?> = Json.instance.convertValue(argument)
        populateRequestValuesFromMap(requestValues, nameToValue)
        return true
    }

    /**
     * 将Map中的值填充到请求参数中
     */
    @Suppress("UNCHECKED_CAST")
    private fun populateRequestValuesFromMap(
        requestValues: HttpRequestValues.Builder, nameToValue: Map<String, Any?>
    ) {
        nameToValue.forEach { (k: String, v: Any?) ->
            if (v == null) {
                return@forEach
            }
            val clz: Class<*> = v.javaClass
            if (BeanUtils.isSimpleValueType(clz)) {
                requestValues.addRequestParameter(k, v.toString())
            } else if (clz.isArray && BeanUtils.isSimpleValueType(clz.componentType)) {
                val arrValue = (v as Array<Any>)
                    .filter { obj: Any? -> Objects.nonNull(obj) }
                    .map { obj: Any -> obj.toString() }
                    .toTypedArray()
                if (arrValue.isNotEmpty()) {
                    requestValues.addRequestParameter(k, *arrValue)
                }
            } else if (v is Iterable<*>) {
                val values: MutableList<String> = ArrayList()
                v.forEach { item: Any? ->
                    if (item != null && BeanUtils.isSimpleValueType(item.javaClass)) {
                        values.add(item.toString())
                    }
                }
                if (values.isNotEmpty()) {
                    requestValues.addRequestParameter(k, *values.toTypedArray())
                }
            }
        }
    }

    /**
     * 判断是否为不可解析的参数
     */
    private fun isNonResolvableArgument(argument: Any?, parameter: MethodParameter): Boolean {
        // If there is @RequestParam, @PathVariable, @RequestHeader, @CookieValue, etc.,
        // we cannot convert Java bean to request parameters,
        // it will be resolved by other ArgumentResolver.
        return argument == null || argument is URI // UrlArgumentResolver
                || argument is HttpMethod // HttpMethodArgumentResolver
                || argument is UriBuilderFactory // UriBuilderFactoryArgumentResolver
                || argument is MultipartFile // RequestPartArgumentResolver
                || BeanUtils.isSimpleValueType(argument.javaClass)
                || hasWebBindPackageAnnotation(parameter)
    }

    /**
     * 判断参数是否包含Web绑定注解
     */
    private fun hasWebBindPackageAnnotation(parameter: MethodParameter): Boolean {
        for (annotation in parameter.parameterAnnotations) {
            if (annotation.annotationClass.java.packageName.startsWith(WEB_BIND_ANNOTATION_PACKAGE)) {
                return true
            }
        }
        return false
    }

    override fun getOrder(): Int {
        return ORDER
    }

    companion object {
        const val ORDER: Int = 0

        private val WEB_BIND_ANNOTATION_PACKAGE: String = RequestParam::class.java.packageName
    }
}
