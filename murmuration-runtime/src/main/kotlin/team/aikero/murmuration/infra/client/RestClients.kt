package team.aikero.murmuration.infra.client

import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpRequest
import org.springframework.http.HttpStatusCode
import org.springframework.http.client.ClientHttpRequestExecution
import org.springframework.http.client.ClientHttpRequestInterceptor
import org.springframework.http.client.ClientHttpResponse
import org.springframework.http.client.JettyClientHttpRequestFactory
import org.springframework.util.StopWatch
import org.springframework.web.client.RestClient
import org.springframework.web.client.support.RestClientAdapter
import org.springframework.web.service.invoker.HttpServiceProxyFactory
import java.io.ByteArrayInputStream
import java.io.InputStream
import java.nio.charset.StandardCharsets
import java.time.Duration

/**
 * Spring 原生的客户端接口实现
 *
 * [Docs](https://docs.spring.io/spring-framework/reference/integration/rest-clients.html#rest-http-interface)
 *
 * <AUTHOR>
 */
object RestClients {

    /**
     * 构造 RestClient 接口实例（带拦截器配置）
     */
    fun <T> build(
        clazz: Class<T>,
        url: String,
        connectTimeout: Long = 3,
        readTimeout: Long = 10,
        interceptorClasses: Array<Class<out ClientHttpRequestInterceptor>> = emptyArray()
    ): T {
        val jettyClientHttpRequestFactory = JettyClientHttpRequestFactory()
        jettyClientHttpRequestFactory.apply {
            setConnectTimeout(Duration.ofSeconds(connectTimeout))
            setReadTimeout(Duration.ofSeconds(readTimeout))
        }

        val restClientBuilder = RestClient.builder()
            .baseUrl(url)
            .requestFactory(jettyClientHttpRequestFactory)
            .requestInterceptor(ClientLoggerRequestInterceptor(clazz))

        // 添加自定义拦截器
        interceptorClasses
            .map { it.getDeclaredConstructor().newInstance() }
            .forEach(restClientBuilder::requestInterceptor)

        val restClient = restClientBuilder.build()
        val adapter = RestClientAdapter.create(restClient)
        val factory = HttpServiceProxyFactory.builderFor(adapter)
            .customArgumentResolver(ObjectRequestParamArgumentResolver())
            .build()

        return factory.createClient(clazz)
    }

    /**
     * 请求日志记录拦截器
     */
    class ClientLoggerRequestInterceptor<T>(clazz: Class<T>) : ClientHttpRequestInterceptor {
        private val log = KotlinLogging.logger { clazz.name }
        override fun intercept(
            request: HttpRequest, body: ByteArray,
            execution: ClientHttpRequestExecution
        ): ClientHttpResponse {

            logRequest(request, body)

            val watch = StopWatch()
            watch.start()
            val response = try {
                execution.execute(request, body)
            }finally {
                watch.stop()
            }

            return logResponse(response, watch)
        }

        private fun logRequest(request: HttpRequest, body: ByteArray?) {
            val requestBodyStr = if (body != null && body.isNotEmpty()) {
                String(body, StandardCharsets.UTF_8)
            } else {
                ""
            }

            val logParts = mutableListOf<String?>()
            logParts.add("> ${request.method.name()} ${request.uri}")
            logParts.add(request.headers.entries.joinToString("\n") { "> $it" })
            logParts.add(requestBodyStr)
            log.debug { "request " + logParts.filterNotNull().joinToString("\n") }
        }

        private fun logResponse(
            response: ClientHttpResponse,
            watch: StopWatch
        ): ClientHttpResponse {
            val responseBody: ByteArray = response.body.readAllBytes()
            val responseBodyStr = if (responseBody.isNotEmpty()) {
                String(responseBody, StandardCharsets.UTF_8)
            } else {
                ""
            }

            val logParts = mutableListOf<String?>()
            logParts.add("< ${watch.let { "${it.totalTimeMillis}ms " }}${response.statusCode}")
            logParts.add(response.headers.entries.joinToString("\n") { "< $it" })
            logParts.add(responseBodyStr)
            log.debug { "response " + logParts.filterNotNull().joinToString("\n") }

            return BufferingClientHttpResponseWrapper(response, responseBody)
        }

    }

    /**
     * 响应体缓存包装器
     */
    class BufferingClientHttpResponseWrapper(
        private val response: ClientHttpResponse,
        private val body: ByteArray
    ) : ClientHttpResponse {
        override fun getBody(): InputStream {
            return ByteArrayInputStream(body)
        }

        override fun close() {
            response.close()
        }

        override fun getStatusCode(): HttpStatusCode {
            return response.statusCode
        }

        override fun getStatusText(): String {
            return response.statusText
        }

        override fun getHeaders(): HttpHeaders {
            return response.headers
        }
    }


}
