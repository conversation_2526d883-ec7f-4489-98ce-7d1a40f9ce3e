package team.aikero.murmuration.infra.client

import org.springframework.beans.factory.annotation.AnnotatedBeanDefinition
import org.springframework.beans.factory.support.AbstractBeanDefinition
import org.springframework.beans.factory.support.BeanDefinitionBuilder
import org.springframework.beans.factory.support.BeanDefinitionRegistry
import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.context.EnvironmentAware
import org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider
import org.springframework.context.annotation.ImportBeanDefinitionRegistrar
import org.springframework.core.env.Environment
import org.springframework.core.type.AnnotationMetadata
import org.springframework.core.type.filter.AnnotationTypeFilter
import org.springframework.util.Assert
import org.springframework.util.ClassUtils
import org.springframework.util.StringUtils

/**
 * RestClient Bean 注册器
 *
 * 遵循  SpringBootApplication 配置的扫描规则
 *
 * <AUTHOR>
 */
open class RestClientBeanRegistrar(
    /**
     * 扫描包
     */
    val scanBasePackages: Set<String> = setOf("team.aikero.murmuration")
) : ImportBeanDefinitionRegistrar, EnvironmentAware {

    private lateinit var environment: Environment

    override fun registerBeanDefinitions(metadata: AnnotationMetadata, registry: BeanDefinitionRegistry) {
        val scanner = getScanner()
        scanner.addIncludeFilter(AnnotationTypeFilter(RestClient::class.java))
        val basePackages: Set<String> = getBasePackages(metadata)
        basePackages
            .map { scanner.findCandidateComponents(it) }
            .flatten()
            .forEach {
                if (it is AnnotatedBeanDefinition) {
                    // 必须是一个接口
                    val annotationMetadata: AnnotationMetadata = it.metadata
                    Assert.isTrue(annotationMetadata.isInterface, "@RestClient can only be specified on an interface")

                    // 注解属性不可能为空
                    val attributes = annotationMetadata.getAnnotationAttributes(RestClient::class.java.canonicalName)!!

                    val builder =
                        BeanDefinitionBuilder.genericBeanDefinition(RestClientFactoryBean::class.java)
                            // 指定注入方式
                            .setAutowireMode(AbstractBeanDefinition.AUTOWIRE_CONSTRUCTOR)
                            // 构造参数
                            .addConstructorArgValue(annotationMetadata.className)
                            .addConstructorArgValue(resolve(attributes["url"] as String))
                            .addConstructorArgValue(attributes["connectionTimeout"])
                            .addConstructorArgValue(attributes["readTimeout"])
                            .addConstructorArgValue(attributes["interceptor"])

                    registry.registerBeanDefinition(annotationMetadata.className, builder.beanDefinition)
                }
            }
    }

    /**
     * 获取扫描器
     */
    protected fun getScanner(): ClassPathScanningCandidateComponentProvider {
        return object : ClassPathScanningCandidateComponentProvider(false) {
            override fun isCandidateComponent(beanDefinition: AnnotatedBeanDefinition): Boolean {
                var isCandidate = false
                if (beanDefinition.metadata.isIndependent) {
                    if (!beanDefinition.metadata.isAnnotation) {
                        isCandidate = true
                    }
                }
                return isCandidate
            }
        }
    }

    /**
     * 获取扫描包路径
     */
    @Suppress("UNCHECKED_CAST")
    protected fun getBasePackages(importingClassMetadata: AnnotationMetadata): Set<String> {

        val attributes =
            importingClassMetadata.getAnnotationAttributes(SpringBootApplication::class.java.canonicalName)
                ?: return scanBasePackages

        val basePackages: MutableSet<String> = HashSet()

        for (pkg in (attributes["scanBasePackages"] as Array<String>)) {
            if (StringUtils.hasText(pkg)) {
                basePackages.add(pkg)
            }
        }
        for (clazz in (attributes["scanBasePackageClasses"] as Array<Class<*>>)) {
            basePackages.add(ClassUtils.getPackageName(clazz))
        }

        if (basePackages.isEmpty()) {
            basePackages.add(ClassUtils.getPackageName(importingClassMetadata.className))
        }
        return basePackages
    }

    private fun resolve(value: String): String {
        return this.environment.resolvePlaceholders(value)
    }

    override fun setEnvironment(environment: Environment) {
        this.environment = environment
    }
}
