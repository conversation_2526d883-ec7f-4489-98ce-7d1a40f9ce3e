package team.aikero.murmuration.infra.rocketmq

import com.fasterxml.jackson.databind.ObjectMapper
import org.apache.rocketmq.client.apis.producer.Producer
import org.apache.rocketmq.client.apis.message.MessageId
import org.apache.rocketmq.client.java.message.MessageBuilderImpl
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log

class RocketMQClient(
    val producer: Producer,
    val objectMapper: ObjectMapper,
) {

    fun send(message: Message<*>): MessageId {
        val serializedBody = objectMapper.writeValueAsBytes(message.payload)
        val builder = MessageBuilderImpl()
            .setTopic(message.topic)
            .setKeys(*message.keys.toTypedArray())
            .setBody(serializedBody)

        if (message.tag != null) {
            builder.setTag(message.tag)
        }

        val sendReceipt = producer.send(builder.build())
        val messageId = sendReceipt.messageId
        log.info { "发送MQ消息成功: $messageId" }
        return messageId
    }
}

data class Message<T>(
    val payload: T,
    val topic: String,
    val keys: Collection<String>,
    val tag: String? = null,
)
