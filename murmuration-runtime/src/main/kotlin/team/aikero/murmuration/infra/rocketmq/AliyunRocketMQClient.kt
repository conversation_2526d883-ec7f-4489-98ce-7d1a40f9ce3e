package team.aikero.murmuration.infra.rocketmq

import com.aliyun.rocketmq20220801.Client
import com.aliyun.rocketmq20220801.models.ListMessagesRequest
import com.aliyun.teaopenapi.models.Config

class AliyunRocketMQClient(
    val accessKey: String,
    val secretKey: String,
    val instanceId: String,
) {
    val client = Client(Config().apply {
        endpoint = "rocketmq.cn-hangzhou.aliyuncs.com"
        accessKeyId = accessKey
        accessKeySecret = secretKey
    })

    fun getMessageByKey(messageKey: String): String? {
        val request = ListMessagesRequest().apply {
            this.messageKey = messageKey
        }
        val listMessagesResponse = client.listMessages(instanceId, TopicHolder.topic, request)
        val messages = listMessagesResponse.body.data.list
        if (messages.isEmpty()) return null

        val messageId = messages[0].messageId
        val messageDetailResponse = client.getMessageDetail(instanceId, TopicHolder.topic, messageId)
        return messageDetailResponse.body.data.body
    }
}
