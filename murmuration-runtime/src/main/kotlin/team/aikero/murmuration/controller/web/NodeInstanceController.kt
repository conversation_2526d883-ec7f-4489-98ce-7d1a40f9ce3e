package team.aikero.murmuration.controller.web

import org.babyfish.jimmer.sql.ast.mutation.SaveMode
import org.babyfish.jimmer.sql.kt.KSqlClient
import org.babyfish.jimmer.sql.kt.ast.expression.eq
import org.babyfish.jimmer.sql.kt.exists
import org.babyfish.jimmer.sql.kt.fetcher.newFetcher
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.*
import team.aikero.blade.core.exception.BusinessException
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.ok
import team.aikero.murmuration.core.scheduler.Executor
import team.aikero.murmuration.core.workflow.dto.NodeInstanceDetailVo
import team.aikero.murmuration.core.workflow.entity.*
import team.aikero.murmuration.core.workflow.event.NodeWork
import team.aikero.murmuration.core.workflow.event.NodeWork.Companion.toWork

/**
 * 节点实例
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/web/node/instance")
class NodeInstanceController(
    val sqlClient: KSqlClient,
    val nodeExecutor: Executor<NodeWork>,
) {

    /**
     * 节点详情
     *
     * @param nodeInstanceId 节点实例ID
     */
    @GetMapping("/{nodeInstanceId}/detail")
    fun detail(@PathVariable nodeInstanceId: Long): DataResponse<NodeInstanceDetailVo> {
        return ok(sqlClient.findOneById(NodeInstanceDetailVo::class, nodeInstanceId))
    }


    /**
     * 节点验收通过
     *
     * @param nodeInstanceId 节点实例ID
     */
    @PostMapping("/{nodeInstanceId}/approve")
    @Transactional(rollbackFor = [Exception::class])
    fun approve(@PathVariable nodeInstanceId: Long): DataResponse<Unit> {
        // 重新入队
        val nodeInstance = sqlClient.findOne(newFetcher(NodeInstance::class).by {
            status()
            workflowInstance {
                status()
            }
            supplier()
            ability()
        }){
            where(table.id eq nodeInstanceId)
        }

        if (nodeInstance.workflowInstance?.status != WorkflowStatus.RUNNING){
            throw BusinessException("此节点实例不允许操作")
        }

        // 校验节点下是否存在已验收的任务
        val exists = sqlClient.exists(TaskInstanceResult::class) {
            where(table.task.nodeInstanceId eq nodeInstanceId)
            where(table.passed eq true)
            select(table.id)
        }

        if (!exists) {
            throw BusinessException("节点[${nodeInstanceId}]下不存在可用结果, 禁止提交")
        }

        if (nodeInstance.status != NodeStatus.REVIEWING) {
            throw BusinessException("节点[${nodeInstanceId}]当前状态[${nodeInstance.status}]，无法提交")
        }

        // 更新节点状态
        sqlClient.save(NodeInstance {
            this.id = nodeInstanceId
            this.status = NodeStatus.REVIEWED
        }, SaveMode.UPDATE_ONLY)

        // 直接执行
        nodeExecutor.execute(nodeInstance.toWork())

        return ok()
    }
}
