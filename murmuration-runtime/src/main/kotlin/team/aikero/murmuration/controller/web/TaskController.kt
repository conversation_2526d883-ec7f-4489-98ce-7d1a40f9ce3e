package team.aikero.murmuration.controller.web

import org.babyfish.jimmer.sql.kt.KSqlClient
import org.babyfish.jimmer.sql.kt.ast.expression.asc
import org.babyfish.jimmer.sql.kt.ast.expression.eq
import org.babyfish.jimmer.sql.kt.ast.expression.valueIn
import org.springframework.web.bind.annotation.*
import team.aikero.blade.core.protocol.ComplexPageParam
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.PageVo
import team.aikero.blade.core.protocol.ok
import team.aikero.blade.data.jimmer.orderBy
import team.aikero.blade.util.protocol.paging.toPageableOrDefaultSort
import team.aikero.murmuration.core.task.dto.TaskNodePageReq
import team.aikero.murmuration.core.task.dto.TaskPageVo
import team.aikero.murmuration.core.workflow.entity.*
import team.aikero.murmuration.core.workflow.task.TaskManager

/**
 * 节点任务
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/web/task")
class TaskController(val sqlClient: KSqlClient, val taskManager: TaskManager) {

    /**
     * 查询节点任务
     *
     * @param nodeInstanceId 节点实例ID
     * @param pageReq 分页请求
     */
    @PostMapping("/{nodeInstanceId}/page")
    fun nodePage(
        @PathVariable nodeInstanceId: Long,
        @RequestBody pageReq: ComplexPageParam<TaskNodePageReq>
    ): DataResponse<PageVo<TaskPageVo>> {
        val pageable = pageReq.toPageableOrDefaultSort()
        val specification = pageReq.filters

        val list = sqlClient.executeQuery(TaskInstance::class) {
            where(table.nodeInstanceId eq nodeInstanceId)
            orderBy(table.id.asc())
            orderBy(pageable)
            select(table.fetch(TaskPageVo::class))
        }

        // 根据 groupIndex 分组
        var groups = list.groupBy { it.groupIndex }
            .mapValues { (_, tasks) ->
                tasks.first().copy(
                    status = aggregateGroupStatus(tasks.map { it.status }),
                    results = tasks.flatMap { it.results }
                )
            }.values

        // 筛选
        if (specification.status != null) {
            groups = groups.filter { it.status == specification.status }
        }

        // 内存分页
        val page = groups
            .chunked(pageReq.pageSize)
            .getOrNull(pageReq.pageNum - 1) ?: emptyList()


        return ok(PageVo(pageReq.pageNum, groups.size, page))
    }

    /**
     * 查询节点下任务结果的可用数与不可用数
     *
     * @param nodeInstanceId 节点实例ID
     */
    @GetMapping("/{nodeInstanceId}/result-count")
    fun resultCount(@PathVariable nodeInstanceId: Long): DataResponse<ResultCount> {
        val counts = sqlClient.executeQuery(TaskInstanceResult::class) {
            where(table.task.nodeInstanceId eq nodeInstanceId)
            select(table.fetchBy {
                passed()
            })
        }.groupBy { it.passed }.mapValues { it.value.size }

        val result = ResultCount(
            available = counts.getOrDefault(true, 0),
            unavailable = counts.getOrDefault(false, 0),
        )
        return ok(result)
    }


    /**
     * 接受任务结果
     *
     * 将一批任务 passed 状态更新为 true
     *
     * @param resultIds 任务结果ID
     */
    @PostMapping("/result/accept")
    fun accept(@RequestBody resultIds: List<Long>): DataResponse<Int> {
        val update = sqlClient.executeUpdate(TaskInstanceResult::class) {
            where(table.id valueIn resultIds)
            set(table.passed, true)
        }
        return ok(update)
    }

    /**
     * 将节点下的任务全量接受
     *
     * @param nodeInstanceId 节点实例ID
     */
    @PostMapping("/{nodeInstanceId}/accept-all")
    fun acceptAll(@PathVariable nodeInstanceId: Long): DataResponse<Int> {
        val update = sqlClient.executeUpdate(TaskInstanceResult::class) {
            where(table.task.nodeInstanceId eq nodeInstanceId)
            set(table.passed, true)
        }
        return ok(update)
    }

    /**
     * 拒绝任务结果
     *
     * 将一批任务 passed 状态更新为 false
     *
     * @param resultIds 任务结果ID
     */
    @PostMapping("/result/reject")
    fun reject(@RequestBody resultIds: List<Long>): DataResponse<Int> {
        val update = sqlClient.executeUpdate(TaskInstanceResult::class) {
            where(table.id valueIn resultIds)
            set(table.passed, false)
        }
        return ok(update)
    }

    /**
     * 将节点下的任务全量拒绝
     *
     * @param nodeInstanceId 节点实例ID
     */
    @PostMapping("/{nodeInstanceId}/reject-all")
    fun rejectAll(@PathVariable nodeInstanceId: Long): DataResponse<Int> {
        val update = sqlClient.executeUpdate(TaskInstanceResult::class) {
            where(table.task.nodeInstanceId eq nodeInstanceId)
            set(table.passed, false)
        }
        return ok(update)
    }

    /**
     * 重试任务
     *
     * @param taskIds 任务ID
     */
    @PostMapping("/retry")
    fun retry(@RequestBody taskIds: List<Long>): DataResponse<Unit> {
        taskIds.forEach(taskManager::retryTask)
        return ok()
    }
}

/**
 * 聚合分组任务状态
 *
 * 根据以下规则聚合分组中所有任务的状态：
 * 1. 当分组中存在 RUNNING 状态时返回 RUNNING
 * 2. 当分组中全部 FAILED 时，返回 FAILED
 * 3. 当分组中全部 CANCELLED 时，返回 CANCELLED
 * 4. 当分组中均为 Finished 状态时，存在 COMPLETED 则返回 COMPLETED，否则返回最后一个状态
 *
 * @param statuses 分组中所有任务的状态列表
 * @return 聚合后的状态
 */
private fun aggregateGroupStatus(statuses: List<TaskStatus>): TaskStatus {
    if (statuses.isEmpty()) {
        throw IllegalArgumentException("状态列表不能为空")
    }

    // 1. 如果有任何RUNNING状态，返回RUNNING
    if (statuses.any { it == TaskStatus.RUNNING }) {
        return TaskStatus.RUNNING
    }

    // 2. 如果全部是FAILED，返回FAILED
    if (statuses.all { it == TaskStatus.FAILED }) {
        return TaskStatus.FAILED
    }

    // 3. 如果全部是CANCELLED，返回CANCELLED
    if (statuses.all { it == TaskStatus.CANCELLED }) {
        return TaskStatus.CANCELLED
    }

    // 4. 如果全部是Finished状态
    if (statuses.all { it.isFinished() }) {
        // 如果有COMPLETED，返回COMPLETED
        if (statuses.any { it == TaskStatus.COMPLETED }) {
            return TaskStatus.COMPLETED
        }
        // 否则返回最后一个状态
        return statuses.last()
    }

    // 其他情况，返回第一个状态
    return statuses.first()
}

/**
 * 任务结果计数
 */
data class ResultCount(
    /**
     * 可用数量
     */
    val available: Int,
    /**
     * 不可用数量
     */
    val unavailable: Int,
)
