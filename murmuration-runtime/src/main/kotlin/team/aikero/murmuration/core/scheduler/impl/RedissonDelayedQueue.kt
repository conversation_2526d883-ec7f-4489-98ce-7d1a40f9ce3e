package team.aikero.murmuration.core.scheduler.impl

import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.future.await
import org.redisson.RedissonShutdownException
import org.redisson.api.RedissonClient
import team.aikero.murmuration.core.scheduler.DelayedQueue
import java.util.concurrent.CompletionStage
import java.util.concurrent.TimeUnit
import kotlin.time.Duration

/**
 * 基于 Redisson 的延迟队列实现
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
class RedissonDelayedQueue<T>(
    /**
     * 队列名称
     */
    name: String,

    /**
     * Redisson客户端
     */
    redisson: RedissonClient,
): DelayedQueue<T> {

    /**
     * 阻塞队列
     */
    private val blockingQueue = redisson.getBlockingQueue<T>(name)

    /**
     * 延迟队列
     */
    private val delayedQueue = redisson.getDelayedQueue(blockingQueue)

    /**
     * 提交到队列
     */
    override suspend fun offer(payload: T, delay: Duration) {
        executeAsyncOperation {
            delayedQueue.offerAsync(payload, delay.inWholeMilliseconds, TimeUnit.MILLISECONDS)
        }
    }

    /**
     * 从队列中取出
     */
    override suspend fun take(): T? {
        return executeAsyncOperation {
            blockingQueue.pollAsync(1, TimeUnit.SECONDS)
        }
    }

    /**
     * 读取队列中的所有元素
     */
    override suspend fun readAll(): List<T> {
        return executeAsyncOperation(delayedQueue::readAllAsync)
    }

    /**
     * 执行异步操作
     */
    private suspend fun <R> executeAsyncOperation(block: () -> CompletionStage<R>): R {
        return try {
            block().toCompletableFuture().await()
        } catch (e: RedissonShutdownException) {
            throw CancellationException(e.message, e)
        }
    }
}
