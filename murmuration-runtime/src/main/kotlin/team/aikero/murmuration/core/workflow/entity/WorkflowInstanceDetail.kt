package team.aikero.murmuration.core.workflow.entity

import org.babyfish.jimmer.sql.*
import team.aikero.blade.data.jimmer.entity.LongId
import team.aikero.murmuration.controller.web.NodeDefinitionArgs

/**
 * 工作流实例
 *
 * <AUTHOR>
 */
@Entity
interface WorkflowInstanceDetail: LongId  {

    /**
     * 工作流定义
     */
    @Key
    @OneToOne(inputNotNull = true)
    val workflowInstance: WorkflowInstance?

    /**
     * 工作流输入
     *
     * 冗余记录，便于复制、重试场景
     */
    @Serialized
    @Default("{}")
    val input: Map<String, Any?>

    /**
     * 工作流参数
     *
     * 冗余记录，便于复制、重试场景
     */
    @Serialized
    @Default("{}")
    val parameter: Map<String, Any?>

    /**
     * 节点输入参数
     *
     * 冗余记录，便于复制、重试场景
     */
    @Serialized
    @Default("[]")
    val nodeArgs: List<NodeDefinitionArgs>

    /**
     * 工作流存储
     */
    @Serialized
    @Default("{}")
    val storage: MutableMap<String, String>
}
