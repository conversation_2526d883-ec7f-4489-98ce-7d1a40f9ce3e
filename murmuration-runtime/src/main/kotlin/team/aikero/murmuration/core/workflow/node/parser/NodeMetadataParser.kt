package team.aikero.murmuration.core.workflow.node.parser

import org.slf4j.LoggerFactory
import org.springframework.core.GenericTypeResolver
import team.aikero.murmuration.core.workflow.WorkflowNode
import team.aikero.murmuration.core.workflow.entity.Variable
import team.aikero.murmuration.core.workflow.entity.VariableGroup
import team.aikero.murmuration.core.workflow.NodeType
import team.aikero.murmuration.core.workflow.node.param.Input
import team.aikero.murmuration.core.workflow.node.param.Output
import team.aikero.murmuration.core.workflow.node.param.Parameter
import kotlin.reflect.KClass

/**
 * 节点元数据解析器
 *
 * 负责解析 NodeWork 实现类的元数据，包括泛型参数的递归解析
 *
 * <AUTHOR>
 */
object NodeMetadataParser {

    private val logger = LoggerFactory.getLogger(NodeMetadataParser::class.java)

    /**
     * 解析节点元数据
     *
     * @param nodeInstance 节点实例
     * @param nodeClass 节点类
     * @param nodeIdentifier 节点标识符注解
     * @return 解析后的节点元数据
     */
    fun parseNodeMetadata(
        nodeInstance: WorkflowNode<Input, Parameter, Output>,
        nodeClass: KClass<*>
    ): ParsedNodeMetadata {
        logger.debug("开始解析节点元数据: ${nodeClass.qualifiedName}")

        try {
            // 解析泛型参数
            val genericTypes = resolveGenericTypes(nodeInstance, nodeClass)

            // 解析输入参数
            val inputVariables = parseGenericParameter(genericTypes.inputType, VariableGroup.INPUT)

            // 解析节点参数
            val parameterVariables = parseGenericParameter(genericTypes.parameterType, VariableGroup.PARAMETER)

            // 解析输出参数
            val outputVariables = parseGenericParameter(genericTypes.outputType, VariableGroup.OUTPUT)

            return ParsedNodeMetadata(
                type = nodeInstance.type,
                className = nodeClass.qualifiedName ?: nodeClass.toString(),
                inputVariables = inputVariables,
                parameterVariables = parameterVariables,
                outputVariables = outputVariables
            )
        } catch (e: Exception) {
            logger.error("解析节点元数据失败: ${nodeClass.qualifiedName}", e)
            throw NodeMetadataParseException("解析节点元数据失败: ${nodeClass.qualifiedName}", e)
        }
    }

    /**
     * 解析节点的泛型类型
     */
    private fun resolveGenericTypes(
        nodeInstance: WorkflowNode<Input, Parameter, Output>,
        nodeClass: KClass<*>
    ): GenericTypes {
        try {
            val typeArguments = GenericTypeResolver.resolveTypeArguments(
                nodeClass.java,
                WorkflowNode::class.java
            )

            if (typeArguments == null || typeArguments.size < 3) {
                logger.warn("无法解析节点 ${nodeClass.qualifiedName} 的泛型参数，使用默认类型")
                return GenericTypes(
                    inputType = Input::class,
                    parameterType = Parameter::class,
                    outputType = Output::class
                )
            }

            return GenericTypes(
                inputType = typeArguments[0].kotlin,
                parameterType = typeArguments[1].kotlin,
                outputType = typeArguments[2].kotlin
            )
        } catch (e: Exception) {
            logger.warn("解析泛型类型失败，使用默认类型: ${nodeClass.qualifiedName}", e)
            return GenericTypes(
                inputType = Input::class,
                parameterType = Parameter::class,
                outputType = Output::class
            )
        }
    }

    /**
     * 解析泛型参数为变量列表
     */
    private fun parseGenericParameter(parameterType: KClass<*>, group: VariableGroup): List<Variable> {
        return try {
            // 如果是基础接口类型，返回空列表
            if (isBaseInterface(parameterType)) {
                return emptyList()
            }

            // 分析字段
            val fieldInfos = FieldAnalyzer.analyzeFields(parameterType)

            // 转换为变量
            val variables = mutableListOf<Variable>()
            fieldInfos.forEach { fieldInfo ->
                variables.addAll(VariableConverter.expandComplexType(fieldInfo, group))
            }

            variables
        } catch (e: Exception) {
            logger.warn("解析泛型参数 ${parameterType.qualifiedName} 失败", e)
            emptyList()
        }
    }

    /**
     * 判断是否为基础接口类型
     */
    private fun isBaseInterface(type: KClass<*>): Boolean {
        val qualifiedName = type
        return qualifiedName in setOf(
            Input::class,
            Parameter::class,
            Output::class,
            Parameter.Empty::class,
        )
    }
}

/**
 * 泛型类型信息
 */
data class GenericTypes(
    val inputType: KClass<*>,
    val parameterType: KClass<*>,
    val outputType: KClass<*>
)

/**
 * 解析后的节点元数据
 */
data class ParsedNodeMetadata(
    val type: NodeType,
    val className: String,
    val inputVariables: List<Variable>,
    val parameterVariables: List<Variable>,
    val outputVariables: List<Variable>
)

/**
 * 节点元数据解析异常
 */
class NodeMetadataParseException(message: String, cause: Throwable? = null) : Exception(message, cause)
