package team.aikero.murmuration.core.workflow.node.builtin

import team.aikero.murmuration.core.Ability
import team.aikero.murmuration.core.Supplier
import team.aikero.murmuration.core.annotations.NodeIdentifier
import team.aikero.murmuration.core.workflow.WorkflowNode
import team.aikero.murmuration.core.workflow.context.WorkflowNodeContext
import team.aikero.murmuration.core.workflow.node.NodeResult
import team.aikero.murmuration.core.workflow.NodeType
import team.aikero.murmuration.core.workflow.node.param.GenericData
import team.aikero.murmuration.core.workflow.node.param.Parameter

/**
 * 结束节点
 *
 * <AUTHOR>
 */
@NodeIdentifier(name = "结束节点", supplier = Supplier.MURMURATION, ability = Ability.END)
class EndNode : WorkflowNode<GenericData, Parameter.Empty, GenericData> {
    /**
     * 节点类型
     */
    override val type: NodeType = NodeType.END

    /**
     * 执行任务动作
     *
     * @param context 工作流上下文 发起系统、用户信息、业务 id 等
     * @param input 输入
     * @param parameter 参数
     * @return 节点执行结果
     */
    override fun execute(
        context: WorkflowNodeContext,
        input: GenericData,
        parameter: Parameter.Empty
    ): NodeResult<GenericData> {
        // 在元数据中记录数据流转信息
        val enhancedData = input
            .set("endTime", System.currentTimeMillis())

        return NodeResult.Completed(enhancedData)
    }
}
