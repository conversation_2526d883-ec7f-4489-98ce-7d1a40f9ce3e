package team.aikero.murmuration.core.workflow

/**
 * 节点类型
 */
enum class NodeType {
    /**
     * 开始节点
     */
    START,

    /**
     * 逻辑节点
     *
     * 数据加载、抽取、转换节点
     */
    LOGIC,

    /**
     * 普通节点
     *
     * 执行时间短，可快速执行
     */
    NORMAL,

    /**
     * 任务节点
     *
     * 多步交互，执行时间长
     */
    TASK,

    /**
     * 子工作流节点
     *
     * 执行一个子工作流，执行时间长
     */
    SUBFLOW,

    /**
     * 结束节点
     *
     * 结束工作流，输出结果
     */
    END,
}
