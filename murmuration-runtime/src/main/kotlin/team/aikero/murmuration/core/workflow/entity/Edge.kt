package team.aikero.murmuration.core.workflow.entity

import org.babyfish.jimmer.sql.Entity
import org.babyfish.jimmer.sql.Key
import org.babyfish.jimmer.sql.ManyToOne
import team.aikero.blade.data.jimmer.entity.LongId

/**
 * 工作流连接
 *
 * <AUTHOR>
 */
@Entity
interface Edge : LongId {

    /**
     * 工作流定义
     */
    @Key
    @ManyToOne
    val workflowDefinition: WorkflowDefinition?

    /**
     * 源节点key
     */
    @Key
    val sourceNodeKey: String

    /**
     * 目标节点key
     */
    @Key
    val targetNodeKey: String

}
