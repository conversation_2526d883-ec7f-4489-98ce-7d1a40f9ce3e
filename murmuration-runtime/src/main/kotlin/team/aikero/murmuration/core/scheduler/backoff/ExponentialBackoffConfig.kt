package team.aikero.murmuration.core.scheduler.backoff

import java.math.BigDecimal
import java.time.Duration

/**
 * 指数退避配置
 */
data class ExponentialBackoffConfig(
    /**
     * 底数
     */
    var base: BigDecimal,

    /**
     * 初始退避时间
     */
    var initialDelay: Duration,

    /**
     * 最大退避时间
     */
    var maxDelay: Duration,

    /**
     * 抖动策略
     */
    var jitterPolicy: JitterPolicy,
)
