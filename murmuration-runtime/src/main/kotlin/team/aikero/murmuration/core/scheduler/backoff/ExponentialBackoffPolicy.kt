package team.aikero.murmuration.core.scheduler.backoff

import java.math.BigDecimal

private val MAX_LONG = BigDecimal(Long.MAX_VALUE)

/**
 * 指数退避策略
 *
 * @param base 底数
 * @param initialDelay 初始退避时间(毫秒)
 * @param maxDelay 最大退避时间(毫秒)
 */
class ExponentialBackoffPolicy(
    val base: BigDecimal,
    val initialDelay: BigDecimal,
    val maxDelay: BigDecimal,
    val jitterPolicy: JitterPolicy,
): BackoffPolicy {

    init {
        check(initialDelay > BigDecimal.ZERO) { "initialDelay must be greater than zero" }
        check(maxDelay >= initialDelay) { "maxDelay must be greater than initialDelay" }
        check(maxDelay <= MAX_LONG) { "maxDelay must be less than Long.MAX_VALUE" }
    }

    override fun next(times: Int): Long {
        check(times >= 0) { "times must be grater than or equal to zero" }

        var result = initialDelay * base.pow(times)
        if (result > maxDelay) {
            result = maxDelay
        }

        return jitterPolicy.apply(result.toLong())
    }
}
