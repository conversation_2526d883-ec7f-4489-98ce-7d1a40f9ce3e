package team.aikero.murmuration.core.workflow.event

import team.aikero.murmuration.core.scheduler.Work
import team.aikero.murmuration.core.workflow.entity.WorkflowInstance
import team.aikero.murmuration.core.workflow.entity.WorkflowMode

/**
 * 工作流工作单元
 *
 * <AUTHOR>
 */
data class WorkflowWork(
    /**
     * 工作流实例ID
     */
    override val id: Long,

    /**
     * 工作流模式
     */
    val mode: WorkflowMode,
) : Work {
    companion object {

        /**
         * 从节点实例创建工作单元
         */
        fun from(nodeInstance: WorkflowInstance) = WorkflowWork(nodeInstance.id, nodeInstance.mode)

        /**
         * 从节点实例创建工作单元
         */
        fun WorkflowInstance.toWork() = from(this)
    }
}
