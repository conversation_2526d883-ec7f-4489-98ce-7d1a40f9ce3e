package team.aikero.murmuration.core.workflow.engine.expression

import org.springframework.context.expression.MapAccessor
import org.springframework.expression.*
import org.springframework.expression.Operation.ADD
import org.springframework.expression.Operation.SUBTRACT
import org.springframework.expression.spel.standard.SpelExpressionParser
import org.springframework.expression.spel.support.StandardEvaluationContext
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.murmuration.core.workflow.context.WorkflowNodeContext
import team.aikero.murmuration.core.workflow.entity.NodeStatus
import java.util.concurrent.ConcurrentHashMap

/**
 * 工作流表达式计算器
 *
 * 基于 Spring Expression Language (SpEL) 实现
 *
 * 支持的表达式语法：
 * - #{nodeKey.fieldName} - 引用其他节点输出
 * - #{nodeKey.images[0].url} - 支持数组和复杂对象访问
 * - #{nodeKey.count > 0 ? nodeKey.images[0].url : 'default.jpg'} - 支持条件表达式
 * - #{nodeKey1.list} + #{nodeKey2.list} - 支持集合相加运算
 *
 * <AUTHOR>
 */
class WorkflowExpressionEvaluator {

    private val parser: ExpressionParser = SpelExpressionParser()
    private val expressionCache = ConcurrentHashMap<String, Expression>()

    /**
     * 计算表达式
     *
     * @param expression 表达式字符串
     * @param context 工作流节点上下文
     * @return 计算结果
     */
    fun evaluate(expression: String, context: WorkflowNodeContext): Any? {
        return try {
            // 检查是否包含表达式片段
            if (!containsExpressions(expression)) {
                return expression
            }

            // 简化处理：将复合表达式转换为单个SpEL表达式
            val normalizedExpression = normalizeExpression(expression)
            val spelExpression = getOrParseExpression(normalizedExpression)
            val evaluationContext = buildEvaluationContext(context)

            spelExpression.getValue(evaluationContext)
        } catch (e: Exception) {
            log.warn(e) { "表达式计算失败: $expression, 错误: ${e.message}" }
            expression
        }
    }

    /**
     * 检查字符串是否包含表达式片段
     *
     * @param value 字符串值
     * @return 是否包含表达式
     */
    private fun containsExpressions(value: String): Boolean {
        return value.contains("#{") && value.contains("}")
    }

    /**
     * 标准化表达式：将复合表达式转换为单个SpEL表达式
     *
     * 例如：#{开始.images} + #{开始.images} -> #{开始.images + 开始.images}
     *
     * @param expression 原始表达式
     * @return 标准化后的表达式
     */
    private fun normalizeExpression(expression: String): String {
        // 如果已经是简单表达式，直接返回
        if (isSimpleExpression(expression)) {
            return expression
        }

        // 使用正则表达式替换所有的 #{...} 为其内容
        val pattern = Regex("""#\{([^}]+)\}""")
        val result = pattern.replace(expression) { matchResult ->
            matchResult.groupValues[1] // 返回 {} 内的内容
        }

        // 如果替换后的结果与原表达式不同，说明有表达式片段被替换了
        return if (result != expression) {
            "#{$result}"
        } else {
            expression
        }
    }

    /**
     * 检查是否为简单的单个表达式
     *
     * @param value 字符串值
     * @return 是否为简单表达式
     */
    private fun isSimpleExpression(value: String): Boolean {
        if (value.length < 4 || !value.startsWith("#{") || !value.endsWith("}")) {
            return false
        }

        // 检查是否只包含一个表达式片段
        val pattern = Regex("""#\{([^}]+)\}""")
        val matches = pattern.findAll(value).toList()

        // 只有一个匹配，且匹配的范围覆盖整个字符串
        return matches.size == 1 && matches[0].range.first == 0 && matches[0].range.last == value.length - 1
    }


    /**
     * 获取或解析表达式（带缓存）
     *
     * @param expressionString 表达式字符串
     * @return 解析后的表达式
     */
    private fun getOrParseExpression(expressionString: String): Expression {
        return expressionCache.computeIfAbsent(expressionString) { expr ->
            try {
                val cleanExpression = expr.substring(2, expr.length - 1)
                parser.parseExpression(cleanExpression)
            } catch (e: Exception) {
                log.error { "表达式解析失败: $expr, 错误: ${e.message}" }
                throw IllegalArgumentException("无效的表达式格式: $expr", e)
            }
        }
    }

    /**
     * 构建 SpEL 计算上下文
     *
     * @param context 工作流节点上下文
     * @return SpEL 计算上下文
     */
    private fun buildEvaluationContext(context: WorkflowNodeContext): EvaluationContext {
        val evaluationContext = StandardEvaluationContext()
        // 简化 map 访问路径
        evaluationContext.propertyAccessors.add(MapAccessor())
        evaluationContext.operatorOverloader = ListOperatorOverloader()

        // 构建基础上下文数据
        val contextData = mutableMapOf<String, Any?>()

        // 其他节点输出数据
        contextData.putAll(createNodeOutputsContext(context))

        // 设置根对象为包装器以支持属性访问
        evaluationContext.setRootObject(contextData)

        // 同时也设置为变量以支持两种访问方式
        contextData.forEach { (key, value) ->
            evaluationContext.setVariable(key, value)
        }

        return evaluationContext
    }

    /**
     * 创建其他节点输出的上下文
     * 支持简化访问路径: nodeKey.fieldName
     *
     * @param context 工作流节点上下文
     * @return 节点输出上下文映射
     */
    private fun createNodeOutputsContext(context: WorkflowNodeContext): Map<String, Any?> = try {
        context.workflowContext.workflowInstance.nodeInstances
            // 排除当前节点和未完成节点
            .filter { it.id != context.node.id && it.status == NodeStatus.COMPLETED }
            .associate { node ->
                // 支持 nodeKey.xxx 的访问
                node.nodeDefinition.nodeKey to node.detail?.output
            }

    } catch (e: Exception) {
        log.warn { "创建节点输出上下文失败: ${e.message}" }
        emptyMap()
    }


}


/**
 * 一个自定义的 SpEL 操作符重载器
 *
 * <AUTHOR>
 */
class ListOperatorOverloader : OperatorOverloader {
    /**
     * 判断是否需要重载给定的操作。
     *
     * @param operation 要检查的操作
     * @param leftOperand 左操作数
     * @param rightOperand 右操作数
     * @return 如果我们要处理这个操作，则返回 true
     */
    override fun overridesOperation(operation: Operation, leftOperand: Any?, rightOperand: Any?): Boolean {
        if (leftOperand is Collection<*> && rightOperand is Collection<*>) {
            when (operation) {
                ADD, SUBTRACT -> {
                    return true
                }

                else -> false
            }
        }

        return false
    }

    /**
     * 执行重载的操作。
     *
     * 这个方法只有在 overridesOperation() 返回 true 时才会被调用。
     *
     * @param operation 要执行的操作
     * @param leftOperand 左操作数
     * @param rightOperand 右操作数
     * @return 操作结果
     */
    override fun operate(operation: Operation, leftOperand: Any?, rightOperand: Any?): Any {
        // 因为 overridesOperation 已经做了检查，这里我们直接进行类型转换
        val left = leftOperand as Collection<*>
        val right = rightOperand as Collection<*>

        return when (operation) {
            ADD -> {
                left + right
            }

            SUBTRACT -> {
                left - right
            }

            else -> throw UnsupportedOperationException("Unsupported operation: $operation")
        }

    }
}
