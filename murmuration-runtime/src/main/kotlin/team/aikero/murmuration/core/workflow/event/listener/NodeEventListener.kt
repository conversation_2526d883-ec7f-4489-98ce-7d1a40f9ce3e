package team.aikero.murmuration.core.workflow.event.listener

import org.babyfish.jimmer.sql.kt.KSqlClient
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Component
import team.aikero.murmuration.core.workflow.entity.NodeInstanceEvent
import team.aikero.murmuration.core.workflow.event.NodeEvent

/**
 * 节点事件监听器
 *
 * <AUTHOR>
 * @since 2025-07-20
 */
@Component
class NodeEventListener(val sql: KSqlClient) {

    @EventListener
    fun onEvent(nodeEvent: NodeEvent) {
        // 记录节点事件
        sql.insert(NodeInstanceEvent {
            nodeInstanceId = nodeEvent.node.id
            status = nodeEvent.status
        })
    }
}
