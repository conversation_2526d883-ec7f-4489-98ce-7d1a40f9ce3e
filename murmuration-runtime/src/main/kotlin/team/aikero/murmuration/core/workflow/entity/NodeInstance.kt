package team.aikero.murmuration.core.workflow.entity

import org.babyfish.jimmer.sql.*
import team.aikero.blade.data.jimmer.entity.BaseEntity
import team.aikero.murmuration.common.annotation.EnumComment
import team.aikero.murmuration.core.Ability
import team.aikero.murmuration.core.Supplier

/**
 * 节点实例
 *
 * <AUTHOR>
 */
@Entity
interface NodeInstance: BaseEntity {
    /**
     * 节点定义
     */
    @ManyToOne
    val nodeDefinition: NodeDefinition

    /**
     * 节点定义ID
     */
    @IdView("nodeDefinition")
    val nodeDefinitionId: Long

    /**
     * 工作流实例
     */
    @ManyToOne(inputNotNull = true)
    val workflowInstance: WorkflowInstance?

    /**
     * 供应商
     */
    val supplier: Supplier

    /**
     * 能力
     */
    val ability: Ability

    /**
     * 节点状态
     */
    val status: NodeStatus

    /**
     * 节点详情
     */
    @OneToOne(mappedBy = "nodeInstance")
    val detail: NodeInstanceDetail?

    /**
     * 任务实例
     */
    @OneToMany(mappedBy = "nodeInstance")
    val taskInstances: List<TaskInstance>

    /**
     * 任务历史
     */
    @OneToMany(mappedBy = "nodeInstance")
    val histories: List<NodeInstanceHistory>
}

/**
 * 节点状态
 *
 * <AUTHOR>
 */
@EnumComment
enum class NodeStatus {
    /**
     * 准备中
     */
    PREPARE,

    /**
     * 就绪
     */
    READY,

    /**
     * 运行中
     */
    RUNNING,

    /**
     * 复核中
     */
    REVIEWING,

    /**
     * 已复核
     */
    REVIEWED,

    /**
     * 完成
     */
    COMPLETED,

    /**
     * 失败
     */
    FAILED,


}
