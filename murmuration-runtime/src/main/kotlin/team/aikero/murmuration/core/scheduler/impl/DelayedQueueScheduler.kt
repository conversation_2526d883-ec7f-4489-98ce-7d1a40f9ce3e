package team.aikero.murmuration.core.scheduler.impl

import io.opentelemetry.api.GlobalOpenTelemetry
import io.opentelemetry.context.Context
import io.opentelemetry.context.propagation.TextMapGetter
import io.opentelemetry.extension.kotlin.asContextElement
import jakarta.annotation.PostConstruct
import jakarta.annotation.PreDestroy
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import org.redisson.api.RedissonClient
import org.slf4j.MDC
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.murmuration.core.scheduler.backoff.BackoffPolicy
import team.aikero.murmuration.core.scheduler.DelayedQueue
import team.aikero.murmuration.core.scheduler.Executor
import team.aikero.murmuration.core.scheduler.Scheduler
import team.aikero.murmuration.core.scheduler.Work
import team.aikero.murmuration.core.scheduler.WorkStatus
import team.aikero.murmuration.core.workflow.event.NodeWork
import team.aikero.murmuration.core.workflow.event.TaskWork
import java.time.Instant
import java.time.ZoneId
import kotlin.time.Duration.Companion.milliseconds

/**
 * 基于延迟队列实现的调度器
 */
class DelayedQueueScheduler<T: Work>(
    /**
     * 调度器名称
     */
    private val name: String,

    /**
     * 延迟队列
     */
    private val queue: DelayedQueue<DelayedTask<T>>,

    /**
     * 工作协程数量
     */
    private val workerCount: Int,

    /**
     * Redisson 客户端
     */
    private val redisson: RedissonClient,

    /**
     * 执行器
     */
    private val executor: Executor<T>,

    /**
     * 退避策略
     */
    private val backoffPolicy: BackoffPolicy,

    /**
     * 最大退避次数
     */
    private val maxBackoffTimes: Int,
): Scheduler<T> {

    /**
     * 协程作用域
     *
     * 内部协程作用域，与调度器的生命周期绑定
     * 使用 SupervisorJob，一个子协程的失败不会影响其他协程
     */
    private val scope = CoroutineScope(SupervisorJob() + Dispatchers.IO)

    /**
     * OpenTelemetry
     */
    private val openTelemetry = GlobalOpenTelemetry.get()

    /**
     * 提交一个工作
     *
     * @param work 工作
     */
    override fun submit(work: T) = withMDC(work) {
        enqueue(work, 0)
    }

    /**
     * 启动调度器
     */
    @PostConstruct
    override fun start() {
        check(workerCount > 0) { "[$name] workerCount must be greater than zero" }
        repeat(workerCount) {
            startWorker()
        }
        log.info { "[$name] Started with $workerCount workers" }
    }

    /**
     * 启动工作协程
     */
    private fun startWorker() = scope.launch {
        while (isActive) {
            try {
                // 从队列中取出任务
                val task = queue.take() ?: continue
                val (work, times, traceContext) = task

                // 提取 OpenTelemetry 上下文
                val extractedContext = extractOpenTelemetryContext(traceContext)

                // 传播上下文
                extractedContext.makeCurrent().use {
                    withMDC(work) {
                        // 执行任务
                        log.debug { "[$name] take work[${work.id}] $times times from queue: $work" }

                        try {
                            executeTask(task)
                        }
                        catch (ex: Exception) {
                            log.error(ex) { "[$name] execute work[${work.id}] failed" }
                        }
                    }
                }
            }
            catch (_: CancellationException) {
                log.warn { "[$name] coroutine cancelled" }
                break
            }
            catch (ex: Exception) {
                log.error(ex) { "[$name] occurred an unknown error" }
            }
        }
    }

    /**
     * 执行任务
     */
    private fun executeTask(task: DelayedTask<T>) {
        // 执行任务
        val (work, times) = task
        val status = trigger(work)

        // 执行器期望重试
        if (status == WorkStatus.REDO) {
            // 已到达最大退避次数，不再重试
            if (times > maxBackoffTimes) {
                log.debug { "[$name] work[${work.id}] has reached max backoff times: $times" }
                executor.notifyTimeout(work)
            }
            // 计算下一次执行时间，重新入队
            else {
                enqueue(work, times + 1)
            }
        }
    }

    /**
     * 触发一个工作
     */
    override fun trigger(work: T): WorkStatus {
        // 分布式锁控制同一时间工作只能被触发一次
        val lockKey = "DISTRIBUTED_LOCK:${work::class.simpleName}:${work.id}"
        val lock = redisson.getLock(lockKey)
        if (!lock.tryLock()) return WorkStatus.FINISHED

        return try {
            executor.execute(work)
        }
        catch (ex: Exception) {
            executor.notifyFailed(work, ex)
            throw ex
        }
        finally {
            lock.unlock()
        }
    }

    /**
     * 列出所有工作
     */
    override fun listAll(): List<T> = runBlocking {
        queue.readAll().map { it.work }
    }

    /**
     * 入队
     */
    private fun enqueue(work: T, times: Int) = scope.launch(Context.current().asContextElement()) {
        // 计算下一次执行时间
        val delay = backoffPolicy.next(times)
        val duration = delay.milliseconds
        log.debug {
            val nextTime = Instant.now()
                .plusMillis(delay)
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime()
            "[$name] add work[${work.id}] $times times to queue: ${work}, next in $duration at $nextTime"
        }

        // 投递到队列中的消息结构
        val task = DelayedTask(
            work = work,
            times = times,
            traceContext = mutableMapOf(),
        )

        // 注入 OpenTelemetry 上下文
        injectOpenTelemetryContext(task.traceContext)

        queue.offer(task, duration)
    }

    /**
     * 注入 OpenTelemetry 上下文
     */
    private fun injectOpenTelemetryContext(traceContext: MutableMap<String, String>) {
        openTelemetry.propagators.textMapPropagator.inject(
            Context.current(),
            traceContext,
        ) { carrier, key, value ->
            carrier?.set(key, value)
        }
    }

    /**
     * 提取 OpenTelemetry 上下文
     */
    private fun extractOpenTelemetryContext(traceContext: MutableMap<String, String>): Context {
        val extractedContext = openTelemetry.propagators.textMapPropagator.extract(
            Context.current(),
            traceContext,
            object : TextMapGetter<MutableMap<String, String>> {
                override fun keys(carrier: MutableMap<String, String>): Iterable<String?>? {
                    return carrier.keys
                }

                override fun get(carrier: MutableMap<String, String>?, key: String): String? {
                    return carrier?.get(key)
                }
            }
        )
        return extractedContext
    }

    /**
     * 设置MDC变量
     *
     * [node_id - %X{node_id}] [task_id - %X{task_id}]
     */
    private fun withMDC(work: T, block: (MDC.MDCCloseable) -> Unit) {
        val key = when (work) {
            is NodeWork -> "node_id"
            is TaskWork -> "task_id"
            else -> throw UnsupportedOperationException("Unsupported work type: ${work::class}")
        }
        val value = "${work.id}"
        MDC.putCloseable(key, value).use(block)
    }

    /**
     * 停止调度器
     */
    @PreDestroy
    override fun stop() {
        log.debug { "[$name] Shutting down CoroutineScheduler..." }
        scope.cancel()
        log.debug { "[$name] CoroutineScheduler has been shut down." }
    }
}

/**
 * 延迟任务
 */
data class DelayedTask<T: Work>(
    val work: T,
    val times: Int,
    val traceContext: MutableMap<String, String>,
)
