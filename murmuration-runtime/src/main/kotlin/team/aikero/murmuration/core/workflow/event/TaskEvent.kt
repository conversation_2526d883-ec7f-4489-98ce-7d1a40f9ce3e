package team.aikero.murmuration.core.workflow.event

import org.springframework.context.ApplicationEvent
import team.aikero.murmuration.core.workflow.entity.TaskStatus

/**
 * 任务事件
 */
open class TaskEvent(

    /**
     * 任务
     *
     * TaskWork 对象为不可变对象, 信息不一定最新
     */
    val task: TaskWork,

    /**
     * 任务状态
     *
     * 为了便于使用方判断当前应该是什么状态
     */
    val status: TaskStatus

) : ApplicationEvent(task)

/**
 * 节点准备事件
 */
class TaskPrepareEvent(task: TaskWork) : TaskEvent(task, TaskStatus.PREPARED)

/**
 * 节点运行事件
 */
class TaskRunningEvent(task: TaskWork) : TaskEvent(task, TaskStatus.RUNNING)

/**
 * 节点失败事件
 */
class TaskFailedEvent(task: TaskWork) : TaskEvent(task, TaskStatus.FAILED)

/**
 * 节点完成事件
 */
class TaskCompletedEvent(task: TaskWork) : TaskEvent(task, TaskStatus.COMPLETED)

/**
 * 节点取消事件
 */
class TaskCancelledEvent(task: TaskWork) : TaskEvent(task, TaskStatus.CANCELLED)
