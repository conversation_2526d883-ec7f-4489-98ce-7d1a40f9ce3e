package team.aikero.murmuration.core.scheduler

/**
 * 执行器
 *
 * @param <Payload> 负载类型
 */
interface Executor<T> {

    /**
     * 执行负载
     *
     * @param payload 负载
     */
    fun execute(payload: T): WorkStatus

    /**
     * 通知执行器负载失败
     *
     * @param payload 负载
     * @param ex 异常
     */
    fun notifyFailed(payload: T, ex: Exception)

    /**
     * 通知执行器负载超时
     *
     * @param payload 负载
     */
    fun notifyTimeout(payload: T)
}
