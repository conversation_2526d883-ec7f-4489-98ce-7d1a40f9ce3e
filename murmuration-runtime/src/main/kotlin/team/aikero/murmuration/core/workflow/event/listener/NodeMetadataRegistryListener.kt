package team.aikero.murmuration.core.workflow.event.listener

import org.babyfish.jimmer.sql.kt.KSqlClient
import org.springframework.beans.factory.getBeansOfType
import org.springframework.context.ApplicationListener
import org.springframework.context.event.ContextRefreshedEvent
import org.springframework.core.annotation.AnnotationUtils
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.murmuration.core.annotations.NodeIdentifier
import team.aikero.murmuration.core.workflow.WorkflowNode
import team.aikero.murmuration.core.workflow.entity.NodeMetadata
import team.aikero.murmuration.core.workflow.node.NodeRegistry
import team.aikero.murmuration.core.workflow.node.param.Input
import team.aikero.murmuration.core.workflow.node.param.Output
import team.aikero.murmuration.core.workflow.node.param.Parameter
import team.aikero.murmuration.core.workflow.node.parser.NodeMetadataParser
import kotlin.reflect.KClass

/**
 * 节点定义注册器
 *
 * 在Spring容器启动完成后，扫描所有带 @NodeIdentifier 注解的 NodeWork，
 * 解析并存储到数据库
 *
 * <AUTHOR>
 */
open class NodeMetadataRegistryListener(
    private val sqlClient: KSqlClient
) : ApplicationListener<ContextRefreshedEvent> {

    override fun onApplicationEvent(event: ContextRefreshedEvent) {
        val applicationContext = event.applicationContext

        if (applicationContext.parent != null) {
            // 防止在 Web 环境中父子容器执行多次
            return
        }

        // 获取所有节点
        val nodeMap = applicationContext.getBeansOfType<WorkflowNode<Input, Parameter, Output>>()
        val nodeMetadata = nodeMap.values.mapNotNull {
            try {
                // 检查是否有 NodeIdentifier 注解
                val beanClass = it::class
                val nodeIdentifier = AnnotationUtils.findAnnotation(it.javaClass, NodeIdentifier::class.java) ?: return@mapNotNull null

                // todo 参数类型校验，parameter 必须可无参实例化

                // 注册节点
                registerNode(nodeIdentifier, it)

                // 解析节点元数据
                parseMetadata(it, beanClass, nodeIdentifier)
            } catch (e: Exception) {
                log.error(e) { "处理 ${it::class.simpleName} 时发生错误" }
                return@mapNotNull null
            }
        }

        sqlClient.saveEntities(nodeMetadata)
    }

    /**
     * 解析节点元数据
     */
    private fun parseMetadata(
        node: WorkflowNode<Input, Parameter, Output>,
        beanClass: KClass<out WorkflowNode<Input, Parameter, Output>>,
        nodeIdentifier: NodeIdentifier
    ): NodeMetadata {
        val nodeClassInfo = NodeMetadataParser.parseNodeMetadata(node, beanClass)
        return NodeMetadata {
            this.name = nodeIdentifier.name
            this.type = nodeClassInfo.type
            this.supplier = nodeIdentifier.supplier
            this.ability = nodeIdentifier.ability
            this.input = nodeClassInfo.inputVariables
            this.parameter = nodeClassInfo.parameterVariables
            this.output = nodeClassInfo.outputVariables
        }
    }

    /**
     * 注册节点
     */
    private fun registerNode(
        nodeIdentifier: NodeIdentifier,
        node: WorkflowNode<Input, Parameter, Output>
    ) {
        NodeRegistry.register(nodeIdentifier.supplier, nodeIdentifier.ability, node)
    }

}
