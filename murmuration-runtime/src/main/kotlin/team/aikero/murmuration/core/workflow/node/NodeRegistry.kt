package team.aikero.murmuration.core.workflow.node

import team.aikero.murmuration.core.Ability
import team.aikero.murmuration.core.Supplier
import team.aikero.murmuration.core.workflow.node.param.Input
import team.aikero.murmuration.core.workflow.node.param.Output
import team.aikero.murmuration.core.workflow.node.param.Parameter
import team.aikero.murmuration.core.workflow.WorkflowNode

/**
 * 节点注册表
 *
 * 负责管理和注册工作流节点元数据
 *
 * <AUTHOR>
 */
object NodeRegistry {

    /**
     * 节点注册表
     */
    private val registry = mutableMapOf<Pair<Supplier, Ability>, WorkflowNode<Input, Parameter, Output>>()

    /**
     * 注册节点
     *
     * @param supplier 供应商
     * @param ability 能力
     * @param node 节点实现
     */
    fun register(supplier: Supplier, ability: Ability, node: WorkflowNode<Input, Parameter, Output>) {
        registry[Pair(supplier, ability)] = node
    }

    /**
     * 获取节点
     *
     * @param supplier 供应商
     * @param ability 能力
     * @return 节点实现
     */
    fun get(supplier: Supplier, ability: Ability): WorkflowNode<Input, Parameter, Output> {
        return registry[Pair(supplier, ability)]?: throw IllegalStateException("根据模型[${supplier}]和能力[${ability}]找不到对应的任务处理器")
    }

}
