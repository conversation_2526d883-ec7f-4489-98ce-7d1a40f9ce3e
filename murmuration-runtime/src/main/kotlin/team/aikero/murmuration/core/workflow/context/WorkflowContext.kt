package team.aikero.murmuration.core.workflow.context

import team.aikero.blade.util.json.parseJson
import team.aikero.blade.util.json.toJson
import team.aikero.murmuration.core.workflow.entity.WorkflowInstance

/**
 * 工作流运行时上下文
 *
 * <AUTHOR>
 */
data class WorkflowContext(
    /**
     * 工作流实例
     */
    val workflowInstance: WorkflowInstance,

    /**
     * 存储
     */
    val storage: MutableMap<String, String>,
) {

    /**
     * 设置存储
     *
     * @param key 存储键
     * @param value 存储值
     */
    fun setStorage(key: String, value: Any) {
        storage[key] = value.toJson()
    }

    /**
     * 获取存储
     *
     * @param key 存储键
     * @param T 存储类型
     */
    @Suppress("UNCHECKED_CAST")
    inline fun <reified T> getStorage(key: String): T {
        return storage[key]?.parseJson<T>() ?: throw IllegalStateException("Missing storage: $key")
    }
}
