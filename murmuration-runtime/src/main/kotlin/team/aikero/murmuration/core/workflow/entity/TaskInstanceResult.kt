package team.aikero.murmuration.core.workflow.entity

import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.node.ObjectNode
import org.babyfish.jimmer.sql.Default
import org.babyfish.jimmer.sql.Entity
import org.babyfish.jimmer.sql.ManyToOne
import org.babyfish.jimmer.sql.Serialized
import team.aikero.blade.data.jimmer.entity.CreatedTime
import team.aikero.blade.data.jimmer.entity.LongId

@Entity
interface TaskInstanceResult: LongId, CreatedTime {

    @ManyToOne
    val task: TaskInstance?

    /**
     * 图片URL
     */
    val url: String

    /**
     * 是否验收通过
     */
    @Default("false")
    val passed: <PERSON><PERSON><PERSON>

    @Serialized
    val attributes: JsonNode?
}
