package team.aikero.murmuration.core.workflow.event.listener

import org.springframework.context.event.EventListener
import team.aikero.blade.auth.withSystemUser
import team.aikero.murmuration.core.scheduler.Scheduler
import team.aikero.murmuration.core.workflow.event.TaskPrepareEvent
import team.aikero.murmuration.core.workflow.event.TaskWork

/**
 * 监听任务准备事件
 *
 * <AUTHOR>
 */
class TaskSchedulerListener(val scheduler: Scheduler<TaskWork>) {

    /**
     * 监听任务准备事件
     *
     * @param event
     */
    @EventListener
    fun onTaskPrepare(event: TaskPrepareEvent) = withSystemUser {
        scheduler.submit(event.task)
    }

}
