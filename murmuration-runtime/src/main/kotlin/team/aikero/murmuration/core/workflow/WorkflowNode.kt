package team.aikero.murmuration.core.workflow

import team.aikero.murmuration.core.workflow.context.WorkflowNodeContext
import team.aikero.murmuration.core.workflow.node.NodeResult
import team.aikero.murmuration.core.workflow.node.param.Input
import team.aikero.murmuration.core.workflow.node.param.Output
import team.aikero.murmuration.core.workflow.node.param.Parameter

/**
 * 工作流节点
 *
 * <AUTHOR>
 */
interface WorkflowNode<I : Input, P : Parameter, O : Output> {

    /**
     * 节点类型
     */
    val type: NodeType

    /**
     * 执行任务动作
     *
     * @param context 工作流上下文 发起系统、用户信息、业务 id 等
     * @param input 输入
     * @param parameter 参数
     * @return externalTaskId 外部任务id
     */
    fun execute(context: WorkflowNodeContext, input: I, parameter: P): NodeResult<O>
}
