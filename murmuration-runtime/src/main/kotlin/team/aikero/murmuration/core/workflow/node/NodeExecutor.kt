package team.aikero.murmuration.core.workflow.node

import com.fasterxml.jackson.databind.ObjectMapper
import org.babyfish.jimmer.sql.ast.mutation.AssociatedSaveMode
import org.babyfish.jimmer.sql.ast.mutation.SaveMode
import org.babyfish.jimmer.sql.kt.KSqlClient
import org.babyfish.jimmer.sql.kt.fetcher.newFetcher
import org.springframework.core.GenericTypeResolver
import org.springframework.dao.DataIntegrityViolationException
import org.springframework.transaction.annotation.Transactional
import team.aikero.blade.auth.withSystemUser
import team.aikero.blade.auth.withUser
import team.aikero.blade.core.event.ErrorReportEvent
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.user.entity.CurrentUser
import team.aikero.blade.util.json.toJson
import team.aikero.blade.util.spring.Springs
import team.aikero.murmuration.core.EventPublisher
import team.aikero.murmuration.core.scheduler.Executor
import team.aikero.murmuration.core.scheduler.WorkStatus
import team.aikero.murmuration.core.workflow.WorkflowNode
import team.aikero.murmuration.core.workflow.context.WorkflowContext
import team.aikero.murmuration.core.workflow.context.WorkflowNodeContext
import team.aikero.murmuration.core.workflow.engine.expression.WorkflowExpressionEvaluator
import team.aikero.murmuration.core.workflow.entity.*
import team.aikero.murmuration.core.workflow.event.*
import team.aikero.murmuration.core.workflow.event.NodeWork.Companion.toWork
import team.aikero.murmuration.core.workflow.node.param.GenericData
import team.aikero.murmuration.core.workflow.node.param.Input
import team.aikero.murmuration.core.workflow.node.param.Output
import team.aikero.murmuration.core.workflow.node.param.Parameter
import team.aikero.murmuration.core.workflow.task.TaskResult
import team.aikero.murmuration.metadata.comment
import kotlin.reflect.KClass
import kotlin.reflect.full.createInstance

/**
 * 节点执行器
 *
 * 负责执行工作流节点，处理节点的生命周期和状态转换
 *
 * <AUTHOR>
 */
@Suppress("PARAMETER_NAME_CHANGED_ON_OVERRIDE")
open class NodeExecutor(
    private val sql: KSqlClient,
    private val expressionEvaluator: WorkflowExpressionEvaluator,
    private val eventPublisher: EventPublisher,
    private val objectMapper: ObjectMapper,
) : Executor<NodeWork> {

    /**
     * 执行节点
     */
    @Transactional(rollbackFor = [Exception::class])
    override fun execute(payload: NodeWork): WorkStatus = withSystemUser<WorkStatus> {
        // 1. 加载节点实例及相关信息
        val nodeInstance = loadNodeInstance(payload.id)

        // 1.1 检查工作流状态
        if (!isWorkflowRunning(nodeInstance)) {
            log.info { "节点[${nodeInstance.id}]所在的工作流已结束" }
            return@withSystemUser WorkStatus.FINISHED
        }

        // 1.2 检查节点状态
        if (!isNodeCanExecute(nodeInstance)) {
            log.info { "节点[${nodeInstance.id}]当前状态[${nodeInstance.status}]不可执行" }
            return@withSystemUser WorkStatus.FINISHED
        }

        // 1.3 构建上下文
        val workflowInstance = nodeInstance.workflowInstance ?: throw IllegalStateException("工作流实例不存在")
        val workflowContext = buildWorkflowContext(workflowInstance)
        val nodeContext = buildNodeContext(workflowContext, nodeInstance)

        // 2. 获取节点实现
        val node = NodeRegistry.get(payload.supplier, payload.ability)

        // 3. 构造输入和参数
        val (input, parameter) = parseNodeInputAndParameter(node, nodeInstance, nodeContext)

        // 4. 执行节点
        val user = CurrentUser(
            workflowInstance.creatorId,
            workflowInstance.creatorName,
            "",
            workflowInstance.tenantId,
            workflowInstance.organizationId
        )

        val result = withUser<NodeResult<Output>>(user) {
            node.execute(nodeContext, input, parameter)
        }

        // 5. 处理执行结果
        // 持久化 Workflow storage
        saveStorage(nodeContext)

        when (result) {
            is NodeResult.Running -> {
                this.updateNodeStatus(nodeInstance.id, NodeStatus.RUNNING)
                eventPublisher.publish(NodeRunningEvent(nodeInstance.toWork()))
                // 需要重新调度
                WorkStatus.REDO
            }

            is NodeResult.Reviewing -> {
                this.updateNodeStatus(nodeInstance.id, NodeStatus.REVIEWING)
                eventPublisher.publish(NodeReviewingEvent(nodeInstance.toWork()))
                // 等待人工审核后重新调度
                WorkStatus.FINISHED
            }

            is NodeResult.Completed<*> -> {
                saveNodeOutput(nodeInstance.id, result.value)
                this.updateNodeStatus(nodeInstance.id, NodeStatus.COMPLETED)
                eventPublisher.publish(NodeCompletedEvent(nodeInstance.toWork()))
                WorkStatus.FINISHED
            }

            is NodeResult.Failed -> {
                this.updateNodeStatus(nodeInstance.id, NodeStatus.FAILED) {
                    this.error = result.error?.let { objectMapper.writeValueAsString(it) }
                }
                eventPublisher.publish(NodeFailedEvent(nodeInstance.toWork()))
                WorkStatus.FINISHED
            }
        }
    }

    /**
     * 通知节点执行失败
     */
    @Transactional(rollbackFor = [Exception::class])
    override fun notifyFailed(node: NodeWork, ex: Exception) = withSystemUser {
        // 更新节点状态
        this.updateNodeStatusWithException(node.id, ex)
        // 发布节点失败事件
        eventPublisher.publish(NodeFailedEvent(node))
        // 错误上报
        Springs.publishEvent(ErrorReportEvent(
            source = "NodeExecutor",
            ex = ex,
            context = mapOf(
                "节点实例ID" to node.id,
                "工作流实例ID" to node.workflowId,
                "供应商" to node.supplier.comment,
                "能力" to node.ability.comment,
            ),
        ))
    }

    /**
     * 通知节点执行超时
     */
    @Transactional(rollbackFor = [Exception::class])
    override fun notifyTimeout(payload: NodeWork) = withSystemUser {
        val nodeInstance = sql.findOneById(NodeInstance::class, payload.id)
        this.updateNodeStatus(payload.id, NodeStatus.FAILED) {
            this.error = objectMapper.writeValueAsString(TaskResult.InternalErrorType.TASK_TIMEOUT)
        }
        eventPublisher.publish(NodeFailedEvent(nodeInstance.toWork()))
    }

    /**
     * 加载节点实例
     */
    private fun loadNodeInstance(nodeInstanceId: Long): NodeInstance {
        return sql.findOneById(
            newFetcher(NodeInstance::class).by {
                allScalarFields()
                nodeDefinition {
                    allScalarFields()
                }
                workflowInstance {
                    allScalarFields()
                    workflowDefinition {
                        allScalarFields()
                    }
                    detail {
                        allScalarFields()
                    }
                    nodeInstances {
                        allScalarFields()
                        nodeDefinition {
                            allScalarFields()
                        }
                        detail {
                            allScalarFields()
                        }
                    }
                }
                detail {
                    allScalarFields()
                }
            },
            nodeInstanceId
        )
    }

    /**
     * 构建工作流上下文
     */
    private fun buildWorkflowContext(workflowInstance: WorkflowInstance): WorkflowContext {
        return WorkflowContext(workflowInstance, workflowInstance.detail?.storage ?: mutableMapOf())
    }

    /**
     * 构建节点上下文
     */
    private fun buildNodeContext(workflowContext: WorkflowContext, nodeInstance: NodeInstance): WorkflowNodeContext {
        return WorkflowNodeContext(workflowContext, nodeInstance, nodeInstance.detail?.storage ?: mutableMapOf())
    }

    /**
     * 解析节点输入和参数
     */
    @Suppress("UNCHECKED_CAST")
    private fun parseNodeInputAndParameter(
        node: WorkflowNode<Input, Parameter, Output>,
        nodeInstance: NodeInstance,
        nodeContext: WorkflowNodeContext
    ): Pair<Input, Parameter> {
        try {
            // 1. 解析节点的泛型类型
            val arguments = GenericTypeResolver
                .resolveTypeArguments(node::class.java, WorkflowNode::class.java)
                ?: throw IllegalStateException("解析节点参数类型失败")
            val inputType = arguments[0].kotlin as KClass<Input>
            val parameterType = arguments[1].kotlin as KClass<Parameter>

            // 2. 获取相关数据
            val detail = nodeInstance.detail
            val nodeDefinition = nodeInstance.nodeDefinition

            // 3. 解析输入参数
            val input = parseArguments(detail?.input, nodeDefinition.input, inputType, nodeContext)

            // 4. 解析节点参数
            val parameter = parseArguments(detail?.parameter, nodeDefinition.parameter, parameterType, nodeContext)

            return Pair(input, parameter)
        } catch (e: Exception) {
            throw IllegalArgumentException("解析节点参数类型失败", e)
        }
    }

    /**
     * 解析参数
     */
    private fun <T : Any> parseArguments(
        detailInput: Any?,
        definitionInput: Map<String, String>,
        targetType: KClass<T>,
        nodeContext: WorkflowNodeContext
    ): T {
        // 构建合并后的数据
        val mergedData = mutableMapOf<String, Any?>()

        // 1. 先解析定义中的表达式
        definitionInput.forEach { (key, value) ->
            mergedData[key] = expressionEvaluator.evaluate(value, nodeContext).also {
                log.debug { "node[${nodeContext.node.id}] 表达式计算成功: $key:$value -> $it" }
            }
        }

        // 2. 再添加实际数据（覆盖表达式的值）
        when (detailInput) {
            is GenericData -> mergedData.putAll(detailInput.data)
            null -> {
                // 忽略，保持默认值
            }

            else -> {
                // 尝试将其他类型转换为 Map
                val dataMap = GenericData.from(detailInput).data
                mergedData.putAll(dataMap)
            }
        }

        // 3. 类型转换
        return convertToTargetType(mergedData, targetType)
    }

    /**
     * 转换为目标类型
     */
    @Suppress("UNCHECKED_CAST")
    private fun <T : Any> convertToTargetType(data: Map<String, Any?>, targetType: KClass<T>): T =
        // 如果目标类型是基础接口，使用 GenericData 或 Empty
        when (targetType) {
            // 如果目标类型是 GenericData，直接转换
            GenericData::class -> GenericData(data) as T
            Input::class, Parameter::class -> {
                if (data.isEmpty()) {
                    when (targetType) {
                        Input::class -> Input.Empty as T
                        Parameter::class -> Parameter.Empty as T
                        else -> GenericData(data) as T
                    }
                } else {
                    GenericData(data) as T
                }
            }
            // 如果是具体类型，进行转换
            else -> {
                try {
                    GenericData(data).to(targetType)
                } catch (e: Exception) {
                    runCatching {
                        targetType.createInstance()
                    }.getOrElse {
                        throw IllegalArgumentException("无法将数据转换为目标类型: ${data.toJson()} -> ${targetType.qualifiedName}", e)
                    }
                }
            }
        }

    /**
     * 检查工作流是否正在运行
     */
    private fun isWorkflowRunning(nodeInstance: NodeInstance): Boolean {
        return when (nodeInstance.workflowInstance?.status) {
            WorkflowStatus.START,
            WorkflowStatus.RUNNING,
            // 允许工作流部分失败的情况下继续执行
            WorkflowStatus.FAILED -> true

            WorkflowStatus.COMPLETED,
            WorkflowStatus.PAUSED,
            WorkflowStatus.CANCELLED -> false

            null -> throw IllegalStateException("工作流不存在")
        }
    }

    /**
     * 检查节点是否可以执行
     */
    private fun isNodeCanExecute(nodeInstance: NodeInstance): Boolean {
        return when (nodeInstance.status) {
            NodeStatus.READY,
            NodeStatus.RUNNING,
            NodeStatus.REVIEWED -> true

            NodeStatus.PREPARE,
            NodeStatus.REVIEWING,
            NodeStatus.COMPLETED,
            NodeStatus.FAILED -> false
        }
    }

    /**
     * 保存工作流存储
     *
     * @param nodeContext 节点上下文
     */
    private fun saveStorage(nodeContext: WorkflowNodeContext) {
        sql.save(NodeInstanceDetail {
            this.nodeInstanceId = nodeContext.node.id
            this.storage = nodeContext.storage
        }, SaveMode.UPDATE_ONLY)
    }

    /**
     * 保存节点输出
     */
    private fun saveNodeOutput(nodeInstanceId: Long, output: Output) {
        sql.save(NodeInstanceDetail {
            this.nodeInstanceId = nodeInstanceId
            this.output = GenericData.from(output).data
        }, SaveMode.UPDATE_ONLY)
    }

    /**
     * 更新节点状态
     */
    private fun updateNodeStatus(
        nodeInstanceId: Long,
        status: NodeStatus,
        block: (NodeInstanceDetailDraft.() -> Unit)? = null,
    ) {
        sql.save(NodeInstance {
            this.id = nodeInstanceId
            this.status = status
            if (block != null) {
                detail(block)
            }
        }, SaveMode.UPDATE_ONLY, AssociatedSaveMode.UPDATE)
    }

    /**
     * 根据异常更新节点状态
     */
    private fun updateNodeStatusWithException(nodeInstanceId: Long, ex: Exception) {
        try {
            this.updateNodeStatus(nodeInstanceId, NodeStatus.FAILED) {
                this.error = ex.message
                this.errorStackTrace = ex.stackTraceToString()
            }
        } catch (ex: DataIntegrityViolationException) {
            log.error(ex) { "更新节点[$nodeInstanceId]状态失败: 错误信息超长" }
            this.updateNodeStatus(nodeInstanceId, NodeStatus.FAILED) {
                this.error = ex.message
                this.errorStackTrace = "错误信息超长"
            }
        }
    }
}
