package team.aikero.murmuration.core.workflow.task

/**
 * 异步结果
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
sealed class TaskResult<out T> {
    object Running : TaskResult<Nothing>()
    data class Completed<T>(val value: T) : TaskResult<T>()
    data class Failed(val error: Any?) : TaskResult<Nothing>()

    /**
     * 是否运行中
     */
    fun isRunning() = this is Running

    /**
     * 是否完成
     */
    fun isCompleted() = this is Completed

    /**
     * 是否失败
     */
    fun isFailed(): Boolean = this is Failed

    /**
     * 映射结果
     */
    fun <S> map(fn: (T) -> S): TaskResult<S> = when (this) {
        is Running -> Running
        is Completed -> Completed(fn(value))
        is Failed -> Failed(error)
    }

    /**
     * 内置错误
     */
    enum class InternalErrorType {
        /**
         * 所有任务失败
         */
        ALL_TASK_FAILED,

        /**
         * 任务被取消
         */
        TASK_CANCELED,

        /**
         * 任务超时
         */
        TASK_TIMEOUT,
    }
}
