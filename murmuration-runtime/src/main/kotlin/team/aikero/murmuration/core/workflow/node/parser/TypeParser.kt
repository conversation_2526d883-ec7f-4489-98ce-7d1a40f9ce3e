package team.aikero.murmuration.core.workflow.node.parser

import java.lang.reflect.*

/**
 * 类型解析器
 *
 * 负责递归解析泛型类型，处理复杂的嵌套泛型结构
 *
 * <AUTHOR>
 */
object TypeParser {

    /**
     * 解析类型信息
     *
     * @param type 要解析的类型
     * @return 解析后的类型信息
     */
    fun parseType(type: Type): ParsedType {
        return when (type) {
            is Class<*> -> parseClass(type)
            is ParameterizedType -> parseParameterizedType(type)
            is WildcardType -> parseWildcardType(type)
            is TypeVariable<*> -> parseTypeVariable(type)
            is GenericArrayType -> parseGenericArrayType(type)
            else -> ParsedType(
                type = type,
                name = type.toString(),
                simpleName = type.toString(),
                typeArguments = emptyList()
            )
        }
    }

    /**
     * 解析普通类类型
     */
    private fun parseClass(clazz: Class<*>): ParsedType {
        return ParsedType(
            type = clazz,
            name = clazz.name,
            simpleName = clazz.simpleName,
            typeArguments = emptyList(),
            isArray = clazz.isArray,
            componentType = if (clazz.isArray) parseType(clazz.componentType) else null
        )
    }

    /**
     * 解析参数化类型（泛型类型）
     */
    private fun parseParameterizedType(paramType: ParameterizedType): ParsedType {
        val rawType = paramType.rawType as Class<*>
        val typeArguments = paramType.actualTypeArguments.map { parseType(it) }

        return ParsedType(
            type = paramType,
            name = rawType.name,
            simpleName = rawType.simpleName,
            isGeneric = true,
            typeArguments = typeArguments
        )
    }

    /**
     * 解析通配符类型（? extends, ? super）
     */
    private fun parseWildcardType(wildcardType: WildcardType): ParsedType {
        val upperBounds = wildcardType.upperBounds
        val lowerBounds = wildcardType.lowerBounds

        return when {
            lowerBounds.isNotEmpty() -> {
                // ? super Type
                val boundType = parseType(lowerBounds[0])
                ParsedType(
                    type = wildcardType,
                    name = "? super ${boundType.name}",
                    simpleName = "? super ${boundType.simpleName}",
                    isGeneric = true,
                    isWildcard = true,
                    wildcardBounds = listOf(boundType)
                )
            }
            upperBounds.isNotEmpty() && upperBounds[0] != Any::class.java -> {
                // ? extends Type
                val boundType = parseType(upperBounds[0])
                ParsedType(
                    type = wildcardType,
                    name = "? extends ${boundType.name}",
                    simpleName = "? extends ${boundType.simpleName}",
                    isGeneric = true,
                    isWildcard = true,
                    wildcardBounds = listOf(boundType)
                )
            }
            else -> {
                // ?
                ParsedType(
                    type = wildcardType,
                    name = "?",
                    simpleName = "?",
                    isGeneric = true,
                    isWildcard = true
                )
            }
        }
    }

    /**
     * 解析类型变量（T, E, K, V等）
     */
    private fun parseTypeVariable(typeVar: TypeVariable<*>): ParsedType {
        val bounds = typeVar.bounds.map { parseType(it) }

        return ParsedType(
            type = typeVar,
            name = typeVar.name,
            simpleName = typeVar.name,
            isGeneric = true,
            isTypeVariable = true,
            typeVariableBounds = bounds
        )
    }

    /**
     * 解析泛型数组类型
     */
    private fun parseGenericArrayType(arrayType: GenericArrayType): ParsedType {
        val componentType = parseType(arrayType.genericComponentType)

        return ParsedType(
            type = arrayType,
            name = "${componentType.name}[]",
            simpleName = "${componentType.simpleName}[]",
            isGeneric = componentType.isGeneric,
            isArray = true,
            componentType = componentType
        )
    }

}

/**
 * 解析后的类型信息
 */
data class ParsedType(
    /**
     * 原始类型
     */
    val type: Type,
    /**
     * 原始类型名称（完整类名）
     */
    val name: String,

    /**
     * 简单类型名称
     */
    val simpleName: String,

    /**
     * 是否为泛型类型
     */
    val isGeneric: Boolean = false,

    /**
     * 泛型参数列表
     */
    val typeArguments: List<ParsedType> = emptyList(),

    /**
     * 是否为数组类型
     */
    val isArray: Boolean = false,

    /**
     * 数组组件类型
     */
    val componentType: ParsedType? = null,

    /**
     * 是否为通配符类型
     */
    val isWildcard: Boolean = false,

    /**
     * 通配符边界类型
     */
    val wildcardBounds: List<ParsedType> = emptyList(),

    /**
     * 是否为类型变量
     */
    val isTypeVariable: Boolean = false,

    /**
     * 类型变量边界
     */
    val typeVariableBounds: List<ParsedType> = emptyList()
)
