package team.aikero.murmuration.core.workflow.entity

import org.babyfish.jimmer.sql.*
import team.aikero.blade.data.jimmer.entity.BaseEntity
import team.aikero.murmuration.core.Ability
import team.aikero.murmuration.core.Supplier

/**
 * 任务实例
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Entity
interface TaskInstance : BaseEntity {

    /**
     * 节点实例ID
     */
    @ManyToOne
    val nodeInstance: NodeInstance?

    /**
     * 业务ID
     */
    val bizId: String?

    /**
     * 业务类型
     */
    val bizType: String?

    /**
     * 触发来源
     */
    val triggerSource: TriggerSource

    /**
     * AI模型
     */
    val supplier: Supplier

    /**
     * AI能力
     */
    val ability: Ability

    /**
     * 任务状态
     */
    val status: TaskStatus

    /**
     * 任务分组
     */
    val groupIndex: Long

    @OneToOne(mappedBy = "task")
    val detail: TaskInstanceDetail?

    @OneToMany(mappedBy = "task", orderedProps = [OrderedProp("id")])
    val results: List<TaskInstanceResult>

    @ManyToOne
    @JoinTable(
        name = "ai_box_task_task_instance_mapping",
        joinColumnName = "task_instance_id",
        inverseJoinColumnName = "ai_box_task_id"
    )
    val aiBoxTask: AIBoxTask?
}

/**
 * 触发来源
 */
enum class TriggerSource {
    /**
     * 工作流调度
     */
    WORKFLOW,

    /**
     * API调用
     */
    API,

    /**
     * AI_BOX调用
     */
    AI_BOX
}

/**
 * 任务状态
 */
enum class TaskStatus {
    /**
     * 已就绪
     */
    PREPARED,

    /**
     * 运行中
     */
    RUNNING,

    /**
     * 完成
     */
    COMPLETED,

    /**
     * 失败
     */
    FAILED,

    /**
     * 取消
     */
    CANCELLED;

    fun isFinished() = this == COMPLETED || this == FAILED || this == CANCELLED
}
