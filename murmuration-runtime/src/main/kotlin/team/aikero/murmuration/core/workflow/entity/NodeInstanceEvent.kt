package team.aikero.murmuration.core.workflow.entity

import org.babyfish.jimmer.sql.Entity
import org.babyfish.jimmer.sql.ManyToOne
import team.aikero.blade.data.jimmer.entity.CreatedTime
import team.aikero.blade.data.jimmer.entity.LongId

/**
 * 节点实例事件
 *
 * <AUTHOR>
 * @since 2025-07-20
 */
@Entity
interface NodeInstanceEvent: LongId, CreatedTime {

    /**
     * 节点实例ID
     */
    @ManyToOne
    val nodeInstance: NodeInstance

    /**
     * 节点实例状态
     */
    val status: NodeStatus
}
