package team.aikero.murmuration.core.workflow.node.builtin

import team.aikero.murmuration.core.Ability
import team.aikero.murmuration.core.Supplier
import team.aikero.murmuration.core.annotations.NodeIdentifier
import team.aikero.murmuration.core.workflow.NodeType
import team.aikero.murmuration.core.workflow.WorkflowNode
import team.aikero.murmuration.core.workflow.context.WorkflowNodeContext
import team.aikero.murmuration.core.workflow.node.NodeResult
import team.aikero.murmuration.core.workflow.node.param.GenericData
import team.aikero.murmuration.core.workflow.node.param.Parameter

/**
 * 开始节点
 *
 * <AUTHOR>
 */
@NodeIdentifier(name = "开始节点", supplier = Supplier.MURMURATION, ability = Ability.START)
class StartNode : WorkflowNode<GenericData, Parameter.Empty, GenericData> {
    /**
     * 节点类型
     */
    override val type: NodeType = NodeType.START

    /**
     * 执行任务动作
     *
     * @param context 工作流上下文 发起系统、用户信息、业务 id 等
     * @param input 输入
     * @param parameter 参数
     * @return 节点执行结果
     */
    override fun execute(
        context: WorkflowNodeContext,
        input: GenericData,
        parameter: Parameter.Empty
    ): NodeResult<GenericData> {
        // 记录工作流启动时间及用户信息
        val initData = input
            .set("startTime", System.currentTimeMillis())

        return NodeResult.Completed(initData)
    }
}
