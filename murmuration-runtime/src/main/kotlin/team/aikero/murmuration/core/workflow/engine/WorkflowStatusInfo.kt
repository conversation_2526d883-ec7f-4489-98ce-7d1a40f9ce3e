package team.aikero.murmuration.core.workflow.engine

import team.aikero.murmuration.core.workflow.entity.WorkflowStatus
import java.time.LocalDateTime

/**
 * 工作流状态信息
 */
data class WorkflowStatusInfo(
    /**
     * 工作流实例ID
     */
    val workflowInstanceId: Long,
    /**
     * 工作流编号
     */
    val serialNumber: String,
    /**
     * 工作流状态
     */
    val status: WorkflowStatus,
    /**
     * 节点数量
     */
    val totalNodes: Int,
    /**
     * 已完成节点数量
     */
    val completedNodes: Int,
    /**
     * 失败节点数量
     */
    val failedNodes: Int,
    /**
     * 运行中节点数量
     */
    val runningNodes: Int,
    /**
     * 就绪节点数量
     */
    val readyNodes: Int,
    /**
     * 开始时间
     */
    val startTime: LocalDateTime,
    /**
     * 结束时间
     */
    val endTime: LocalDateTime,
    /**
     * 节点状态列表
     */
    val nodeStatusList: List<NodeStatusInfo>
)
