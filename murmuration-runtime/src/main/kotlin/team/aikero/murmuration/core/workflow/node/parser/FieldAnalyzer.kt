package team.aikero.murmuration.core.workflow.node.parser

import jakarta.validation.constraints.*
import org.slf4j.LoggerFactory
import team.aikero.murmuration.core.annotations.NodeProperties
import java.lang.reflect.Field
import kotlin.reflect.KClass
import kotlin.reflect.KProperty1
import kotlin.reflect.full.declaredMemberProperties
import kotlin.reflect.full.findAnnotation
import kotlin.reflect.jvm.javaField
import kotlin.reflect.jvm.javaType

/**
 * 字段分析器
 *
 * 负责分析类的字段信息，提取字段元数据和注解信息
 *
 * <AUTHOR>
 */
object FieldAnalyzer {

    private val logger = LoggerFactory.getLogger(FieldAnalyzer::class.java)

    /**
     * 分析类的所有字段
     *
     * @param clazz 要分析的类
     * @return 字段信息列表
     */
    fun analyzeFields(clazz: KClass<*>): List<FieldInfo> {
        return try {
            clazz.declaredMemberProperties.mapNotNull { property ->
                analyzeProperty(property)
            }
        } catch (e: Exception) {
            logger.warn("分析类 ${clazz.qualifiedName} 的字段时发生错误", e)
            emptyList()
        }
    }

    /**
     * 分析单个属性
     */
    private fun analyzeProperty(property: KProperty1<out Any, *>): FieldInfo? {
        return try {
            val javaField = property.javaField
            val type = TypeParser.parseType(property.returnType.javaType)

            FieldInfo(
                name = property.name,
                parsedType = type,
                required = isRequired(property, javaField),
                description = extractDescription(property, javaField),
                defaultValue = extractDefaultValue(property, javaField),
                minValue = extractMinValue(property, javaField),
                maxValue = extractMaxValue(property, javaField),
                annotations = extractAnnotations(property, javaField)
            )
        } catch (e: Exception) {
            logger.warn("分析属性 ${property.name} 时发生错误", e)
            null
        }
    }

    /**
     * 判断字段是否必填
     */
    private fun isRequired(property: KProperty1<out Any, *>, javaField: Field?): Boolean {
        // 检查 Kotlin 可空性
        if (property.returnType.isMarkedNullable) {
            return false
        }

        // 检查验证注解
        val hasNotNull = property.findAnnotation<NotNull>() != null ||
                        javaField?.getAnnotation(NotNull::class.java) != null
        val hasNotBlank = property.findAnnotation<NotBlank>() != null ||
                         javaField?.getAnnotation(NotBlank::class.java) != null
        val hasNotEmpty = property.findAnnotation<NotEmpty>() != null ||
                         javaField?.getAnnotation(NotEmpty::class.java) != null

        return !property.returnType.isMarkedNullable || hasNotNull || hasNotBlank || hasNotEmpty
    }

    /**
     * 提取字段描述
     */
    private fun extractDescription(property: KProperty1<out Any, *>, javaField: Field?): String? {
        val nodeProperties = property.findAnnotation<NodeProperties>() ?: javaField?.getAnnotation(NodeProperties::class.java)
        if (nodeProperties != null) {
            return nodeProperties.name
        }

        return null
    }

    /**
     * 提取默认值
     */
    private fun extractDefaultValue(property: KProperty1<out Any, *>, javaField: Field?): String? {
        // todo 暂时不确定是否需要实例化对象后反射获取默认值

        return null
    }

    /**
     * 提取最小值
     */
    private fun extractMinValue(property: KProperty1<out Any, *>, javaField: Field?): Int? {
        val minAnnotation = property.findAnnotation<Min>() ?: javaField?.getAnnotation(Min::class.java)
        if (minAnnotation != null) {
            return minAnnotation.value.toInt()
        }

        val sizeAnnotation = property.findAnnotation<Size>() ?: javaField?.getAnnotation(Size::class.java)
        if (sizeAnnotation != null) {
            return sizeAnnotation.min
        }

        return null
    }

    /**
     * 提取最大值
     */
    private fun extractMaxValue(property: KProperty1<out Any, *>, javaField: Field?): Int? {
        val maxAnnotation = property.findAnnotation<Max>() ?: javaField?.getAnnotation(Max::class.java)
        if (maxAnnotation != null) {
            return maxAnnotation.value.toInt()
        }

        val sizeAnnotation = property.findAnnotation<Size>() ?: javaField?.getAnnotation(Size::class.java)
        if (sizeAnnotation != null) {
            return sizeAnnotation.max
        }

        return null
    }

    /**
     * 提取所有注解信息
     */
    private fun extractAnnotations(property: KProperty1<out Any, *>, javaField: Field?): Map<String, Any> {
        val annotations = mutableMapOf<String, Any>()

        // 从 Kotlin 属性获取注解
        property.annotations.forEach { annotation ->
            annotations[annotation.annotationClass.simpleName!!] = annotation.toString()
        }

        // 从 Java 字段获取注解
        javaField?.annotations?.forEach { annotation ->
            annotations[annotation.annotationClass.simpleName!!] = annotation.toString()
        }

        return annotations
    }
}

/**
 * 字段信息
 */
data class FieldInfo(
    /**
     * 字段名称
     */
    val name: String,

    /**
     * 字段类型
     */
    val parsedType: ParsedType,

    /**
     * 是否必填
     */
    val required: Boolean,

    /**
     * 字段描述
     */
    val description: String?,

    /**
     * 默认值
     */
    val defaultValue: String?,

    /**
     * 最小值
     */
    val minValue: Int?,

    /**
     * 最大值
     */
    val maxValue: Int?,

    /**
     * 注解信息
     */
    val annotations: Map<String, Any>
)
