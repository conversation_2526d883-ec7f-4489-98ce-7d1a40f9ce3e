package team.aikero.murmuration.core.workflow.node

import team.aikero.murmuration.core.workflow.entity.NodeStatus
import team.aikero.murmuration.core.workflow.node.param.Output

/**
 * 节点执行结果
 *
 * <AUTHOR>
 * @since 2025-07-10
 */
sealed class NodeResult<out T: Output>(val status: NodeStatus) {
    /**
     * 复核中(需要人工介入)
     */
    object Reviewing : NodeResult<Nothing>(NodeStatus.REVIEWING)

    /**
     * 运行中(需要被投递回队列重新执行)
     */
    object Running : NodeResult<Nothing>(NodeStatus.RUNNING)

    /**
     * 已完成(携带结果)
     */
    data class Completed<T : Output>(val value: T) : NodeResult<T>(NodeStatus.COMPLETED)

    /**
     * 失败(携带错误)
     */
    data class Failed(val error: Any?) : NodeResult<Nothing>(NodeStatus.FAILED)

    fun isCompleted() = this is Completed
}
