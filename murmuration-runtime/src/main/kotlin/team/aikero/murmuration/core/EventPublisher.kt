package team.aikero.murmuration.core

import org.springframework.context.ApplicationEventPublisher
import org.springframework.transaction.PlatformTransactionManager
import org.springframework.transaction.TransactionDefinition
import org.springframework.transaction.support.TransactionTemplate
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.toJson
import team.aikero.blade.util.spring.Transactions
import team.aikero.murmuration.core.workflow.event.NodeEvent
import team.aikero.murmuration.core.workflow.event.TaskEvent
import team.aikero.murmuration.core.workflow.event.WorkflowEvent

/**
 * 事件发布器
 *
 * <AUTHOR>
 */
class EventPublisher(
    val publisher: ApplicationEventPublisher,
    transactionManager: PlatformTransactionManager,
) {

    private val transactionTemplate = TransactionTemplate(transactionManager).apply {
        propagationBehavior = TransactionDefinition.PROPAGATION_REQUIRES_NEW
    }

    /**
     * 发布工作流事件
     *
     * @param event
     */
    fun publish(event: WorkflowEvent) = Transactions.runAfterCommit {
        log.debug { "publish workflow event workflow[${event.workflow.id}]  ${event.status} ${event.source.toJson()}" }
        doPublish(event)
    }

    /**
     * 发布节点事件
     *
     * @param event
     */
    fun publish(event: NodeEvent)  {
        log.debug { "publish workflow node event node[${event.node.id}] workflow(${event.node.workflowId}) ${event.status} ${event.source.toJson()}" }
        doPublish(event)
    }

    /**
     * 发布任务事件
     *
     * @param event
     */
    fun publish(event: TaskEvent)  {
        log.debug { "publish workflow task event task[${event.task.id}] ${event.status} ${event.source.toJson()}" }
        doPublish(event)
    }

    /**
     * 发布事件
     *
     * 1. 如果有外层事务，则在事务提交后发布
     * 2. 同时要开一个新事务，确保和上一个事务隔离
     */
    private fun doPublish(event: Any) = Transactions.runAfterCommit {
        transactionTemplate.execute {
            publisher.publishEvent(event)
        }
    }
}
