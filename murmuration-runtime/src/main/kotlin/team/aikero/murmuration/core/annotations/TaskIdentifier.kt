package team.aikero.murmuration.core.annotations

import org.springframework.stereotype.Component
import team.aikero.murmuration.core.Ability
import team.aikero.murmuration.core.Supplier

/**
 * 任务标识符
 *
 * <AUTHOR>
 */
@Target(AnnotationTarget.CLASS)
@Retention(AnnotationRetention.RUNTIME)
@MustBeDocumented
@Component
annotation class TaskIdentifier(
    /**
     * 供应商
     *
     * 提供能力的来源，提供者
     */
    val supplier: Supplier,
    /**
     * 能力
     *
     * 业务上定义的 AI 功能或能力类型
     */
    val ability: Ability,
)
