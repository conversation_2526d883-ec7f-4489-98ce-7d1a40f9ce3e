package team.aikero.murmuration.core.workflow.event.listener

import org.springframework.context.event.EventListener
import team.aikero.blade.auth.withSystemUser
import team.aikero.murmuration.core.scheduler.Scheduler
import team.aikero.murmuration.core.workflow.event.NodeReadyEvent
import team.aikero.murmuration.core.workflow.event.NodeWork

/**
 * 节点事件监听器
 *
 * 监听节点事件并触发相应的处理逻辑
 *
 * <AUTHOR>
 */
class NodeSchedulerListener(
    private val scheduler: Scheduler<NodeWork>,
) {

    /**
     * 监听节点就绪事件
     *
     * 当节点进入READY状态时，将其提交到调度器执行
     */
    @EventListener
    fun onNodeReady(event: NodeReadyEvent) = withSystemUser {
        scheduler.submit(event.node)
    }
}
