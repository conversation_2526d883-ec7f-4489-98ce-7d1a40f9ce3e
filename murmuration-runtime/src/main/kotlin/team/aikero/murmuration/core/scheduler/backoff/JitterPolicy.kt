package team.aikero.murmuration.core.scheduler.backoff

import kotlin.random.Random

/**
 * 抖动策略
 */
enum class JitterPolicy {
    /**
     * 全抖动：[0, 计算出的退避时间]
     */
    FULL {
        override fun apply(backoffTime: Long): Long {
            return Random.Default.nextLong(0, backoffTime + 1)
        }
    },

    /**
     * 等比例抖动：[退避时间/2, 退避时间]
     */
    EQUAL {
        override fun apply(backoffTime: Long): Long {
            val half = backoffTime / 2
            return half + Random.Default.nextLong(0, (backoffTime - half) + 1)
        }
    },

    /**
     * 无抖动
     */
    NONE {
        override fun apply(backoffTime: Long): Long {
            return backoffTime
        }
    };

    abstract fun apply(backoffTime: Long): Long
}
