package team.aikero.murmuration.core.workflow.task

import team.aikero.murmuration.core.Ability
import team.aikero.murmuration.core.Supplier
import team.aikero.murmuration.core.annotations.TaskIdentifier
import kotlin.reflect.full.findAnnotation

/**
 * 任务处理器注册中心
 *
 * <AUTHOR>
 */
class TaskHandlerRegistry(handlers: List<TaskHandler<*, *>>) {

    val handlerMap = handlers
        .filter { it::class.findAnnotation<TaskIdentifier>() != null }
        .associateBy { handler ->
            val identifier = handler::class.findAnnotation<TaskIdentifier>()!!
            Pair(identifier.supplier, identifier.ability)
        }

    /**
     * 获取任务处理器
     */
    fun get(supplier: Supplier, ability: Ability): TaskHandler<*, *> {
        return handlerMap[supplier to ability]
            ?: throw IllegalStateException("根据模型[${supplier}]和能力[${ability}]找不到对应的任务处理器")
    }
}
