package team.aikero.murmuration.core.workflow.task

import team.aikero.murmuration.util.resolveGenericType
import kotlin.reflect.KClass

/**
 * 任务处理器
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
interface TaskHandler<Request : Any, Context : Any> {
    val requestType: KClass<Request> get() = resolveGenericType(this, TaskHandler::class, 0)
    val contextType: KClass<Context> get() = resolveGenericType(this, TaskHandler::class, 1)

    /**
     * 创建任务
     *
     * @param request 任务输入
     * @return 任务上下文
     */
    fun create(request: Request): Context

    /**
     * 查询任务
     *
     * @param request 任务输入
     * @param context 任务上下文
     * @return 任务结果(一批)
     */
    fun query(request: Request, context: Context): TaskResult<List<TaskHandlerResult>>
}

/**
 * 任务结果
 */
class TaskHandlerResult(
    /**
     * 图片URL
     */
    val url: String,

    val attributes: Any? = null
) {
    companion object {
        fun image(url: String, attributes: Any? = null) = TaskHandlerResult(url, attributes)
    }
}

/**
 * 任务立即完成异常，用于在create方法中，如果判断任务无需执行，则抛出该异常并携带最终结果，可跳过query这一步
 */
class TaskImmediatelyDoneException(val results: List<TaskHandlerResult>) : Exception()

/**
 * 任务创建可重试异常，用于在create方法中，如果判断任务创建失败，但是可以重试，则抛出该异常
 */
class TaskCreationRetryableException(val reason: Any?): Exception()
