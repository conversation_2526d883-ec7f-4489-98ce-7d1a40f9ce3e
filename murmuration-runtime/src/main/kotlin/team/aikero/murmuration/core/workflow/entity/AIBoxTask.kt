package team.aikero.murmuration.core.workflow.entity

import org.babyfish.jimmer.sql.Default
import org.babyfish.jimmer.sql.Entity
import org.babyfish.jimmer.sql.OneToMany
import org.babyfish.jimmer.sql.Table
import team.aikero.blade.data.jimmer.entity.BaseEntity
import team.aikero.murmuration.common.req.task.ai_box.TaskSource
import java.time.LocalDateTime

@Entity
@Table(
    name = "ai_box_task"
)
interface AIBoxTask : BaseEntity {

    /**
     * 任务编号
     */
    val taskCode: String

    @Default(value = "PREPARED")
    val status: TaskStatus

    val taskSourceId: String?

    /**
     * 任务来源
     */
    val taskSource: TaskSource

    /**
     * 完成时间
     */
    val finishTime: LocalDateTime?

    @OneToMany(mappedBy = "aiBoxTask")
    val taskInstances: List<TaskInstance>

}

