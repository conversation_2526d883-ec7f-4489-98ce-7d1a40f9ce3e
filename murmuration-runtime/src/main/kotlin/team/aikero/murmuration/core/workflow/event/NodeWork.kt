package team.aikero.murmuration.core.workflow.event

import team.aikero.murmuration.core.Ability
import team.aikero.murmuration.core.Supplier
import team.aikero.murmuration.core.scheduler.Work
import team.aikero.murmuration.core.workflow.entity.NodeInstance

/**
 * 节点工作单元
 *
 * 封装节点执行所需的信息，用于节点调度器
 *
 * <AUTHOR>
 */
data class NodeWork(
    /**
     * 节点实例ID
     */
    override val id: Long,

    /**
     * 工作流实例ID
     */
    val workflowId: Long?,

    /**
     * 供应商
     */
    val supplier: Supplier,

    /**
     * 能力
     */
    val ability: Ability
): Work {
    companion object {

        /**
         * 从节点实例创建工作单元
         */
        fun from(nodeInstance: NodeInstance) = NodeWork(nodeInstance.id, nodeInstance.workflowInstance?.id, nodeInstance.supplier, nodeInstance.ability)

        /**
         * 从节点实例创建工作单元
         */
        fun NodeInstance.toWork() = from(this)
    }
}
