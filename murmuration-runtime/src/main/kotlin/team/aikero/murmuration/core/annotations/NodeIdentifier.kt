package team.aikero.murmuration.core.annotations

import org.springframework.stereotype.Component
import team.aikero.murmuration.core.Ability
import team.aikero.murmuration.core.Supplier

/**
 * 节点标识符
 *
 * <AUTHOR>
 */
@Target(AnnotationTarget.CLASS)
@Retention(AnnotationRetention.RUNTIME)
@Component
annotation class NodeIdentifier(
    /**
     * 节点名称
     */
    val name: String = "",

    /**
     * 供应商
     *
     * 提供能力的来源，提供者
     */
    val supplier: Supplier,

    /**
     * 能力
     *
     * 业务上定义的 AI 功能或能力类型
     */
    val ability: Ability,
)
