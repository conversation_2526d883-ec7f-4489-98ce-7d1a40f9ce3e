package team.aikero.murmuration.core.workflow.event

import org.springframework.context.ApplicationEvent
import team.aikero.murmuration.core.workflow.entity.WorkflowStatus

/**
 * 工作流事件
 */
open class WorkflowEvent(
    /**
     * 工作流上下文
     */
    val workflow: WorkflowWork,
    /**
     * 工作流状态
     */
    val status: WorkflowStatus
) : ApplicationEvent(workflow)

/**
 * 工作流启动事件
 */
class WorkflowStartEvent(workflow: WorkflowWork) : WorkflowEvent(workflow, WorkflowStatus.START)

/**
 * 工作流等待事件
 */
class WorkflowWaitingEvent(workflow: WorkflowWork) : WorkflowEvent(workflow, WorkflowStatus.PAUSED)

/**
 * 工作流运行事件
 */
class WorkflowRunningEvent(workflow: WorkflowWork) : WorkflowEvent(workflow, WorkflowStatus.RUNNING)

/**
 * 工作流完成事件
 */
class WorkflowCompletedEvent(workflow: WorkflowWork) : WorkflowEvent(workflow, WorkflowStatus.COMPLETED)

/**
 * 工作流失败事件
 */
class WorkflowFailedEvent(workflow: WorkflowWork) : WorkflowEvent(workflow, WorkflowStatus.FAILED)

/**
 * 工作流取消事件
 */
class WorkflowCancelledEvent(workflow: WorkflowWork) : WorkflowEvent(workflow, WorkflowStatus.CANCELLED)
