package team.aikero.murmuration.core.workflow.entity

import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.node.ObjectNode
import org.babyfish.jimmer.sql.Entity
import org.babyfish.jimmer.sql.Key
import org.babyfish.jimmer.sql.OneToOne
import org.babyfish.jimmer.sql.Serialized
import team.aikero.blade.data.jimmer.entity.LongId

@Entity
interface TaskInstanceDetail: LongId {

    @Key
    @OneToOne(inputNotNull = true)
    val task: TaskInstance?

    /**
     * 任务请求
     */
    @Serialized
    val request: ObjectNode

    /**
     * 任务创建后产生的上下文
     */
    @Serialized
    val context: JsonNode?

    /**
     * 任务存储
     *
     * 在任务创建时希望额外存储的一些数据
     * 1. 在任务完成时可以重新读取该数据做进一步处理
     * 2. 在异步结果回调时会将该数据回传给调用方
     */
    @Serialized
    val storage: JsonNode?

    /**
     * 任务执行错误
     */
    val error: String?

    /**
     * 错误堆栈
     */
    val errorStackTrace: String?
}
