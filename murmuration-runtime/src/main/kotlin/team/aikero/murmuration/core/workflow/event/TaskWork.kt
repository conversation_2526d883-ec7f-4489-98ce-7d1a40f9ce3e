package team.aikero.murmuration.core.workflow.event

import team.aikero.murmuration.core.Ability
import team.aikero.murmuration.core.Supplier
import team.aikero.murmuration.core.scheduler.Work
import team.aikero.murmuration.core.workflow.entity.TaskInstance

/**
 * 任务主体
 *
 * <AUTHOR>
 */
data class TaskWork(
    /**
     * 任务id
     */
    override val id: Long,
    /**
     * 节点实例ID
     */
    val nodeId: Long? = null,
    /**
     * 模型名称
     */
    val supplier: Supplier,
    /**
     * 能力名称
     */
    val ability: Ability,
): Work {
    constructor(taskInstance: TaskInstance) : this(
        id = taskInstance.id,
        nodeId = taskInstance.nodeInstance?.id,
        supplier = taskInstance.supplier,
        ability = taskInstance.ability,
    )
}
