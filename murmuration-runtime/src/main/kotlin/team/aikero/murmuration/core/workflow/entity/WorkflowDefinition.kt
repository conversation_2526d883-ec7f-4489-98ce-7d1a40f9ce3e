package team.aikero.murmuration.core.workflow.entity

import org.babyfish.jimmer.sql.*
import team.aikero.blade.data.jimmer.entity.BaseEntity
import team.aikero.blade.data.jimmer.entity.CreatedTime
import team.aikero.blade.data.jimmer.entity.LongId
import team.aikero.blade.data.jimmer.entity.OrganizationId
import team.aikero.blade.data.jimmer.entity.RevisedTime
import team.aikero.blade.data.jimmer.entity.Reviser
import team.aikero.blade.data.jimmer.entity.TenantId

/**
 * 工作流定义
 *
 * <AUTHOR>
 */
@Entity
interface WorkflowDefinition: LongId, CreatedTime, RevisedTime, Reviser, OrganizationId, TenantId {
    /**
     * 工作流名称
     */
    @Key
    val name: String

    /**
     * 工作流类别
     *
     * [图案开款、衍生开款、款式开款]
     */
    val category: String

    /**
     * 工作流变量
     */
    @Default("[]")
    @Serialized
    val variables: List<Variable>

    /**
     * 工作流节点
     */
    @OneToMany(mappedBy = "workflowDefinition", orderedProps = [OrderedProp("id")])
    val nodeDefinitions: List<NodeDefinition>

    /**
     * 工作流连接
     */
    @OneToMany(mappedBy = "workflowDefinition")
    val edges: List<Edge>

}
