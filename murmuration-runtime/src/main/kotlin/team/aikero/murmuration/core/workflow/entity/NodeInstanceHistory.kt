package team.aikero.murmuration.core.workflow.entity

import org.babyfish.jimmer.sql.Entity
import org.babyfish.jimmer.sql.ManyToOne
import org.babyfish.jimmer.sql.OneToOne
import team.aikero.blade.data.jimmer.entity.CreatedTime
import team.aikero.blade.data.jimmer.entity.LongId

/**
 * 节点实例历史
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Entity
interface NodeInstanceHistory: LongId, CreatedTime {

    /**
     * 节点实例
     */
    @ManyToOne
    val nodeInstance: NodeInstance

    /**
     * 历史数据
     */
    val data: String
}
