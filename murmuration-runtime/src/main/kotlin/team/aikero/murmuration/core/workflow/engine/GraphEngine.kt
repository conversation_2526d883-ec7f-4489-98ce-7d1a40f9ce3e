package team.aikero.murmuration.core.workflow.engine

import team.aikero.murmuration.controller.web.NodeDefinitionArgs

/**
 * 图执行引擎服务接口
 *
 * 提供工作流图执行的标准接口
 *
 * <AUTHOR>
 */
interface GraphEngine {

    /**
     * 启动工作流
     *
     * @param workflowDefinitionId 工作流定义ID
     * @param input 输入参数
     * @return 工作流实例ID
     */
    fun startWorkflow(
        workflowDefinitionId: Long,
        input: Map<String, Any>,
        parameter: Map<String, Any>,
        nodeArgs: List<NodeDefinitionArgs>
    ): Long

    /**
     * 获取工作流状态
     *
     * @param workflowInstanceId 工作流实例ID
     * @return 工作流状态信息
     */
    fun getWorkflowStatus(workflowInstanceId: Long): WorkflowStatusInfo

    /**
     * 取消工作流
     *
     * @param workflowInstanceId 工作流实例ID
     */
    fun cancelWorkflow(workflowInstanceId: Long)

    /**
     * 重试失败的工作流
     *
     * @param workflowInstanceId 工作流实例ID
     */
    fun retryWorkflow(workflowInstanceId: Long)

    /**
     * 重置工作流状态
     *
     * @param workflowInstanceId 工作流实例ID
     */
    fun resetWorkflow(workflowInstanceId: Long)

    /**
     * 暂停工作流
     *
     * @param workflowInstanceId 工作流实例ID
     */
    fun pauseWorkflow(workflowInstanceId: Long)

    /**
     * 恢复暂停的工作流
     *
     * @param workflowInstanceId 工作流实例ID
     */
    fun resumeWorkflow(workflowInstanceId: Long)

    /**
     * 恢复运行中的工作流
     *
     * @param workflowInstanceId 工作流实例ID
     */
    fun recoverWorkflow(workflowInstanceId: Long)
}
