package team.aikero.murmuration.core.workflow.engine

import team.aikero.murmuration.core.Ability
import team.aikero.murmuration.core.Supplier
import team.aikero.murmuration.core.workflow.entity.NodeStatus
import java.time.LocalDateTime

/**
 * 节点状态信息
 */
data class NodeStatusInfo(
    /**
     * 节点实例ID
     */
    val nodeInstanceId: Long,
    /**
     * 节点唯一标识
     */
    val nodeKey: String,
    /**
     * 节点状态
     */
    val status: NodeStatus,
    /**
     * 供应商
     */
    val supplier: Supplier,
    /**
     * 能力
     */
    val ability: Ability,
    /**
     * 开始时间
     */
    val startTime: LocalDateTime,
    /**
     * 结束时间
     */
    val endTime: LocalDateTime,
    /**
     * 错误信息
     */
    val errorMessage: String?
)
