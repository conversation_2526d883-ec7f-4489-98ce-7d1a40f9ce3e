package team.aikero.murmuration.core.workflow.entity

import org.babyfish.jimmer.sql.*
import team.aikero.blade.data.jimmer.entity.LongId

@Entity
interface NodeInstanceDetail : LongId {

    /**
     * 节点实例
     */
    @Key
    @OneToOne
    val nodeInstance: NodeInstance

    /**
     * 节点存储
     */
    @Serialized
    @Default("{}")
    val storage: MutableMap<String, String>

    /**
     * 节点输入参数
     */
    @Serialized
    @Default("{}")
    val input: Map<String, Any?>

    /**
     * 节点参数
     */
    @Serialized
    @Default("{}")
    val parameter: Map<String, Any?>

    /**
     * 节点输出参数
     */
    @Serialized
    val output: Map<String, Any?>?

    /**
     * 节点执行错误
     */
    val error: String?

    /**
     * 错误堆栈
     */
    val errorStackTrace: String?
}
