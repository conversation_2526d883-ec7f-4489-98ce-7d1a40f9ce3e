package team.aikero.murmuration.core.workflow.entity

import org.babyfish.jimmer.sql.*
import team.aikero.blade.data.jimmer.entity.LongId

/**
 * 工作流节点
 *
 * <AUTHOR>
 */
@Entity
interface NodeDefinition : LongId {

    /**
     * 工作流定义
     */
    @Key
    @ManyToOne
    val workflowDefinition: WorkflowDefinition?

    /**
     * 节点元数据
     */
    @ManyToOne
    val nodeMetadata: NodeMetadata

    /**
     * 节点唯一标识
     *
     * 一个工作流下的唯一标识
     */
    @Key
    val nodeKey: String

    /**
     * 是否需要验收
     */
    val review: Boolean

    /**
     * 是否允许跳过
     *
     * 当节点 Input 参数满足时，是否允许跳过前端参数输入步骤。
     */
    val skip: Boolean

    /**
     * 节点输入参数
     */
    @Serialized
    @Default("{}")
    val input: Map<String, String>

    /**
     * 节点参数
     */
    @Serialized
    @Default("{}")
    val parameter: Map<String, String>
}
