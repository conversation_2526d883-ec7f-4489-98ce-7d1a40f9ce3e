package team.aikero.murmuration.core.workflow.event

import org.springframework.context.ApplicationEvent
import team.aikero.murmuration.core.workflow.entity.NodeStatus

/**
 * 工作流节点事件
 */
open class NodeEvent(
    /**
     * 工作流节点事件对象
     */
    val node: NodeWork,
    /**
     * 节点状态
     */
    val status: NodeStatus
) : ApplicationEvent(node)

/**
 * 节点就绪事件
 */
class NodeReadyEvent(node: NodeWork) : NodeEvent(node, NodeStatus.READY)

/**
 * 节点运行事件
 */
class NodeRunningEvent(node: NodeWork) : NodeEvent(node, NodeStatus.RUNNING)

/**
 * 节点审核事件
 */
class NodeReviewingEvent(node: NodeWork) : NodeEvent(node, NodeStatus.REVIEWING)

/**
 * 节点失败事件
 */
class NodeFailedEvent(node: NodeWork) : NodeEvent(node, NodeStatus.FAILED)

/**
 * 节点完成事件
 */
class NodeCompletedEvent(node: NodeWork) : NodeEvent(node, NodeStatus.COMPLETED)
