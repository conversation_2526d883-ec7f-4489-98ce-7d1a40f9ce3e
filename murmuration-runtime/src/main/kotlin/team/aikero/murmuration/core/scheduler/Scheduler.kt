package team.aikero.murmuration.core.scheduler

/**
 * 调度器
 *
 * <AUTHOR>
 */
interface Scheduler<T : Work> {

    /**
     * 提交一个工作
     *
     * @param work 工作
     */
    fun submit(work: T)

    /**
     * 触发一个工作
     *
     * @param work 工作
     */
    fun trigger(work: T): WorkStatus

    /**
     * 列出所有工作
     */
    fun listAll(): List<T>

    /**
     * 启动调度器
     */
    fun start()

    /**
     * 停止调度器
     */
    fun stop()
}
