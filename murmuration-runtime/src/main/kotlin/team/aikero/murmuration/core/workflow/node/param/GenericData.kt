package team.aikero.murmuration.core.workflow.node.param

import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.convertValue
import com.fasterxml.jackson.module.kotlin.readValue
import team.aikero.blade.util.json.Json
import team.aikero.murmuration.core.annotations.NodeProperties
import kotlin.reflect.KClass


/**
 * 通用输入载体
 *
 * <AUTHOR>
 */
@Suppress("UNCHECKED_CAST")
data class GenericData(

    /**
     * 原始数据的JSON表示
     */
    @NodeProperties(name = "数据内容")
    val data: Map<String, Any?>,

    ) : Input, Parameter, Output{

    companion object {
        val objectMapper: ObjectMapper = Json.instance

        /**
         * 从任意对象创建GenericData
         */
        fun <T> from(obj: T): GenericData {
            val dataMap = when (obj) {
                is GenericData -> return obj
                is Map<*, *> -> obj as Map<String, Any?>
                else -> {
                    try {
                        objectMapper.convertValue(obj, object : TypeReference<Map<String, Any?>>() {})
                    } catch (e: Exception) {
                        // 如果转换失败，尝试先序列化为JSON再反序列化
                        val jsonString = objectMapper.writeValueAsString(obj)
                        objectMapper.readValue(jsonString, object : TypeReference<Map<String, Any?>>() {})
                    }
                }
            }
            return GenericData(data = dataMap)
        }

    }

    /**
     * 转换为指定类型的对象
     */
    inline fun <reified T> to(): T {
        return try {
            objectMapper.convertValue<T>(data)
        } catch (e: Exception) {
            // 如果直接转换失败，尝试通过JSON序列化/反序列化
            val jsonString = objectMapper.writeValueAsString(data)
            objectMapper.readValue<T>(jsonString)
        }
    }

    /**
     * 转换为指定类型的对象
     */
    fun <T : Any> to(klass: KClass<T>): T {
        return objectMapper.convertValue(data, klass.java)
    }

    /**
     * 获取指定字段的值
     */
    fun <T> get(key: String): T? {
        return data[key] as? T
    }

    /**
     * 设置字段值，返回新的GenericData
     */
    fun set(key: String, value: Any?): GenericData {
        return this.copy(data = data.toMutableMap().apply { put(key, value) })
    }

    /**
     * 合并其他GenericData
     */
    fun merge(other: GenericData): GenericData {
        return this.copy(data = data + other.data)
    }

}
