package team.aikero.murmuration.config

import com.fasterxml.jackson.databind.ObjectMapper
import org.apache.rocketmq.client.apis.ClientConfiguration
import org.apache.rocketmq.client.apis.ClientServiceProvider
import org.apache.rocketmq.client.apis.StaticSessionCredentialsProvider
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import team.aikero.murmuration.infra.rocketmq.AliyunRocketMQClient
import team.aikero.murmuration.infra.rocketmq.RocketMQClient

@Configuration
@EnableConfigurationProperties(RocketMQProperties::class)
class RocketMQConfiguration {

    @Bean
    fun rocketMQClient(properties: RocketMQProperties, objectMapper: ObjectMapper): RocketMQClient {
        val credentialsProvider = StaticSessionCredentialsProvider(properties.accessKey, properties.secretKey)
        val clientConfiguration = ClientConfiguration
            .newBuilder()
            .setEndpoints(properties.endpoints)
            .setCredentialProvider(credentialsProvider)
            .build()

        val provider = ClientServiceProvider.loadService()

        // producer
        val producer = provider
            .newProducerBuilder()
            .setClientConfiguration(clientConfiguration)
            .build()

        return RocketMQClient(
            producer = producer,
            objectMapper = objectMapper,
        )
    }

    @Bean
    fun aliyunRocketMQClient(properties: RocketMQProperties): AliyunRocketMQClient {
        return AliyunRocketMQClient(
            accessKey = properties.accessKey,
            secretKey = properties.secretKey,
            instanceId = properties.instanceId,
        )
    }
}
