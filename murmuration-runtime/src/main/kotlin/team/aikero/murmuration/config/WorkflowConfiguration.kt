package team.aikero.murmuration.config

import com.fasterxml.jackson.databind.ObjectMapper
import org.babyfish.jimmer.sql.kt.KSqlClient
import org.redisson.api.RedissonClient
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.cloud.context.config.annotation.RefreshScope
import org.springframework.context.ApplicationEventPublisher
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.transaction.PlatformTransactionManager
import team.aikero.murmuration.core.EventPublisher
import team.aikero.murmuration.core.scheduler.Scheduler
import team.aikero.murmuration.core.scheduler.backoff.ExponentialBackoffPolicy
import team.aikero.murmuration.core.scheduler.impl.DelayedQueueScheduler
import team.aikero.murmuration.core.scheduler.impl.RedissonDelayedQueue
import team.aikero.murmuration.core.workflow.engine.expression.WorkflowExpressionEvaluator
import team.aikero.murmuration.core.workflow.event.NodeWork
import team.aikero.murmuration.core.workflow.event.TaskWork
import team.aikero.murmuration.core.workflow.event.listener.NodeMetadataRegistryListener
import team.aikero.murmuration.core.workflow.event.listener.NodeSchedulerListener
import team.aikero.murmuration.core.workflow.event.listener.TaskSchedulerListener
import team.aikero.murmuration.core.workflow.node.NodeExecutor
import team.aikero.murmuration.core.workflow.task.TaskExecutor
import team.aikero.murmuration.core.workflow.task.TaskHandler
import team.aikero.murmuration.core.workflow.task.TaskHandlerRegistry

/**
 * 工作流 Spring 配置
 *
 * 意图使用声明式配置，帮助了解工作流系统各组件组成
 *
 * <AUTHOR>
 */
@EnableConfigurationProperties(MurmurationProperties::class)
@Configuration(proxyBeanMethods = false)
class WorkflowConfiguration() {

    /**
     * 事件发布器
     */
    @Bean
    fun eventPublisher(
        publisher: ApplicationEventPublisher,
        transactionManager: PlatformTransactionManager,
    ): EventPublisher {
        return EventPublisher(publisher, transactionManager)
    }

    /**
     * 工作流表达式计算器
     */
    @Bean
    fun workflowExpressionEvaluator(): WorkflowExpressionEvaluator {
        return WorkflowExpressionEvaluator()
    }

    /**
     * 任务处理器注册中心
     */
    @Bean
    fun taskHandlerRegistry(handlers: List<TaskHandler<*, *>>): TaskHandlerRegistry {
        return TaskHandlerRegistry(handlers)
    }

    /**
     * 任务执行器
     */
    @Bean
    fun murmurationTaskExecutor(
        taskHandlerRegistry: TaskHandlerRegistry,
        sql: KSqlClient,
        objectMapper: ObjectMapper,
        eventPublisher: EventPublisher,
    ): TaskExecutor {
        return TaskExecutor(taskHandlerRegistry, sql, objectMapper, eventPublisher)
    }

    /**
     * 任务调度器
     */
    @Bean
    @RefreshScope
    fun taskScheduler(
        redisson: RedissonClient,
        executor: TaskExecutor,
        properties: MurmurationProperties
    ): Scheduler<TaskWork> {
        val (queueName, workerCount, backoffConfig, maxBackoffTimes) = properties.task
        return DelayedQueueScheduler(
            name = queueName,
            queue = RedissonDelayedQueue(queueName, redisson),
            workerCount = workerCount,
            redisson = redisson,
            executor = executor,
            backoffPolicy = ExponentialBackoffPolicy(
                base = backoffConfig.base,
                initialDelay = backoffConfig.initialDelay.toMillis().toBigDecimal(),
                maxDelay = backoffConfig.maxDelay.toMillis().toBigDecimal(),
                jitterPolicy = backoffConfig.jitterPolicy,
            ),
            maxBackoffTimes = maxBackoffTimes,
        )
    }

    /**
     * 任务调度事件监听器
     */
    @Bean
    fun taskListener(scheduler: Scheduler<TaskWork>): TaskSchedulerListener {
        return TaskSchedulerListener(scheduler)
    }

    /**
     * 节点执行器
     */
    @Bean
    fun murmurationNodeExecutor(
        sql: KSqlClient,
        expressionEvaluator: WorkflowExpressionEvaluator,
        eventPublisher: EventPublisher,
        objectMapper: ObjectMapper,
    ): NodeExecutor {
        return NodeExecutor(sql, expressionEvaluator, eventPublisher, objectMapper)
    }

    /**
     * 节点调度器
     */
    @Bean
    @RefreshScope
    fun murmurationNodeScheduler(
        redisson: RedissonClient,
        executor: NodeExecutor,
        properties: MurmurationProperties
    ): Scheduler<NodeWork> {
        val (queueName, workerCount, backoffConfig, maxBackoffTimes) = properties.node
        return DelayedQueueScheduler(
            name = queueName,
            queue = RedissonDelayedQueue(queueName, redisson),
            workerCount = workerCount,
            redisson = redisson,
            executor = executor,
            backoffPolicy = ExponentialBackoffPolicy(
                base = backoffConfig.base,
                initialDelay = backoffConfig.initialDelay.toMillis().toBigDecimal(),
                maxDelay = backoffConfig.maxDelay.toMillis().toBigDecimal(),
                jitterPolicy = backoffConfig.jitterPolicy,
            ),
            maxBackoffTimes = maxBackoffTimes,
        )
    }

    /**
     * 节点调度事件监听器
     */
    @Bean
    fun nodeSchedulerListener(scheduler: Scheduler<NodeWork>): NodeSchedulerListener {
        return NodeSchedulerListener(scheduler)
    }


    /**
     * 节点元数据注册监听器
     */
    @Bean
    fun nodeMetadataRegistryListener(sql: KSqlClient): NodeMetadataRegistryListener {
        return NodeMetadataRegistryListener(sql)
    }
}
