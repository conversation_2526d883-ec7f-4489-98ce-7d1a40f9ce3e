export team.aikero.murmuration.core.workflow.entity.WorkflowInstance
    -> package team.aikero.murmuration.core.workflow.dto

specification WorkflowInstancePageReq {
    flat(workflowDefinition){
        name
        category
    }
    serialNumber
    mode
    status
    creatorId
    ge(createdTime)
    le(createdTime)
}

WorkflowInstancePageVo {
    id
    serialNumber
    flat(workflowDefinition){
        name
        category
    }
    mode
    status
    createdTime
    creatorId
    creatorName
}

WorkflowInstanceDetailVo {
    id
    serialNumber
    flat(workflowDefinition){
        id as workflowDefinitionId
        name
        category
    }
    mode
    status
    createdTime
    creatorId
    creatorName
    detail {
        input
        parameter
        nodeArgs
        storage
    }
    nodeInstances{
        id
        supplier
        ability
        status
        flat(nodeDefinition){
            flat(nodeMetadata){
                type
            }
            nodeKey
            review
            skip
        }
        flat(detail){
            input
            parameter
            output
            error
        }
    }
}
