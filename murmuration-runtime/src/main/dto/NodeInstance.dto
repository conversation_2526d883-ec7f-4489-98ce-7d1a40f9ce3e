export team.aikero.murmuration.core.workflow.entity.NodeInstance
    -> package team.aikero.murmuration.core.workflow.dto

NodeInstanceDetailVo {
    flat(nodeDefinition){
        nodeKey
        review
        skip
        nodeMetadata {
            name
            supplier
            ability
            type
            input
            parameter
            output
        }
    }
    supplier
    ability
    status
    detail {
        input
        parameter
        output
        error
    }
}
